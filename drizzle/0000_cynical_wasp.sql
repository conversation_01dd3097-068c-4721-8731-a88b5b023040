CREATE TABLE "faceswap_thumbnails" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"imageUrl" text NOT NULL,
	"originalImageUrl" text NOT NULL,
	"personaImageUrl" text NOT NULL,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "generated_thumbnails" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"prompt" text NOT NULL,
	"imageUrl" text NOT NULL,
	"title" text,
	"tags" text[] DEFAULT '{}' NOT NULL,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "recreated_thumbnails" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"imageUrl" text NOT NULL,
	"sourceType" text NOT NULL,
	"prompt" text NOT NULL,
	"personaUsed" boolean DEFAULT false NOT NULL,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"email" text NOT NULL,
	"name" text,
	"avatarUrl" text,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "users_userId_unique" UNIQUE("userId"),
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "faceswap_thumbnails" ADD CONSTRAINT "faceswap_thumbnails_userId_users_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("userId") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "generated_thumbnails" ADD CONSTRAINT "generated_thumbnails_userId_users_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("userId") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "recreated_thumbnails" ADD CONSTRAINT "recreated_thumbnails_userId_users_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("userId") ON DELETE cascade ON UPDATE no action;