CREATE TABLE "personas" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"name" text NOT NULL,
	"finetuneId" text,
	"triggerWord" text DEFAULT 'TOK' NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"generatedImageUrl" text,
	"trainingImagesCount" integer DEFAULT 0 NOT NULL,
	"error" text,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "personas" ADD CONSTRAINT "personas_userId_users_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("userId") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "personas_userId_idx" ON "personas" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "personas_status_idx" ON "personas" USING btree ("status");--> statement-breakpoint
CREATE INDEX "personas_createdAt_idx" ON "personas" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "personas_finetuneId_idx" ON "personas" USING btree ("finetuneId");--> statement-breakpoint
CREATE INDEX "personas_userId_createdAt_idx" ON "personas" USING btree ("userId","createdAt");