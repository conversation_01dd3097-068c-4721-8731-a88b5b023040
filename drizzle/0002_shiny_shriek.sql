CREATE TABLE "title_chat_messages" (
	"id" text PRIMARY KEY NOT NULL,
	"sessionId" text NOT NULL,
	"type" text NOT NULL,
	"content" text NOT NULL,
	"titles" json,
	"status" text,
	"error" text,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "title_chat_sessions" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"title" text,
	"firstPrompt" text NOT NULL,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "title_chat_messages" ADD CONSTRAINT "title_chat_messages_sessionId_title_chat_sessions_id_fk" FOREIGN KEY ("sessionId") REFERENCES "public"."title_chat_sessions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "title_chat_sessions" ADD CONSTRAINT "title_chat_sessions_userId_users_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("userId") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "title_chat_messages_sessionId_idx" ON "title_chat_messages" USING btree ("sessionId");--> statement-breakpoint
CREATE INDEX "title_chat_messages_createdAt_idx" ON "title_chat_messages" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "title_chat_messages_sessionId_createdAt_idx" ON "title_chat_messages" USING btree ("sessionId","createdAt");--> statement-breakpoint
CREATE INDEX "title_chat_sessions_userId_idx" ON "title_chat_sessions" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "title_chat_sessions_createdAt_idx" ON "title_chat_sessions" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "title_chat_sessions_userId_createdAt_idx" ON "title_chat_sessions" USING btree ("userId","createdAt");