CREATE TABLE "background_removals" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"imageUrl" text NOT NULL,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "background_removals" ADD CONSTRAINT "background_removals_userId_users_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("userId") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "background_removals_userId_idx" ON "background_removals" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "background_removals_createdAt_idx" ON "background_removals" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "background_removals_userId_createdAt_idx" ON "background_removals" USING btree ("userId","createdAt");