CREATE INDEX "faceswap_thumbnails_userId_idx" ON "faceswap_thumbnails" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "faceswap_thumbnails_createdAt_idx" ON "faceswap_thumbnails" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "faceswap_thumbnails_userId_createdAt_idx" ON "faceswap_thumbnails" USING btree ("userId","createdAt");--> statement-breakpoint
CREATE INDEX "generated_thumbnails_userId_idx" ON "generated_thumbnails" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "generated_thumbnails_createdAt_idx" ON "generated_thumbnails" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "generated_thumbnails_userId_createdAt_idx" ON "generated_thumbnails" USING btree ("userId","createdAt");--> statement-breakpoint
CREATE INDEX "recreated_thumbnails_userId_idx" ON "recreated_thumbnails" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "recreated_thumbnails_createdAt_idx" ON "recreated_thumbnails" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "recreated_thumbnails_userId_createdAt_idx" ON "recreated_thumbnails" USING btree ("userId","createdAt");--> statement-breakpoint
CREATE INDEX "recreated_thumbnails_sourceType_idx" ON "recreated_thumbnails" USING btree ("sourceType");--> statement-breakpoint
CREATE INDEX "recreated_thumbnails_personaUsed_idx" ON "recreated_thumbnails" USING btree ("personaUsed");