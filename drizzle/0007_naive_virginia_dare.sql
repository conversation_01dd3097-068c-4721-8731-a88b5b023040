CREATE TABLE "edited_images" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"originalImageUrl" text NOT NULL,
	"editedImageUrl" text NOT NULL,
	"sourceType" text NOT NULL,
	"sourceId" text NOT NULL,
	"prompt" text NOT NULL,
	"personaUsed" boolean DEFAULT false NOT NULL,
	"createdAt" timestamp (6) with time zone DEFAULT now() NOT NULL,
	"updatedAt" timestamp (6) with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "edited_images" ADD CONSTRAINT "edited_images_userId_users_userId_fk" FOREIGN KEY ("userId") REFERENCES "public"."users"("userId") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "edited_images_userId_idx" ON "edited_images" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "edited_images_sourceType_idx" ON "edited_images" USING btree ("sourceType");--> statement-breakpoint
CREATE INDEX "edited_images_sourceId_idx" ON "edited_images" USING btree ("sourceId");--> statement-breakpoint
CREATE INDEX "edited_images_createdAt_idx" ON "edited_images" USING btree ("createdAt");--> statement-breakpoint
CREATE INDEX "edited_images_userId_createdAt_idx" ON "edited_images" USING btree ("userId","createdAt");--> statement-breakpoint
CREATE INDEX "edited_images_userId_source_idx" ON "edited_images" USING btree ("userId","sourceType","sourceId");--> statement-breakpoint
ALTER TABLE "faceswap_thumbnails" DROP COLUMN "editedImageUrl";--> statement-breakpoint
ALTER TABLE "generated_thumbnails" DROP COLUMN "editedImageUrl";--> statement-breakpoint
ALTER TABLE "recreated_thumbnails" DROP COLUMN "editedImageUrl";