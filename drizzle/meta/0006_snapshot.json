{"id": "233da345-a736-4447-ad0f-305be683fda6", "prevId": "877cdae4-681c-4a88-a263-cd8a5dced19c", "version": "7", "dialect": "postgresql", "tables": {"public.background_removals": {"name": "background_removals", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"background_removals_userId_idx": {"name": "background_removals_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "background_removals_createdAt_idx": {"name": "background_removals_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "background_removals_userId_createdAt_idx": {"name": "background_removals_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"background_removals_userId_users_userId_fk": {"name": "background_removals_userId_users_userId_fk", "tableFrom": "background_removals", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.faceswap_thumbnails": {"name": "faceswap_thumbnails", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "editedImageUrl": {"name": "editedImageUrl", "type": "text", "primaryKey": false, "notNull": false}, "originalImageUrl": {"name": "originalImageUrl", "type": "text", "primaryKey": false, "notNull": true}, "personaImageUrl": {"name": "personaImageUrl", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"faceswap_thumbnails_userId_idx": {"name": "faceswap_thumbnails_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "faceswap_thumbnails_createdAt_idx": {"name": "faceswap_thumbnails_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "faceswap_thumbnails_userId_createdAt_idx": {"name": "faceswap_thumbnails_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"faceswap_thumbnails_userId_users_userId_fk": {"name": "faceswap_thumbnails_userId_users_userId_fk", "tableFrom": "faceswap_thumbnails", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generated_thumbnails": {"name": "generated_thumbnails", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "editedImageUrl": {"name": "editedImageUrl", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'"}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"generated_thumbnails_userId_idx": {"name": "generated_thumbnails_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_thumbnails_createdAt_idx": {"name": "generated_thumbnails_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_thumbnails_userId_createdAt_idx": {"name": "generated_thumbnails_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"generated_thumbnails_userId_users_userId_fk": {"name": "generated_thumbnails_userId_users_userId_fk", "tableFrom": "generated_thumbnails", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.personas": {"name": "personas", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "finetuneId": {"name": "finetuneId", "type": "text", "primaryKey": false, "notNull": false}, "modelId": {"name": "modelId", "type": "text", "primaryKey": false, "notNull": false}, "triggerWord": {"name": "trigger<PERSON>ord", "type": "text", "primaryKey": false, "notNull": true, "default": "'TOK'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "generatedImageUrl": {"name": "generatedImageUrl", "type": "text", "primaryKey": false, "notNull": false}, "trainingImagesCount": {"name": "trainingImagesCount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"personas_userId_idx": {"name": "personas_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "personas_status_idx": {"name": "personas_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "personas_createdAt_idx": {"name": "personas_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "personas_finetuneId_idx": {"name": "personas_finetuneId_idx", "columns": [{"expression": "finetuneId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "personas_userId_createdAt_idx": {"name": "personas_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"personas_userId_users_userId_fk": {"name": "personas_userId_users_userId_fk", "tableFrom": "personas", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recreated_thumbnails": {"name": "recreated_thumbnails", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "editedImageUrl": {"name": "editedImageUrl", "type": "text", "primaryKey": false, "notNull": false}, "sourceType": {"name": "sourceType", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "personaUsed": {"name": "personaUsed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"recreated_thumbnails_userId_idx": {"name": "recreated_thumbnails_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_createdAt_idx": {"name": "recreated_thumbnails_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_userId_createdAt_idx": {"name": "recreated_thumbnails_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_sourceType_idx": {"name": "recreated_thumbnails_sourceType_idx", "columns": [{"expression": "sourceType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_personaUsed_idx": {"name": "recreated_thumbnails_personaUsed_idx", "columns": [{"expression": "personaUsed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"recreated_thumbnails_userId_users_userId_fk": {"name": "recreated_thumbnails_userId_users_userId_fk", "tableFrom": "recreated_thumbnails", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.title_chat_messages": {"name": "title_chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "sessionId": {"name": "sessionId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "titles": {"name": "titles", "type": "json", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"title_chat_messages_sessionId_idx": {"name": "title_chat_messages_sessionId_idx", "columns": [{"expression": "sessionId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "title_chat_messages_createdAt_idx": {"name": "title_chat_messages_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "title_chat_messages_sessionId_createdAt_idx": {"name": "title_chat_messages_sessionId_createdAt_idx", "columns": [{"expression": "sessionId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"title_chat_messages_sessionId_title_chat_sessions_id_fk": {"name": "title_chat_messages_sessionId_title_chat_sessions_id_fk", "tableFrom": "title_chat_messages", "tableTo": "title_chat_sessions", "columnsFrom": ["sessionId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.title_chat_sessions": {"name": "title_chat_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "firstPrompt": {"name": "firstPrompt", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"title_chat_sessions_userId_idx": {"name": "title_chat_sessions_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "title_chat_sessions_createdAt_idx": {"name": "title_chat_sessions_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "title_chat_sessions_userId_createdAt_idx": {"name": "title_chat_sessions_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"title_chat_sessions_userId_users_userId_fk": {"name": "title_chat_sessions_userId_users_userId_fk", "tableFrom": "title_chat_sessions", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "avatarUrl": {"name": "avatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_userId_unique": {"name": "users_userId_unique", "nullsNotDistinct": false, "columns": ["userId"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}