{"id": "9f61a40c-8640-40f5-86be-b887c4853dca", "prevId": "07095687-2c79-423d-85d9-81941aaf6195", "version": "7", "dialect": "postgresql", "tables": {"public.faceswap_thumbnails": {"name": "faceswap_thumbnails", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "originalImageUrl": {"name": "originalImageUrl", "type": "text", "primaryKey": false, "notNull": true}, "personaImageUrl": {"name": "personaImageUrl", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"faceswap_thumbnails_userId_idx": {"name": "faceswap_thumbnails_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "faceswap_thumbnails_createdAt_idx": {"name": "faceswap_thumbnails_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "faceswap_thumbnails_userId_createdAt_idx": {"name": "faceswap_thumbnails_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"faceswap_thumbnails_userId_users_userId_fk": {"name": "faceswap_thumbnails_userId_users_userId_fk", "tableFrom": "faceswap_thumbnails", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.generated_thumbnails": {"name": "generated_thumbnails", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'"}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"generated_thumbnails_userId_idx": {"name": "generated_thumbnails_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_thumbnails_createdAt_idx": {"name": "generated_thumbnails_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "generated_thumbnails_userId_createdAt_idx": {"name": "generated_thumbnails_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"generated_thumbnails_userId_users_userId_fk": {"name": "generated_thumbnails_userId_users_userId_fk", "tableFrom": "generated_thumbnails", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.recreated_thumbnails": {"name": "recreated_thumbnails", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "sourceType": {"name": "sourceType", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "personaUsed": {"name": "personaUsed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"recreated_thumbnails_userId_idx": {"name": "recreated_thumbnails_userId_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_createdAt_idx": {"name": "recreated_thumbnails_createdAt_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_userId_createdAt_idx": {"name": "recreated_thumbnails_userId_createdAt_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_sourceType_idx": {"name": "recreated_thumbnails_sourceType_idx", "columns": [{"expression": "sourceType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "recreated_thumbnails_personaUsed_idx": {"name": "recreated_thumbnails_personaUsed_idx", "columns": [{"expression": "personaUsed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"recreated_thumbnails_userId_users_userId_fk": {"name": "recreated_thumbnails_userId_users_userId_fk", "tableFrom": "recreated_thumbnails", "tableTo": "users", "columnsFrom": ["userId"], "columnsTo": ["userId"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "avatarUrl": {"name": "avatarUrl", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (6) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_userId_unique": {"name": "users_userId_unique", "nullsNotDistinct": false, "columns": ["userId"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}