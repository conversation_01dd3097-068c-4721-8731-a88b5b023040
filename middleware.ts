import { createServer<PERSON><PERSON>, type <PERSON>ieOptions } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";
import { PUBLIC_ROUTES_SET } from "@/src/lib/routes"; // Import your public routes

// Define static assets paths that should be ignored - use Set for O(1) lookup time
const STATIC_ASSETS_PREFIXES = new Set([
  "/_next/static",
  "/_next/image",
  "/favicon.ico",
  // Add other static prefixes if any, e.g., "/fonts", "/images"
]);

// Regex for static file extensions - compile once for better performance
const STATIC_FILE_EXTENSION_REGEX = /\.(?:svg|png|jpg|jpeg|gif|webp)$/;

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 1. Check for static assets by prefix
  for (const prefix of STATIC_ASSETS_PREFIXES) {
    if (pathname.startsWith(prefix)) {
      return NextResponse.next(); // Serve static asset directly
    }
  }
  // 2. Check for static assets by file extension
  if (STATIC_FILE_EXTENSION_REGEX.test(pathname)) {
    return NextResponse.next(); // Serve static asset directly
  }

  // Initialize response. This will be modified for cookie operations.
  const response = NextResponse.next({
    request: {
      headers: request.headers,
    },
  });

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return request.cookies.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // The request cookies are updated to reflect the change and ensure `getUser()` in the same request sees the updated state.
          request.cookies.set({ name, value, ...options });
          // The response cookies are set to send back to the browser.
          response.cookies.set({ name, value, ...options });
        },
        remove(name: string, options: CookieOptions) {
          request.cookies.set({ name, value: "", ...options });
          response.cookies.set({ name, value: "", ...options });
        },
      },
    }
  );

  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Note: User synchronization is handled in API routes using getCurrentUser()
  // Middleware runs in Edge Runtime and cannot use Prisma/database operations

  const isPublicRoute = PUBLIC_ROUTES_SET.has(pathname);

  // Redirect logic:
  if (!user && !isPublicRoute) {
    // Not logged in and trying to access a protected route
    return NextResponse.redirect(new URL("/auth", request.url));
  }

  if (user) {
    if (pathname.startsWith("/auth")) {
      // Logged in and trying to access an auth page (e.g., /auth, /auth/callback)
      // Allow /auth/callback to proceed for session exchange, otherwise redirect.
      if (pathname !== "/auth/callback") {
        return NextResponse.redirect(new URL("/dashboard", request.url));
      }
    } else if (pathname === "/") {
      // Logged in and on the homepage, redirect to dashboard
      return NextResponse.redirect(new URL("/dashboard", request.url));
    }
  }

  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * This matcher is a bit redundant now with the explicit checks above but can be kept as a broad filter.
     * The explicit checks for static assets at the beginning of the middleware are more performant.
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
    // Ensure API routes are not matched by default if they don't need session handling,
    // or add specific logic for them if they do.
    // Example: '/api/:path*' (if you want middleware on API routes)
  ],
};
