"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Video } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/src/components/ui/avatar";
import type { DeviceSize, VideoThumbnail } from "@/src/types/preview";

// ThumbnailCard Component with enhanced YouTube styling
export function ThumbnailCard({
  thumbnail,
  deviceSize,
  isCustom = false,
}: {
  thumbnail: VideoThumbnail;
  deviceSize: DeviceSize;
  isCustom?: boolean;
}) {
  const [imageError, setImageError] = useState(false);

  const getThumbnailClasses = (deviceSize: DeviceSize) => {
    switch (deviceSize) {
      case "mobile":
        return "aspect-video w-full";
      case "tablet":
        return "aspect-video w-full";
      case "desktop":
      default:
        return "aspect-video w-full";
    }
  };

  const getChannelInitial = (name: string) => {
    return name.charAt(0).toUpperCase();
  };

  const formatViews = (views: string) => {
    return views;
  };

  return (
    <div className="group cursor-pointer">
      {/* Thumbnail Container */}
      <div className="relative mb-3">
        <div
          className={`relative overflow-hidden rounded-xl ${
            getThumbnailClasses(deviceSize)
          } bg-muted group-hover:rounded-lg transition-all duration-200`}
        >
          {thumbnail.url && !imageError ? (
            <Image
              src={thumbnail.url || "/placeholder.svg"}
              alt={thumbnail.title}
              fill
              className="object-cover transition-transform duration-200 group-hover:scale-105"
              sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw"
              priority={isCustom}
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-muted to-muted/80 text-muted-foreground">
              <div className="text-center">
                <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <div className="text-sm font-medium">
                  {isCustom ? "Upload Thumbnail" : "No Preview"}
                </div>
              </div>
            </div>
          )}

          {/* Duration Badge */}
          <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded font-medium backdrop-blur-sm">
            {thumbnail.duration}
          </div>

          {/* Hover Overlay */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 rounded-xl group-hover:rounded-lg" />
        </div>
      </div>

      {/* Video Info */}
      <div className="flex gap-3">
        {/* Channel Avatar */}
        <div className="flex-shrink-0 mt-1">
          <Avatar className="w-9 h-9">
            <AvatarImage 
              src={thumbnail.channelAvatar} 
              alt={`${thumbnail.channelName} avatar`}
            />
            <AvatarFallback className="text-sm font-semibold bg-gradient-to-br from-blue-500 to-purple-600 text-white">
              {getChannelInitial(thumbnail.channelName)}
            </AvatarFallback>
          </Avatar>
        </div>

        {/* Video Details */}
        <div className="flex-1 min-w-0">
          <h3
            className={`text-sm font-medium line-clamp-2 mb-1 leading-5 text-foreground group-hover:text-foreground/80 ${
              isCustom ? "text-blue-400" : ""
            }`}
          >
            {thumbnail.title}
          </h3>

          <div className="text-xs space-y-1 text-muted-foreground">
            <div className="flex items-center gap-1 cursor-pointer transition-colors hover:text-foreground">
              <span>{thumbnail.channelName}</span>
              {thumbnail.verified && (
                <div className="w-3 h-3 bg-gray-500 rounded-full flex items-center justify-center">
                  <div className="w-1.5 h-1.5 bg-white rounded-full" />
                </div>
              )}
            </div>
            <div className="flex items-center gap-1">
              <span>{formatViews(thumbnail.views)}</span>
              <span>•</span>
              <span>{thumbnail.timeAgo}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}