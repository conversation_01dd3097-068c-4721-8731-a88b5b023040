"use client";

import React, { useState } from "react";
import Image from "next/image";
import { Button } from "@/src/components/ui/button";
import { Upload, X } from "lucide-react";
import type { CustomThumbnail } from "@/src/types/preview";

// Enhanced CustomThumbnailUpload Component
export function CustomThumbnailUpload({
  onThumbnailUpload,
  customThumbnail,
}: {
  onThumbnailUpload: (thumbnail: CustomThumbnail | null) => void;
  customThumbnail: CustomThumbnail | null;
}) {
  const [isDragging, setIsDragging] = useState(false);

  const handleFileSelect = (file: File) => {
    if (file && file.type.startsWith("image/")) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        onThumbnailUpload({
          url,
          title: "Your Amazing Video Title",
          channelName: "Your Channel",
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  return (
    <div className="space-y-4">
      {customThumbnail ? (
        <div className="relative w-full aspect-video rounded-lg overflow-hidden border-2 border-blue-500">
          <Image
            src={customThumbnail.url || "/placeholder.svg"}
            alt="Custom thumbnail"
            fill
            className="object-cover"
            sizes="300px"
          />
          <Button
            onClick={() => onThumbnailUpload(null)}
            size="sm"
            variant="ghost"
            className="absolute top-2 right-2 h-8 w-8 p-0 bg-black/70 hover:bg-black/90 rounded-full backdrop-blur-sm"
          >
            <X className="w-4 h-4 text-white" />
          </Button>
          <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded backdrop-blur-sm">
            Custom
          </div>
        </div>
      ) : (
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-all duration-200 ${
            isDragging
              ? "border-blue-500 bg-blue-500/10 scale-105"
              : "border-border hover:border-muted-foreground bg-muted hover:bg-muted/80"
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => document.getElementById("file-input")?.click()}
        >
          <Upload className="w-8 h-8 mx-auto mb-3 text-muted-foreground" />
          <p className="text-sm font-medium mb-1 text-foreground">
            Upload your thumbnail
          </p>
          <p className="text-xs text-muted-foreground">
            Drag & drop or click to browse
          </p>
        </div>
      )}

      <input
        id="file-input"
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
}
