"use client";

import React from "react";
import { ThumbnailCard } from "./ThumbnailCard";
import type {
  DeviceSize,
  CustomThumbnail,
  VideoThumbnail,
} from "@/src/types/preview";

// Enhanced ThumbnailGrid Component
export function ThumbnailGrid({
  deviceSize,
  customThumbnail,
  shuffledThumbnails,
  isLoading = false,
}: {
  deviceSize: DeviceSize;
  customThumbnail: CustomThumbnail | null;
  shuffledThumbnails: VideoThumbnail[];
  isLoading?: boolean;
}) {
  const getGridClasses = (deviceSize: DeviceSize): string => {
    switch (deviceSize) {
      case "mobile":
        return "grid-cols-1 gap-4 max-w-sm mx-auto";
      case "tablet":
        return "grid-cols-2 gap-6 max-w-4xl mx-auto";
      case "desktop":
        return "grid-cols-3 2xl:grid-cols-4 gap-6 max-w-6xl 2xl:max-w-7xl mx-auto";
      default:
        return "grid-cols-3 2xl:grid-cols-4 gap-6 max-w-6xl 2xl:max-w-7xl mx-auto";
    }
  };

  const getContainerPadding = (deviceSize: DeviceSize) => {
    switch (deviceSize) {
      case "mobile":
        return "px-4";
      case "tablet":
        return "px-6";
      case "desktop":
      default:
        return "px-6";
    }
  };

  // Always show empty placeholder at index 1, replace with custom thumbnail if it exists
  const thumbnailsToShow = [...shuffledThumbnails];

  // Always set index 1 to empty placeholder initially
  thumbnailsToShow[1] = {
    id: "placeholder",
    url: "", // Empty URL to show placeholder
    title: "Your Amazing Video Title",
    channelName: "Your Channel",
    views: "0 views",
    duration: "10:30",
    timeAgo: "Just uploaded",
  };

  // If custom thumbnail exists, replace the placeholder
  if (customThumbnail) {
    thumbnailsToShow[1] = {
      id: "custom",
      ...customThumbnail,
      views: "0 views",
      duration: "10:30",
      timeAgo: "Just uploaded",
    };
  }

  const itemsToShow =
    deviceSize === "mobile" ? 40 : deviceSize === "tablet" ? 40 : 40;

  if (isLoading) {
    return (
      <div className={`${getContainerPadding(deviceSize)} py-6`}>
        <div className={`grid ${getGridClasses(deviceSize)}`}>
          {Array.from({ length: itemsToShow }).map((_, index) => (
            <div
              key={`skeleton-${index}`}
              className="aspect-video rounded-xl animate-pulse bg-muted"
            >
              <div className="h-full w-full rounded-xl flex items-center justify-center">
                <div className="text-sm text-muted-foreground">Loading...</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className={`${getContainerPadding(deviceSize)} py-6 overflow-y-auto`}>
      <div className={`grid ${getGridClasses(deviceSize)}`}>
        {thumbnailsToShow.slice(0, itemsToShow).map((thumbnail, index) => (
          <ThumbnailCard
            key={`${thumbnail.id}-${index}`}
            thumbnail={thumbnail}
            deviceSize={deviceSize}
            isCustom={!!(customThumbnail && index === 1)}
          />
        ))}
      </div>
    </div>
  );
}
