import { Card, CardContent } from "@/src/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON><PERSON>rigger,
  TabsContent,
} from "@/src/components/ui/tabs";
import { Input } from "@/src/components/ui/input";
import { Button } from "@/src/components/ui/button";
import {
  Link2,
  Upload,
  CheckCircle,
  Image as ImageIcon,
  X,
} from "lucide-react";
import Image from "next/image";

interface SourceImageSectionProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  url: string;
  setUrl: (url: string) => void;
  thumbnailUrl: string | null;
  uploadedImage: string | null;
  handleExtractThumbnail: () => void;
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleRemoveImage: () => void;
}

export function SourceImageSection({
  activeTab,
  setActiveTab,
  url,
  setUrl,
  thumbnailUrl,
  uploadedImage,
  handleExtractThumbnail,
  handleFileUpload,
  handleRemoveImage,
}: SourceImageSectionProps) {
  const hasImage =
    (activeTab === "link" && thumbnailUrl) ||
    (activeTab === "upload" && uploadedImage);
  const currentImage = activeTab === "link" ? thumbnailUrl : uploadedImage;
  return (
    <Card className="border-0 shadow-lg bg-card/50 backdrop-blur">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <ImageIcon className="w-5 h-5 text-primary" />
          <h2 className="text-xl font-semibold">Source Image</h2>
        </div>

        {hasImage ? (
          // Show image with remove button when image is present
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-600 font-medium">
                  {activeTab === "link"
                    ? "Thumbnail extracted successfully"
                    : "Image uploaded successfully"}
                </span>
              </div>
              <Button
                onClick={handleRemoveImage}
                variant="outline"
                size="sm"
                className="gap-2 text-destructive hover:text-destructive"
              >
                <X className="w-4 h-4" />
                Remove
              </Button>
            </div>

            <div className="relative rounded-xl overflow-hidden border border-border/50 bg-muted/20 aspect-video">
              <Image
                src={currentImage!}
                alt={
                  activeTab === "link"
                    ? "Extracted thumbnail"
                    : "Uploaded image"
                }
                fill
                className="object-cover"
              />
            </div>
          </div>
        ) : (
          // Show input tabs when no image is present
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="link" className="flex items-center gap-2">
                <Link2 className="w-4 h-4" />
                YouTube URL
              </TabsTrigger>
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Upload Image
              </TabsTrigger>
            </TabsList>

            <TabsContent value="link" className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  placeholder="Paste YouTube URL here..."
                  className="bg-background/50 border-0 focus-visible:ring-1"
                />
                <Button
                  onClick={handleExtractThumbnail}
                  disabled={!url.trim()}
                  variant="outline"
                  className="shrink-0"
                >
                  Extract
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="upload" className="space-y-4">
              <div className="relative">
                <label className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-xl cursor-pointer bg-muted/20 border-border/50 hover:bg-muted/30 transition-all duration-200">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-10 h-10 mb-3 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground text-center font-medium">
                      Drop your image here or click to upload
                    </p>
                    <p className="text-xs text-muted-foreground/70 mt-1">
                      PNG, JPG, JPEG, WebP (max 4MB)
                    </p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept=".png,.jpg,.jpeg,.webp,image/png,image/jpeg,image/webp"
                    onChange={handleFileUpload}
                  />
                </label>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}
