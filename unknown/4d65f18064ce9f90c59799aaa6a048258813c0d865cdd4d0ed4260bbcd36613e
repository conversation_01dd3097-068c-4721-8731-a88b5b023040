import React from "react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Progress } from "@/src/components/ui/progress";
import { Badge } from "@/src/components/ui/badge";
import { ScrollArea } from "@/src/components/ui/scroll-area";
import { Separator } from "@/src/components/ui/separator";
import { User, Zap, Eye, Tags } from "lucide-react";

interface AnalysisTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  analysis: {
    faces?: {
      count: number;
      person_details?: Array<{
        name: string;
        confidence: number;
      }>;
    };
    intrigue?: Record<string, number>;
    objects?: Array<{
      name: string;
      confidence: number;
    }>;
    labels?: Array<{
      name: string;
      confidence: number;
    }>;
  };
}

export function AnalysisTabs({
  activeTab,
  setActiveTab,
  analysis,
}: AnalysisTabsProps) {
  const tabs = [
    { id: "faces", label: "Faces", icon: User },
    { id: "intrigue", label: "Intrigue", icon: <PERSON>ap },
    { id: "objects", label: "Objects", icon: Eye },
    { id: "labels", label: "Labels", icon: Tags },
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-2 justify-center">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? "default" : "outline"}
              className="flex items-center gap-2"
              onClick={() => setActiveTab(tab.id)}
            >
              <Icon className="w-4 h-4" />
              <span className="font-medium">{tab.label}</span>
            </Button>
          );
        })}
      </div>

      <Separator />

      {/* Tab Content */}
      <ScrollArea className="h-64">
        <div className="space-y-4">
          {/* Face Analysis Tab */}
          {activeTab === "faces" && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Face Analysis</h3>
              <div className="space-y-3">
                <div className="text-center p-4  rounded-lg">
                  Person {analysis.faces?.count || 0}
                </div>
                {analysis.faces?.person_details &&
                analysis.faces.person_details.length > 0 ? (
                  <div className="space-y-2">
                    {analysis.faces.person_details.map((detail, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-4 bg-muted/20 rounded-lg"
                      >
                        <span className="font-medium capitalize">
                          {detail.name}
                        </span>
                        <div className="flex items-center gap-3 flex-1 mx-4">
                          <Progress
                            value={detail.confidence}
                            className="flex-1"
                          />
                          <Badge
                            variant="outline"
                            className="min-w-[3rem] text-center"
                          >
                            {detail.confidence.toFixed(0)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-muted-foreground">
                    <div className="p-4 bg-muted/20 rounded-lg">
                      No faces detected in this thumbnail
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Intrigue Levels Tab */}
          {activeTab === "intrigue" && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Intrigue Levels</h3>
              {analysis.intrigue &&
              Object.keys(analysis.intrigue).length > 0 ? (
                <div className="space-y-3">
                  {Object.entries(analysis.intrigue).map(([factor, score]) => (
                    <div key={factor} className="p-4 bg-muted/20 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <span className="font-medium capitalize">{factor}</span>
                        <Badge variant="outline">{score}/5</Badge>
                      </div>
                      <Progress value={(score / 5) * 100} className="h-2" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <div className="p-4 bg-muted/20 rounded-lg">
                    No intrigue data available
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Detected Objects Tab */}
          {activeTab === "objects" && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Detected Objects</h3>
              {analysis.objects && analysis.objects.length > 0 ? (
                <div className="space-y-3">
                  {analysis.objects.map((obj, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 bg-muted/20 rounded-lg"
                    >
                      <span className="font-medium capitalize">{obj.name}</span>
                      <div className="flex items-center gap-3 flex-1 mx-4">
                        <Progress value={obj.confidence} className="flex-1" />
                        <Badge
                          variant="outline"
                          className="min-w-[3rem] text-center"
                        >
                          {obj.confidence.toFixed(0)}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <div className="p-4 bg-muted/20 rounded-lg">
                    No objects detected in this thumbnail
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Content Labels Tab */}
          {activeTab === "labels" && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Content Labels</h3>
              {analysis.labels && analysis.labels.length > 0 ? (
                <div className="space-y-3">
                  {analysis.labels.map((label, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-4 bg-muted/20 rounded-lg"
                    >
                      <span className="font-medium capitalize">
                        {label.name}
                      </span>
                      <div className="flex items-center gap-3 flex-1 mx-4">
                        <Progress value={label.confidence} className="flex-1" />
                        <Badge
                          variant="outline"
                          className="min-w-[3rem] text-center"
                        >
                          {label.confidence.toFixed(0)}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 text-muted-foreground">
                  <div className="p-4 bg-muted/20 rounded-lg">
                    No content labels detected in this thumbnail
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
