// store/useDownloadThumbnailStore.ts
import { create } from "zustand";
import {
  extractVideoID,
  buildThumbnailUrl,
  Quality,
} from "@/src/lib/downloadThumbnail";

interface DownloadThumbnailState {
  url: string;
  videoId: string | null;
  thumbnailUrl: string | null;
  error: string | null;
  showVariants?: boolean;

  setUrl: (url: string) => void;
  generate: (quality?: Quality, showVariants?: boolean) => void;
  reset: () => void;
}

export const useDownloadThumbnailStore = create<DownloadThumbnailState>(
  (set, get) => ({
    url: "",
    videoId: null,
    thumbnailUrl: null,
    error: null,
    showVariants: false,

    setUrl: (url) => set({ url, error: null }),

    generate: (quality = "maxresdefault", showVariants = false) => {
      const { url } = get();
      const id = extractVideoID(url);
      if (!id) {
        set({
          error: "Invalid YouTube URL",
          thumbnailUrl: null,
          videoId: null,
        });
        return;
      }
      set({
        videoId: id,
        thumbnailUrl: buildThumbnailUrl(id, quality),
        error: null,
        showVariants,
      });
    },

    reset: () =>
      set({
        url: "",
        videoId: null,
        thumbnailUrl: null,
        error: null,
      }),
  })
);
