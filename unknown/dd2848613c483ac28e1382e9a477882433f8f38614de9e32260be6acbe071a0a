// src/lib/types.ts
export interface GeneratedImage {
  id: string;
  imageUrl: string;
  cloudinaryUrl?: string;
  prompt: string;
  revisedPrompt?: string;
  timestamp: number;
  responseId?: string;
}

export interface ImageGenerationRequest {
  prompt: string;
  previousResponseId?: string;
  quality?: "low" | "medium" | "high" | "auto";
  size?: "1024x1024" | "1536x1024" | "1024x1536" | "auto";
  background?: "transparent" | "opaque" | "auto";
}

export interface ConversationHistory {
  id: string;
  images: GeneratedImage[];
  createdAt: number;
}
