import React from "react";

export const HeroSection = () => {
  return (
    <div className="text-center space-y-4 max-w-4xl mx-auto">
      <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
        YouTube Thumbnail Downloader
      </h1>
      <p className="text-xl text-muted-foreground leading-relaxed">
        Extract & Download YouTube Thumbnails Instantly — In HD, HQ, and All
        Resolutions
      </p>
      <p className="text-muted-foreground max-w-3xl mx-auto">
        Want to download YouTube thumbnails in just one click? You&apos;re in the
        right place. Our free YouTube thumbnail downloader lets you extract
        thumbnails from any YouTube video in multiple quality formats — from
        HD to low-res — without any hassle.
      </p>
    </div>
  );
};