import React from "react";
import { Card, CardContent } from "@/src/components/ui/card";
import {
  Download,
  Image as ImageIcon,
  Check,
  Smartphone,
  Lock,
  Zap,
  Link,
} from "lucide-react";

interface FeaturesSectionProps {
  thumbnailUrl: string | null;
  error: string | null;
}

export const FeaturesSection = ({
  thumbnailUrl,
  error,
}: FeaturesSectionProps) => {
  if (thumbnailUrl || error) return null;

  return (
    <Card className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-200 dark:border-green-800 mt-20">
      <CardContent className="p-8">
        <div className="text-center space-y-6">
          <div className="flex items-center justify-center gap-2">
            <Check className="h-6 w-6 text-green-600" />
            <h3 className="text-2xl font-semibold text-green-900 dark:text-green-100">
              Why Use Our YT Thumbnail Downloader?
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="flex items-start gap-3 text-left">
              <Download className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Download thumbnail from YouTube in Full HD, HQ, SD, and more
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 text-left">
              <Link className="h-5 w-5 text-gray-600 mt-1 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Just paste the YouTube video URL — no sign-up required
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 text-left">
              <ImageIcon className="h-5 w-5 text-orange-600 mt-1 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Get multiple sizes: MaxResDefault, SD, HQ, and Medium quality
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 text-left">
              <Zap className="h-5 w-5 text-pink-600 mt-1 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Super-fast loading and easy-to-use interface
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 text-left">
              <Smartphone className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Works on mobile, tablet, and desktop
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 text-left">
              <Lock className="h-5 w-5 text-orange-600 mt-1 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  100% free, safe, and private
                </p>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
