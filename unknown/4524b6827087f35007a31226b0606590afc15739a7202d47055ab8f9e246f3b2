import 'server-only';
import axios from 'axios';
import path from 'path';
import { promises as fs, existsSync, readFileSync, writeFileSync, mkdirSync } from 'fs';

// Server-side fs operations - only available on server
let fsOperations: {
  fs: typeof fs | null;
  existsSync: typeof existsSync | null;
  readFileSync: typeof readFileSync | null;
  writeFileSync: typeof writeFileSync | null;
  mkdirSync: typeof mkdirSync | null;
} = {
  fs: null,
  existsSync: null,
  readFileSync: null,
  writeFileSync: null,
  mkdirSync: null
};

// Initialize fs operations only on server side
if (typeof window === 'undefined') {
  try {
    fsOperations = {
      fs,
      existsSync,
      readFileSync,
      writeFileSync,
      mkdirSync
    };
  } catch {
    console.warn('fs module not available');
  }
}

interface TrendingVideo {
  title: string;
  description: string;
  videoId: string;
  thumbnails: {
    quality: string;
    url: string;
    width: number;
    height: number;
  }[];
  channelName: string;
  channelId: string;
  channelUrl: string;
  verified: boolean;
  publishedText: string;
  publishedDate: string;
  viewsText: string;
  views: number;
  videoUrl: string;
  duration: number;
  durationText: string;
  isShort: boolean;
}

type TrendingResponse = TrendingVideo[];

interface VideoThumbnail {
  id: string;
  url: string;
  title: string;
  channelName: string;
  channelId: string;
  channelAvatar?: string;
  views: string;
  duration: string;
  timeAgo: string;
  verified?: boolean;
}

interface CachedData {
  lastFetched: number;
  videos: VideoThumbnail[];
}

const CACHE_FILE_PATH = path.join(process.cwd(), 'public', 'trending-videos.json');
const CACHE_DURATION = 8 * 60 * 60 * 1000; // 8 hours in milliseconds
const MAX_REQUESTS_PER_MONTH = 100;
const REQUESTS_FILE_PATH = path.join(process.cwd(), 'public', 'api-requests.json');



// Get the best thumbnail URL (prefer reliable quality)
function getBestThumbnailUrl(thumbnails: TrendingVideo['thumbnails']): string {
  if (thumbnails.length === 0) return '';
  
  // Priority order: maxresdefault > sddefault > high > medium > default (prioritizing working maxresdefault)
  const qualityPriority = ['maxresdefault', 'sddefault', 'high', 'medium', 'default'];
  
  for (const quality of qualityPriority) {
    const thumbnail = thumbnails.find(t => t.quality === quality);
    if (thumbnail) return thumbnail.url;
  }
  
  // Final fallback to highest width if no standard qualities found
  const sorted = thumbnails.sort((a, b) => b.width - a.width);
  return sorted[0].url;
}

// Get channel avatar using YouTube Data API v3
async function getChannelAvatar(channelId: string): Promise<string | null> {
  try {
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/channels?part=snippet&id=${channelId}&key=${process.env.YOUTUBE_API_KEY}`
    );
    
    if (!response.ok) {
      console.error('Failed to fetch channel data:', response.statusText);
      return null;
    }
    
    const data = await response.json();
    
    if (data.items && data.items.length > 0) {
      const thumbnails = data.items[0].snippet.thumbnails;
      // Prefer higher quality thumbnails
      return thumbnails.medium?.url || thumbnails.default?.url || null;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching channel avatar:', error);
    return null;
  }
}

// Transform API response to our VideoThumbnail format
async function transformToVideoThumbnail(video: TrendingVideo): Promise<VideoThumbnail> {
  const channelAvatar = await getChannelAvatar(video.channelId);
  
  return {
    id: video.videoId,
    url: getBestThumbnailUrl(video.thumbnails),
    title: video.title,
    channelName: video.channelName,
    channelId: video.channelId,
    channelAvatar: channelAvatar || undefined,
    views: video.viewsText,
    duration: video.durationText,
    timeAgo: video.publishedText,
    verified: video.verified,
  };
}

// Check if we can make an API request (rate limiting)
function canMakeRequest(): boolean {
  try {
    if (!fsOperations.existsSync || !fsOperations.readFileSync || !fsOperations.existsSync(REQUESTS_FILE_PATH)) {
      return true;
    }
    
    const requestData = JSON.parse(fsOperations.readFileSync(REQUESTS_FILE_PATH, 'utf8'));
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    if (requestData.month !== currentMonth || requestData.year !== currentYear) {
      // Reset counter for new month
      return true;
    }
    
    return requestData.count < MAX_REQUESTS_PER_MONTH;
  } catch (error) {
    console.error('Error checking request limit:', error);
    return true;
  }
}

// Update request counter
function updateRequestCounter(): void {
  try {
    if (!fsOperations.existsSync || !fsOperations.readFileSync || !fsOperations.writeFileSync) {
      return;
    }
    
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    const requestData = { month: currentMonth, year: currentYear, count: 0 };
    
    if (fsOperations.existsSync(REQUESTS_FILE_PATH)) {
      const existingData = JSON.parse(fsOperations.readFileSync(REQUESTS_FILE_PATH, 'utf8'));
      if (existingData.month === currentMonth && existingData.year === currentYear) {
        requestData.count = existingData.count;
      }
    }
    
    requestData.count += 1;
    fsOperations.writeFileSync(REQUESTS_FILE_PATH, JSON.stringify(requestData, null, 2));
  } catch (error) {
    console.error('Error updating request counter:', error);
  }
}

// Load cached data
function loadCachedData(): CachedData | null {
  try {
    if (!fsOperations.existsSync || !fsOperations.readFileSync || !fsOperations.existsSync(CACHE_FILE_PATH)) {
      return null;
    }
    
    const data = JSON.parse(fsOperations.readFileSync(CACHE_FILE_PATH, 'utf8'));
    return data;
  } catch (error) {
    console.error('Error loading cached data:', error);
    return null;
  }
}

// Save data to cache
function saveCachedData(videos: VideoThumbnail[]): void {
  try {
    if (!fsOperations.existsSync || !fsOperations.writeFileSync || !fsOperations.mkdirSync) {
      return;
    }
    
    const cacheData: CachedData = {
      lastFetched: Date.now(),
      videos,
    };
    
    // Ensure public directory exists
    const publicDir = path.join(process.cwd(), 'public');
    if (!fsOperations.existsSync(publicDir)) {
      fsOperations.mkdirSync(publicDir, { recursive: true });
    }
    
    fsOperations.writeFileSync(CACHE_FILE_PATH, JSON.stringify(cacheData, null, 2));
  } catch (error) {
    console.error('Error saving cached data:', error);
  }
}

// Check if cached data is still valid
function isCacheValid(cachedData: CachedData): boolean {
  const now = Date.now();
  return (now - cachedData.lastFetched) < CACHE_DURATION;
}

// Fetch trending videos from RapidAPI
export async function fetchTrendingVideos(): Promise<VideoThumbnail[]> {
  // Check cache first
  const cachedData = loadCachedData();
  if (cachedData && isCacheValid(cachedData)) {
    console.log('Using cached trending videos data');
    return cachedData.videos;
  }
  
  // Check rate limiting
  if (!canMakeRequest()) {
    console.warn('API request limit reached for this month. Using cached data or fallback.');
    if (cachedData) {
      return cachedData.videos;
    }
    // Return empty array if no cached data and rate limited
    return [];
  }
  
  try {
    console.log('Fetching fresh trending videos from API');
    
    const options = {
      method: 'GET',
      url: 'https://youtube-trending.p.rapidapi.com/trending',
      params: {
        country: 'US',
        type: 'gaming'
      },
      headers: {
        'x-rapidapi-key': process.env.RAPID_API_KEY,
        'x-rapidapi-host': 'youtube-trending.p.rapidapi.com'
      }
    };
    
    const response = await axios.request<TrendingResponse>(options);
    updateRequestCounter();
    
    // Transform the data to our format
    const transformedVideos = await Promise.all(
      response.data
        .slice(0, 40) // Limit to 40 videos
        .map(transformToVideoThumbnail)
    );
    
    // Save to cache
    saveCachedData(transformedVideos);
    
    console.log(`Successfully fetched ${transformedVideos.length} trending videos`);
    return transformedVideos;
    
  } catch (error) {
    console.error('Error fetching trending videos:', error);
    
    // Return cached data if available, even if expired
    if (cachedData) {
      console.log('Using expired cached data due to API error');
      return cachedData.videos;
    }
    
    // Return empty array if no cached data and API failed
    return [];
  }
}



// Clear cache data (for testing purposes)
export function clearCacheData(): void {
  try {
    if (!fsOperations.existsSync || !fsOperations.writeFileSync) {
      console.log('File system operations not available');
      return;
    }
    
    if (fsOperations.existsSync(CACHE_FILE_PATH)) {
      fsOperations.writeFileSync(CACHE_FILE_PATH, JSON.stringify({ lastFetched: 0, videos: [] }, null, 2));
      console.log('Cache data cleared');
    }
    if (fsOperations.existsSync(REQUESTS_FILE_PATH)) {
      fsOperations.writeFileSync(REQUESTS_FILE_PATH, JSON.stringify({ month: -1, year: -1, count: 0 }, null, 2));
      console.log('Request counter cleared');
    }
  } catch (error) {
    console.error('Error clearing cache data:', error);
  }
}

// Server-side function for API route
export async function getTrendingVideosServer(): Promise<VideoThumbnail[]> {
  const trendingVideos = await fetchTrendingVideos();
  
  // If no trending videos available, return fallback data
  if (trendingVideos.length === 0) {
    console.log('No trending videos available, using fallback data');
    return getFallbackThumbnails();
  }
  
  return trendingVideos;
}

// Fallback data loader - loads from the existing cached data or returns empty array
function getFallbackThumbnails(): VideoThumbnail[] {
  try {
    // Try to load from existing cache file first
    const cachedData = loadCachedData();
    if (cachedData && cachedData.videos.length > 0) {
      console.log('Using existing cached data as fallback');
      return cachedData.videos;
    }
    
    console.log('No cached data available, returning empty array');
    return [];
  } catch (error) {
    console.error('Error loading fallback data:', error);
    return [];
  }
}