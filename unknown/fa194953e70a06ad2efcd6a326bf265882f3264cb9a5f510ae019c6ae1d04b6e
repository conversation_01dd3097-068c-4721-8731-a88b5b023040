import { RefreshCw } from "lucide-react";

export function RecreateHeader() {
  return (
    <div className="text-center mb-8">
      <div className="flex items-center justify-center gap-2 mb-4">
        <div className="p-2 rounded-lg bg-primary/10">
          <RefreshCw className="w-6 h-6 text-primary" />
        </div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          Thumbnail Recreate
        </h1>
      </div>
      <p className="text-muted-foreground max-w-2xl mx-auto">
        Transform your existing thumbnails with AI. Upload an image or extract from YouTube, 
        then describe your vision to recreate it with stunning results.
      </p>
    </div>
  );
}