// lib/downloadThumbnail.ts
export function extractVideoID(url: string): string | null {
  const regExp =
    /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#\&\?]*).*/;
  const match = url.match(regExp);
  return match && match[7].length === 11 ? match[7] : null;
}

export type Quality = "maxresdefault" | "sddefault" | "hqdefault" | "mqdefault";

/**
 * Build a YouTube thumbnail URL given an ID and desired quality.
 */
export function buildThumbnailUrl(
  videoId: string,
  quality: Quality = "maxresdefault"
): string {
  return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
}
