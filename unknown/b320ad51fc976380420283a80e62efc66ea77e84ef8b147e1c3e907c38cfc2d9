"use client";

import { useAuthStore } from "@/src/store/authStore";
import { Button } from "@/src/components/ui/button"; // Import the Button component

export default function LogoutButton() {
  const { signOut, isLoading } = useAuthStore();

  const handleLogout = async () => {
    await signOut();
  };

  return (
    <Button variant="destructive" onClick={handleLogout} disabled={isLoading}>
      {isLoading ? "Logging out..." : "Logout"}
    </Button>
  );
}
