import React from "react";
import { Quality } from "@/src/lib/downloadThumbnail";
import { PrimaryThumbnailCard } from "./PrimaryThumbnailCard";
import { ThumbnailGrid } from "./ThumbnailGrid";

interface ThumbnailResultsProps {
  thumbnailUrl: string | null;
  videoId: string | null;
  handleDownload: (quality: Quality) => void;
  isDownloading: (quality: Quality) => boolean;
  isDownloaded: (quality: Quality) => boolean;
}

export const ThumbnailResults = ({
  thumbnailUrl,
  videoId,
  handleDownload,
  isDownloading,
  isDownloaded,
}: ThumbnailResultsProps) => {
  if (!thumbnailUrl || !videoId) return null;

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h3 className="text-xl font-semibold">Choose Your Preferred Quality</h3>
        <p className="text-muted-foreground">
          Select and download the thumbnail size that works best for your needs
        </p>
      </div>

      <PrimaryThumbnailCard
        videoId={videoId}
        handleDownload={handleDownload}
        isDownloading={isDownloading}
        isDownloaded={isDownloaded}
      />

      <ThumbnailGrid
        videoId={videoId}
        handleDownload={handleDownload}
        isDownloading={isDownloading}
        isDownloaded={isDownloaded}
      />
    </div>
  );
};
