// components/tools/ToolCard.tsx

"use client";
import { useRouter } from "next/navigation";
import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import Image from "next/image";
import type { Tool } from "@/src/constants/tools";

export function ToolCard({ tool }: { tool: Tool }) {
  const router = useRouter();
  const isExtra =
    tool.title.includes("Preview") || tool.title.includes("Rating");

  return (
    <Card
      onClick={() => router.push(tool.url)}
      className="cursor-pointer hover:shadow-lg transition"
      role="button"
      aria-label={tool.title}
    >
      <div className="relative w-full aspect-video rounded-t-lg overflow-hidden">
        <Image src={tool.image} alt="" fill className="object-cover" />
      </div>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">{tool.title}</h3>
          {isExtra && <Badge variant="outline">Extra</Badge>}
        </div>
        <p className="text-sm text-gray-600 mt-1">{tool.description}</p>
      </CardContent>
    </Card>
  );
}
