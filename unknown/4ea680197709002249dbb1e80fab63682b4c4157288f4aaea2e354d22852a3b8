/**
 * Utility functions for Konva operations and canvas management
 */
import Konva from 'konva';

/**
 * Create a new Konva stage with specified dimensions
 */
export const createStage = (
  container: HTMLDivElement,
  width: number,
  height: number
): Konva.Stage => {
  return new Konva.Stage({
    container,
    width,
    height,
  });
};

/**
 * Create a new Konva layer
 */
export const createLayer = (): Konva.Layer => {
  return new Konva.Layer();
};

/**
 * Create a Konva image node from HTMLImageElement
 */
export const createKonvaImage = (
  image: HTMLImageElement,
  x: number = 0,
  y: number = 0,
  width?: number,
  height?: number
): Konva.Image => {
  return new Konva.Image({
    image,
    x,
    y,
    width: width || image.width,
    height: height || image.height,
  });
};

/**
 * Create a Konva line for drawing strokes
 */
export const createKonvaLine = (
  points: number[],
  stroke: string = '#ffffff',
  strokeWidth: number = 5,
  globalCompositeOperation: GlobalCompositeOperation = 'source-over'
): Konva.Line => {
  return new Konva.Line({
    points,
    stroke,
    strokeWidth,
    globalCompositeOperation,
    lineCap: 'round',
    lineJoin: 'round',
  });
};

/**
 * Create a Konva rectangle
 */
export const createKonvaRect = (
  x: number,
  y: number,
  width: number,
  height: number,
  fill: string = 'rgba(255, 255, 255, 0.5)',
  stroke: string = '#ffffff',
  strokeWidth: number = 2
): Konva.Rect => {
  return new Konva.Rect({
    x,
    y,
    width,
    height,
    fill,
    stroke,
    strokeWidth,
  });
};

/**
 * Convert Konva stage to data URL
 */
export const stageToDataURL = (
  stage: Konva.Stage,
  mimeType: string = 'image/png',
  quality: number = 1.0
): string => {
  return stage.toDataURL({
    mimeType,
    quality,
  });
};

/**
 * Convert Konva layer to data URL
 */
export const layerToDataURL = (
  layer: Konva.Layer,
  mimeType: string = 'image/png',
  quality: number = 1.0
): string => {
  return layer.toDataURL({
    mimeType,
    quality,
  });
};

/**
 * Get pointer position relative to stage
 */
export const getPointerPosition = (stage: Konva.Stage): { x: number; y: number } | null => {
  return stage.getPointerPosition();
};

/**
 * Clear all children from a layer
 */
export const clearLayer = (layer: Konva.Layer): void => {
  layer.destroyChildren();
  layer.draw();
};

/**
 * Add shape to layer and redraw
 */
export const addShapeToLayer = (layer: Konva.Layer, shape: Konva.Shape): void => {
  layer.add(shape);
  layer.draw();
};

/**
 * Remove shape from layer and redraw
 */
export const removeShapeFromLayer = (layer: Konva.Layer, shape: Konva.Shape): void => {
  shape.destroy();
  layer.draw();
};

/**
 * Set layer opacity
 */
export const setLayerOpacity = (layer: Konva.Layer, opacity: number): void => {
  layer.opacity(opacity);
  layer.draw();
};

/**
 * Resize stage and all its layers
 */
export const resizeStage = (
  stage: Konva.Stage,
  width: number,
  height: number
): void => {
  stage.width(width);
  stage.height(height);
  stage.draw();
};

/**
 * Create a mask layer with semi-transparent overlay
 */
export const createMaskOverlay = (
  width: number,
  height: number,
  opacity: number = 0.5
): Konva.Rect => {
  return new Konva.Rect({
    x: 0,
    y: 0,
    width,
    height,
    fill: 'rgba(0, 0, 0, ' + opacity + ')',
    listening: false,
  });
};

/**
 * Create a selection rectangle for rectangle tool
 */
export const createSelectionRect = (
  startX: number,
  startY: number,
  endX: number,
  endY: number,
  stroke: string = '#ffffff',
  strokeWidth: number = 2,
  dash: number[] = [5, 5]
): Konva.Rect => {
  const x = Math.min(startX, endX);
  const y = Math.min(startY, endY);
  const width = Math.abs(endX - startX);
  const height = Math.abs(endY - startY);
  
  return new Konva.Rect({
    x,
    y,
    width,
    height,
    stroke,
    strokeWidth,
    dash,
    fill: 'rgba(255, 255, 255, 0.2)',
  });
};

/**
 * Convert rectangle selection to mask points
 */
export const rectToMaskPoints = (
  x: number,
  y: number,
  width: number,
  height: number,
  brushSize: number = 10
): number[] => {
  const points: number[] = [];
  const step = Math.max(1, Math.floor(brushSize / 2));
  
  // Top edge
  for (let i = x; i <= x + width; i += step) {
    points.push(i, y);
  }
  
  // Right edge
  for (let i = y; i <= y + height; i += step) {
    points.push(x + width, i);
  }
  
  // Bottom edge
  for (let i = x + width; i >= x; i -= step) {
    points.push(i, y + height);
  }
  
  // Left edge
  for (let i = y + height; i >= y; i -= step) {
    points.push(x, i);
  }
  
  return points;
};

/**
 * Fill rectangle area with mask points
 */
export const fillRectWithMask = (
  x: number,
  y: number,
  width: number,
  height: number,
  brushSize: number = 10
): number[] => {
  const points: number[] = [];
  const step = Math.max(1, Math.floor(brushSize / 3));
  
  for (let i = x; i <= x + width; i += step) {
    for (let j = y; j <= y + height; j += step) {
      points.push(i, j);
    }
  }
  
  return points;
};

/**
 * Destroy stage and clean up resources
 */
export const destroyStage = (stage: Konva.Stage): void => {
  stage.destroy();
};

/**
 * Get stage as canvas element
 */
export const stageToCanvas = (stage: Konva.Stage): HTMLCanvasElement => {
  return stage.toCanvas();
};

/**
 * Clone a Konva node
 */
export const cloneNode = <T extends Konva.Node>(node: T): T => {
  return node.clone();
};

/**
 * Set stage scale for zoom functionality
 */
export const setStageScale = (
  stage: Konva.Stage,
  scaleX: number,
  scaleY: number = scaleX
): void => {
  stage.scaleX(scaleX);
  stage.scaleY(scaleY);
  stage.draw();
};

/**
 * Center stage on specific coordinates
 */
export const centerStageOn = (
  stage: Konva.Stage,
  x: number,
  y: number
): void => {
  const scale = stage.scaleX();
  stage.x(-x * scale + stage.width() / 2);
  stage.y(-y * scale + stage.height() / 2);
  stage.draw();
};