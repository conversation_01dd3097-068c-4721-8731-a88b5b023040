"use client";

import React from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { <PERSON><PERSON>, Sparkles, Wand2 } from "lucide-react";

interface EnhancePromptButtonsProps {
  onMicClick?: () => void;
  onEnhanceClick?: () => void;
  onStylesClick?: () => void;
  disabled?: boolean;
}

export function EnhancePromptButtons({
  onMicClick,
  onEnhanceClick,
  onStylesClick,
  disabled = false,
}: EnhancePromptButtonsProps) {
  return (
    <div className="flex items-center gap-2 mt-2">
      <Button
        variant="outline"
        size="sm"
        onClick={onEnhanceClick}
        disabled={disabled}
        className="gap-2 bg-background/50 text-xs sm:text-sm"
      >
        <Sparkles className="w-3 h-3 sm:w-4 sm:h-4" />
        <span>Enhance Prompt</span>
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={onStylesClick}
        disabled={disabled}
        className="gap-2 bg-background/50 text-xs sm:text-sm"
      >
        <Wand2 className="w-3 h-3 sm:w-4 sm:h-4" />
        <span>Styles</span>
      </Button>

      <Button
        variant="outline"
        size="sm"
        onClick={onMicClick}
        disabled={disabled}
        className="gap-2 bg-background/50 text-xs sm:text-sm"
      >
        <Mic className="w-3 h-3 sm:w-4 sm:h-4" />
        <span className="hidden sm:inline">Voice</span>
      </Button>
    </div>
  );
}
