import { create } from "zustand";

export type FaceSwapStatus = "idle" | "generating" | "error" | "success";

export interface FaceSwapMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  imageUrl?: string;
  timestamp: Date;
  status?: FaceSwapStatus;
  error?: string;
}

interface FaceSwapState {
  messages: FaceSwapMessage[];
  isGenerating: boolean;
  uploadedImage: string | null;
  selectedPersonaImage: string | null;

  addMessage: (message: Omit<FaceSwapMessage, "id" | "timestamp">) => string;
  updateMessage: (id: string, updates: Partial<FaceSwapMessage>) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  clearMessages: () => void;
  setUploadedImage: (image: string | null) => void;
  setSelectedPersonaImage: (image: string | null) => void;
}

export const useFaceSwapStore = create<FaceSwapState>((set, get) => ({
  messages: [],
  isGenerating: false,
  uploadedImage: null,
  selectedPersonaImage: null,

  addMessage: (message) => {
    const newMessage: FaceSwapMessage = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    };
    set((state) => ({ messages: [...state.messages, newMessage] }));
    return newMessage.id;
  },

  updateMessage: (id, updates) => {
    set((state) => ({
      messages: state.messages.map((msg) =>
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }));
  },

  setIsGenerating: (isGenerating) => set({ isGenerating }),

  clearMessages: () => set({ messages: [] }),

  setUploadedImage: (image) => set({ uploadedImage: image }),
  setSelectedPersonaImage: (image) => set({ selectedPersonaImage: image }),
}));
