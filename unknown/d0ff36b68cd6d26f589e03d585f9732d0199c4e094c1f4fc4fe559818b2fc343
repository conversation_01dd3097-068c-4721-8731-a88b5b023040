import { type NextRequest, NextResponse } from "next/server";
import { openai } from "@/src/lib/openai";
import sharp from "sharp";

export const dynamic = "force-dynamic";
export const maxDuration = 300;

interface AnalyzeRequest {
  image: string;
  title: string;
  fileName: string;
  fileSize: number;
  dimensions: { width: number; height: number };
}

export async function POST(request: NextRequest) {
  try {
    const { image, title, fileName, fileSize, dimensions } =
      (await request.json()) as AnalyzeRequest;

    if (!image || !title) {
      return NextResponse.json(
        { success: false, error: "Image and title are required" },
        { status: 400 }
      );
    }

    // Extract image metadata using Sharp for server-side validation
    const imageBuffer = Buffer.from(image.split(",")[1], "base64");
    let serverDimensions = dimensions;

    try {
      const metadata = await sharp(imageBuffer).metadata();
      if (metadata.width && metadata.height) {
        serverDimensions = { width: metadata.width, height: metadata.height };
      }
    } catch (error) {
      console.error("Sharp metadata extraction failed:", error);
      // Continue with client-provided dimensions if <PERSON> fails
      console.log("Using client-provided dimensions:", dimensions);
    }

    // Ensure we have valid dimensions, fallback to client dimensions if needed
    const actualWidth = serverDimensions?.width || dimensions?.width || 0;
    const actualHeight = serverDimensions?.height || dimensions?.height || 0;
    const aspectRatio = actualHeight > 0 ? actualWidth / actualHeight : 0;
    const aspectRatioString = `${aspectRatio.toFixed(3)}:1`;

    // Check if we have valid dimensions to proceed
    if (actualWidth === 0 || actualHeight === 0) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Unable to determine image dimensions. Please try uploading the image again.",
        },
        { status: 400 }
      );
    }

    // Validation checks (for compliance reporting only - don't block analysis)
    const isValidDimensions = actualWidth === 1280 && actualHeight === 720;
    const isValidMinWidth = actualWidth >= 640;
    const isValidAspectRatio = Math.abs(aspectRatio - 16 / 9) < 0.01;
    const sizeInMB = fileSize / (1024 * 1024);
    const isValidFileSize = sizeInMB <= 2;
    const fileExtension = fileName.split(".").pop()?.toLowerCase();
    const validExtensions = [
      "jpg",
      "jpeg",
      "png",
      "gif",
      "bmp",
      "webp",
      "tiff",
      "tif",
      "svg",
    ];
    const isValidFileType = validExtensions.includes(fileExtension || "");

    // Log validation results for debugging
    console.log("File validation:", {
      fileName,
      fileExtension,
      isValidFileType,
      actualWidth,
      actualHeight,
      sizeInMB,
    });

    // Enhanced AI prompt for better analysis
    const systemPrompt = `You are an expert YouTube thumbnail analyzer. You must provide accurate, detailed analysis based on what you actually see in the image.

**IMAGE SPECIFICATIONS:**
- Actual Dimensions: ${actualWidth}x${actualHeight}px
- Aspect Ratio: ${aspectRatioString}
- File Size: ${sizeInMB.toFixed(2)}MB

**CRITICAL INSTRUCTIONS:**
1. EXAMINE the image carefully - analyze what you actually see
2. If you see human faces, set faces.detected = true and count them accurately
3. Provide realistic confidence scores (0-100) for objects and labels based on detection certainty
4. Give specific, actionable improvements based on the actual image content and quality
5. Create engaging, click-worthy title suggestions that match the image theme and content
6. Be honest about what you can and cannot detect - don't make up data

**ANALYSIS REQUIREMENTS:**

FACES: Look carefully for human faces in the image:
- If you see faces: detected = true, count them, identify visible emotions/expressions
- If no faces: detected = false, count = 0, empty person_details array
- Confidence scores should reflect how certain you are about each emotion/attribute

OBJECTS: Identify specific objects you can clearly see:
- Only include objects you can actually identify with reasonable confidence
- Confidence scores should be realistic (most real detections are 60-95)

LABELS: Categorize the content based on what you see:
- Include relevant content categories, themes, lighting, mood, etc.
- Confidence scores should reflect how well the label describes the image

INTRIGUE: Rate 1-5 based on the actual visual elements:
- Consider composition, mystery, emotional impact, visual interest
- Be realistic - most thumbnails score 2-4 in most categories

RATING: Score 1-10 based on actual thumbnail quality:
- Consider clarity, composition, appeal, professionalism
- Most thumbnails score 3-6, only exceptional ones get 7+
- Provide specific reasoning in the message
- Be  brutally honest with the rating , ensure you analyze the actual image and not just the title and description
- Be extremely critical in your analysis - most thumbnails score between 3-6
- Reserve scores of 7+ only for thumbnails that demonstrate exceptional quality in composition, clarity, emotional impact and professional execution
- Provide detailed justification for high scores (7+) by pointing out specific outstanding elements


IMPROVEMENTS: Give 5 specific suggestions based on actual issues you see:
- Each suggestion should have a clear title and detailed description
- Focus on real problems: blur, poor composition, weak contrast, etc.
- Avoid generic advice - be specific to this image
- Consider the actual dimensions and quality

TITLES: Create 5 engaging titles that match the actual image content:
- Base titles on what you actually see in the image
- Make them click-worthy but relevant to the visual content
- Consider the provided video title for context

You MUST return valid JSON in this exact format:
{
  "faces": {
    "detected": boolean,
    "count": number,
    "person_details": [
      {"name": "specific_emotion_or_attribute", "confidence": number_0_to_100}
    ]
  },
  "intrigue": {
    "elicit": number_1_to_5,
    "enigmatic": number_1_to_5, 
    "provocative": number_1_to_5,
    "intriguing": number_1_to_5,
    "ominous": number_1_to_5
  },
  "objects": [
    {"name": "specific_object_name", "confidence": number_0_to_100}
  ],
  "labels": [
    {"name": "content_label", "confidence": number_0_to_100}
  ],
  "rating": {
    "score": number_1_to_10,
    "qualitative": "Poor|Fair|Good|Very Good|Excellent",
    "message": "specific explanation of the rating"
  },
  "improvements": {
    "thumbnail": [
      {"title": "Clear, actionable title", "description": "Detailed explanation of the specific improvement needed based on actual image analysis"},
      {"title": "Second improvement title", "description": "Specific description of another improvement"},
      {"title": "Third improvement title", "description": "Detailed third improvement description"},
      {"title": "Fourth improvement title", "description": "Specific fourth improvement description"},
      {"title": "Fifth improvement title", "description": "Detailed fifth improvement description"}
    ],
    "title": ["engaging title based on image content", "second engaging title", "third engaging title", "fourth engaging title", "fifth engaging title"]
  }
}

IMPORTANT: Base everything on what you actually see. Don't generate generic responses.`;

    const userPrompt = `Analyze this YouTube thumbnail image for the video titled: "${title}".

The image dimensions are ${actualWidth}x${actualHeight} pixels.

Examine the image carefully and provide detailed analysis with accurate confidence scores. Focus on what will actually help improve this thumbnail's performance on YouTube.

Be specific about what you see - don't make generic suggestions.`;

    // The image from frontend is already a complete data URL from FileReader.readAsDataURL()
    // Format: 'data:image/jpeg;base64,/9j/4AAQ...'
    let imageUrl = image;

    // If for some reason it's not a data URL, construct it properly
    if (!image.startsWith("data:")) {
      const mimeType = fileExtension === "jpg" ? "jpeg" : fileExtension;
      const base64Data = image.includes(",") ? image.split(",")[1] : image;
      imageUrl = `data:image/${mimeType};base64,${base64Data}`;
    }

    console.log("Image URL format:", imageUrl.substring(0, 50) + "...");
    console.log("File extension:", fileExtension);
    console.log("Image URL MIME type:", imageUrl.split(";")[0]);

    const completion = await openai.chat.completions.create({
      model: "gpt-4o",
      messages: [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: userPrompt,
            },
            {
              type: "image_url",
              image_url: {
                url: imageUrl,
                detail: "high",
              },
            },
          ],
        },
      ],
      temperature: 0.2,
      max_tokens: 3000,
      response_format: { type: "json_object" },
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error("No content generated from AI");
    }

    let analysis;
    try {
      analysis = JSON.parse(content);
    } catch (parseError) {
      console.error("JSON parsing error:", parseError);
      console.error("Raw content:", content);

      return NextResponse.json(
        {
          success: false,
          error: "Failed to parse AI response. Please try again.",
        },
        { status: 500 }
      );
    }

    // Only add compliance data - don't override AI analysis
    analysis.compliance = {
      dimensions: {
        width: actualWidth,
        height: actualHeight,
        valid: isValidDimensions,
      },
      minWidth: {
        value: actualWidth,
        valid: isValidMinWidth,
      },
      aspectRatio: {
        ratio: aspectRatioString,
        valid: isValidAspectRatio,
      },
      fileSize: {
        size: fileSize,
        valid: isValidFileSize,
      },
      fileType: {
        type: fileExtension?.toUpperCase() || "UNKNOWN",
        valid: isValidFileType,
      },
      overall:
        isValidDimensions &&
        isValidMinWidth &&
        isValidAspectRatio &&
        isValidFileSize &&
        isValidFileType,
    };

    return NextResponse.json({
      success: true,
      analysis,
    });
  } catch (error: unknown) {
    console.error("Error analyzing thumbnail:", error);

    // Provide more specific error messages
    let errorMessage = "Failed to analyze thumbnail";
    if (error instanceof Error && error.message) {
      if (
        error.message.includes("Invalid image") ||
        error.message.includes("unsupported")
      ) {
        errorMessage =
          "The uploaded image format is not supported or the file is corrupted. Please try uploading a different image.";
      } else if (
        error.message.includes("timeout") ||
        error.message.includes("rate limit")
      ) {
        errorMessage =
          "The analysis is taking longer than expected. Please try again in a moment.";
      } else {
        errorMessage = error.message;
      }
    }

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
      },
      { status: 500 }
    );
  }
}
