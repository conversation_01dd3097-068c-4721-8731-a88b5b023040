"use client";

import React from "react";
import { Button } from "@/src/components/ui/button";

export function CategoryTabs() {
  return (
    <div className="sticky mt-6 z-30 transition-colors overflow-x-hidden w-[90%] bg-background">
      <div className="overflow-x-hidden">
        <div className="flex items-center gap-3 px-6 py-3">
          <Button className="px-3 py-1.5 h-8 rounded-lg text-sm font-medium whitespace-nowrap bg-primary text-primary-foreground hover:bg-primary/90">
            All
          </Button>
          {[
            "Gaming",
            "Music",
            "Sports",
            "News",
            "Entertainment",
            "Technology",
            "Comedy",
            "Education",
            "Travel",
            "Food",
            "Fashion",
            "Science",
          ].map((category) => (
            <Button
              key={category}
              variant="secondary"
              className="px-3 py-1.5 h-8 rounded-lg text-sm font-medium whitespace-nowrap"
            >
              {category}
            </Button>
          ))}
        </div>
      </div>
    </div>
  );
}
