import { Card, CardContent } from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Upload, CheckCircle, X, Image as ImageIcon } from "lucide-react";
import Image from "next/image";

interface ImageUploadSectionProps {
  uploadedImage: string | null;
  handleFileUpload: (file: File) => void;
  handleRemoveImage: () => void;
}

export function ImageUploadSection({
  uploadedImage,
  handleFileUpload,
  handleRemoveImage,
}: ImageUploadSectionProps) {
  return (
    <Card className="border-0 shadow-lg bg-card/50 backdrop-blur">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <ImageIcon className="w-5 h-5 text-primary" />
          <h2 className="text-xl font-semibold">Upload Thumbnail</h2>
        </div>

        {uploadedImage ? (
          // Show image with remove button when image is present
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-sm text-green-600 font-medium">
                  Image uploaded successfully
                </span>
              </div>
              <Button
                onClick={handleRemoveImage}
                variant="outline"
                size="sm"
                className="gap-2 text-destructive hover:text-destructive"
              >
                <X className="w-4 h-4" />
                Remove
              </Button>
            </div>

            <div className="relative rounded-xl overflow-hidden border border-border/50 bg-muted/20 aspect-video">
              <Image
                src={uploadedImage}
                alt="Uploaded thumbnail"
                fill
                className="object-cover"
              />
            </div>
          </div>
        ) : (
          // Show upload interface when no image
          <div className="relative">
            <label className="flex flex-col items-center justify-center w-full h-40 border-2 border-dashed rounded-xl cursor-pointer bg-muted/20 border-border/50 hover:bg-muted/30 transition-all duration-200">
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Upload className="w-10 h-10 mb-3 text-muted-foreground" />
                <p className="text-sm text-muted-foreground text-center font-medium">
                  Drop your thumbnail here or click to upload
                </p>
                <p className="text-xs text-muted-foreground/70 mt-1">
                  PNG, JPG, JPEG, WebP (max 4MB)
                </p>
              </div>

              <input
                type="file"
                className="hidden"
                accept=".png,.jpg,.jpeg,.webp,image/png,image/jpeg,image/webp"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileUpload(file);
                }}
              />
            </label>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
