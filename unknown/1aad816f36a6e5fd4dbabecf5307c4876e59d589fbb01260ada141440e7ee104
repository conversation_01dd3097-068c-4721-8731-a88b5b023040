"use client";

import React from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/src/components/ui/avatar";
import { Menu, Video, Search, Mic, Plus, Bell } from "lucide-react";

export function YouTubeHeader({
  searchQuery,
  setSearchQuery,
  showSidebar,
  setShowSidebar,
}: {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  showSidebar: boolean;
  setShowSidebar: (show: boolean) => void;
}) {
  return (
    <div className="border-b mt-20 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left section */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSidebar(!showSidebar)}
              className="p-2 rounded-full"
            >
              <Menu className="w-5 h-5" />
            </Button>
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-red-600 rounded-sm flex items-center justify-center">
                <Video className="w-5 h-5 text-white" />
              </div>
              <div className="flex items-baseline gap-1">
                <span className="text-xl font-medium tracking-tight">
                  YouTube
                </span>
                <span className="text-xs font-normal bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                  Preview
                </span>
              </div>
            </div>
          </div>

          {/* Center section - Enhanced Search */}
          <div className="flex-1 max-w-2xl mx-8">
            <div className="flex items-center gap-4">
              <div className="flex items-center flex-1">
                <div className="flex-1 flex items-center border rounded-l-full h-10 transition-colors bg-background focus-within:border-primary">
                  <Input
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search"
                    className="border-0 rounded-l-full px-4 h-full focus:ring-0 focus-visible:ring-0 bg-background"
                  />
                </div>
                <Button
                  variant="secondary"
                  className="rounded-r-full px-6 h-10 border-l-0"
                >
                  <Search className="w-5 h-5" />
                </Button>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="p-2 rounded-full w-10 h-10"
              >
                <Mic className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Right section */}
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2 px-3 py-2 h-9 rounded-full"
            >
              <Plus className="w-4 h-4" />
              <span className="text-sm font-medium">Create</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-2 rounded-full w-10 h-10 relative flex items-center justify-center"
            >
              <Bell className="w-8 h-8" size={30} />
            </Button>
            <Avatar>
              <AvatarImage src="https://avatars.githubusercontent.com/u/124599?v=4" />
              <AvatarFallback>CN</AvatarFallback>
            </Avatar>
          </div>
        </div>
      </div>
    </div>
  );
}
