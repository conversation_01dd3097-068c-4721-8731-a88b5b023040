"use client";

import { CurrentPlan } from "@/src/components/account/CurrentPlan";
import { AccountInformation } from "@/src/components/account/AccountInformation";
import { Invoices } from "@/src/components/account/Invoices";
import LogoutButton from "@/src/components/LogoutButton";

export default function AccountPage() {
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-5xl mx-auto space-y-8">
        <div className="flex justify-between items-center">
          <h1 className="text-4xl font-semibold">
            Manage Your <span className="text-primary">Account</span>
          </h1>
          <LogoutButton />
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <CurrentPlan />
          <AccountInformation />
        </div>

        <Invoices />
      </div>
    </div>
  );
}
