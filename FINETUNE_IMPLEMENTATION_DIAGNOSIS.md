# FLUX Fine-tuning Implementation Diagnosis

## PROBLEM STATEMENT
- User has lost $40+ on failed fine-tuning attempts
- Current implementation generates random people instead of the trained persona
- Getting 404 errors when checking fine-tune status
- Need to diagnose what's wrong with the implementation

## CURRENT IMPLEMENTATION OVERVIEW

### 1. ENVIRONMENT CONFIGURATION

```

### 2. CORE FINE-TUNING API (src/lib/flux-finetune.ts)
```typescript
export class FluxFinetuneAPI {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, region: "us" | "eu" = "us") {
    this.apiKey = apiKey;
    this.baseUrl = region === "us" ? "https://api.us1.bfl.ai" : "https://api.eu1.bfl.ai";
  }

  async submitFinetune(request: FluxFinetuneRequest): Promise<FluxFinetuneResponse> {
    const payload = {
      file_data: request.file_data,
      finetune_comment: request.finetune_comment,
      trigger_word: request.trigger_word || "MYCHAR",
      mode: request.mode || "character", // CRITICAL: Use character mode for personas
      iterations: request.iterations || 150,
      learning_rate: request.learning_rate || 0.0001, // Correct LoRA learning rate
      captioning: request.captioning !== undefined ? request.captioning : true,
      priority: request.priority || "quality",
      finetune_type: request.finetune_type || "lora",
      lora_rank: request.lora_rank || 32,
    };

    const response = await fetch(`${this.baseUrl}/v1/finetune`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Key": this.apiKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to submit fine-tune: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxFinetuneResponse;
  }

  async getFinetuneStatus(finetuneId: string): Promise<FluxFinetuneStatus> {
    const url = new URL(`${this.baseUrl}/v1/get_result`);
    url.searchParams.append("id", finetuneId);

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        accept: "application/json",
        "X-Key": this.apiKey,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to get fine-tune status: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxFinetuneStatus;
  }

  async generateWithFinetune(request: FluxFinetuneInferenceRequest): Promise<FluxFinetuneInferenceResponse> {
    const enhancedPrompt = `MYCHAR, a professional portrait of MYCHAR, ${request.prompt}`;

    const response = await fetch(`${this.baseUrl}/v1/flux-pro-1.1-ultra-finetuned`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Key": this.apiKey,
      },
      body: JSON.stringify({
        finetune_id: request.finetune_id,
        prompt: enhancedPrompt,
        finetune_strength: request.finetune_strength || 1.5,
        aspect_ratio: request.aspect_ratio || "1:1",
        output_format: request.output_format || "jpeg",
        safety_tolerance: request.safety_tolerance || 2,
        width: 1024,
        height: 1024,
        steps: 28,
        guidance: 3.5,
        seed: request.seed,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to generate with fine-tune: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxFinetuneInferenceResponse;
  }
}
```

### 3. PERSONA CREATION API (src/app/api/personas/create/route.ts)
```typescript
export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const formData = await req.formData();
    const name = formData.get("name") as string;
    const files = formData.getAll("images") as File[];
    const zipFile = formData.get("zipFile") as File | null;

    // Generate trigger word and get recommendations
    const triggerWord = generateTriggerWord(name);
    const recommendations = getFinetuningRecommendations(imageCount);
    const description = generatePersonaDescription(name, imageCount);

    // Get training data - either from provided ZIP or create from images
    let zipBase64: string;
    
    if (zipFile) {
      console.log("Using provided ZIP file directly");
      const zipBuffer = await zipFile.arrayBuffer();
      zipBase64 = Buffer.from(zipBuffer).toString('base64');
    } else {
      console.log("Creating ZIP from individual images");
      zipBase64 = await createTrainingZip(files);
    }

    // Submit fine-tuning request following the finetunning-fix.md guide
    const finetuneResponse = await fluxFinetuneAPI.submitFinetune({
      file_data: zipBase64,
      finetune_comment: description,
      trigger_word: "MYCHAR",
      mode: "character",
      iterations: 150,
      learning_rate: 0.0001,
      captioning: true,
      priority: "quality",
      finetune_type: "lora",
    });

    console.log("Fine-tuning response:", finetuneResponse);
    console.log(`Fine-tuning submitted with ID: ${finetuneResponse.finetune_id}`);

    // Update persona with fine-tune details
    await updatePersona(persona.id, req.user.userId, {
      finetuneId: finetuneResponse.finetune_id,
      triggerWord,
      status: "training",
    });

    return NextResponse.json({
      success: true,
      persona: {
        ...persona,
        finetuneId: finetuneResponse.finetune_id,
        triggerWord,
        status: "training",
      },
    });
  } catch (error) {
    console.error("Error creating persona:", error);
    return NextResponse.json(
      { success: false, error: "Failed to create persona" },
      { status: 500 }
    );
  }
});
```

### 4. PERSONA UTILITIES (src/lib/persona-utils.ts)
```typescript
export function generateTriggerWord(_personaName: string): string {
  return "MYCHAR";
}

export function getFinetuningRecommendations(imageCount: number) {
  return {
    iterations: 150,
    learningRate: 0.0001,
    priority: "quality",
  };
}

export function generatePersonaDescription(personaName: string, imageCount: number): string {
  return `Character fine-tune for ${personaName} (MYCHAR) trained on ${imageCount} high-quality images. This model generates consistent character images using the trigger word MYCHAR. Trained in character mode for optimal persona preservation.`;
}

export async function createTrainingZip(files: File[]): Promise<string> {
  const JSZip = (await import("jszip")).default;
  const zip = new JSZip();

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const processedImage = await processImageForFinetune(file);
    const extension = processedImage.filename.split('.').pop() || 'jpg';
    const filename = `${i + 1}.${extension}`;
    zip.file(filename, processedImage.buffer);
  }

  const zipBlob = await zip.generateAsync({ type: "blob" });
  const zipBuffer = await zipBlob.arrayBuffer();
  return Buffer.from(zipBuffer).toString("base64");
}
```

### 5. STATUS CHECKING API (src/app/api/personas/[id]/status/route.ts)
```typescript
export const GET = withAuth(async (req: AuthenticatedRequest, { params }: { params: { id: string } }) => {
  try {
    const personaId = params.id;
    const persona = await getPersona(personaId, req.user.userId);

    if (!persona) {
      return NextResponse.json({ success: false, error: "Persona not found" }, { status: 404 });
    }

    try {
      console.log(`Checking fine-tune status for persona ${personaId}, finetuneId: ${persona.finetuneId}`);
      const fluxFinetuneAPI = createFluxFinetuneAPI();
      const status = await fluxFinetuneAPI.getFinetuneStatus(persona.finetuneId);

      console.log(`Persona ${personaId} fine-tune status:`, status.status);

      switch (status.status) {
        case "Ready":
          // Generate persona image and update status
          break;
        case "Error":
          // Handle error
          break;
        default:
          // Still training
          break;
      }
    } catch (statusError) {
      console.error("Error checking fine-tune status:", statusError);

      if (statusError instanceof FluxFinetuneAPIError && statusError.status === 404) {
        console.log(`Fine-tune ID ${persona.finetuneId} not found (404). This might be normal if training just started or if there's a delay.`);
        
        return NextResponse.json({
          success: true,
          persona: { ...persona, status: "training" },
        });
      }

      return NextResponse.json({ success: true, persona });
    }
  } catch (error) {
    console.error("Error checking persona status:", error);
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
});
```

### 6. FRONTEND PERSONA CREATION MODAL (src/components/personas/CreatePersonaModal.tsx)
```typescript
export function CreatePersonaModal({ isOpen, onClose, onSuccess }: CreatePersonaModalProps) {
  const [name, setName] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [zipFile, setZipFile] = useState<File | null>(null);
  const [uploadMode, setUploadMode] = useState<"images" | "zip">("images");
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append("name", name);

    if (uploadMode === "zip" && zipFile) {
      formData.append("zipFile", zipFile);
    } else {
      files.forEach((file) => {
        formData.append("images", file);
      });
    }

    const response = await fetch("/api/personas/create", {
      method: "POST",
      body: formData,
    });

    const data = await response.json();
    if (data.success) {
      setSuccess(true);
      onSuccess();
    } else {
      setError(data.error || "Failed to create persona");
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent>
        <form onSubmit={handleSubmit}>
          {/* Upload mode selection */}
          <div className="flex gap-4">
            <label>
              <input type="radio" name="uploadMode" value="images"
                     checked={uploadMode === "images"}
                     onChange={(e) => setUploadMode(e.target.value as "images" | "zip")} />
              Individual Images
            </label>
            <label>
              <input type="radio" name="uploadMode" value="zip"
                     checked={uploadMode === "zip"}
                     onChange={(e) => setUploadMode(e.target.value as "images" | "zip")} />
              ZIP File (Advanced)
            </label>
          </div>

          {/* File upload areas */}
          {uploadMode === "images" ? (
            <input type="file" multiple accept="image/*" onChange={handleFileSelect} />
          ) : (
            <input type="file" accept=".zip" onChange={handleZipSelect} />
          )}

          <Button type="submit" disabled={isCreating}>Create Persona</Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
```

## CURRENT ISSUES IDENTIFIED

### 1. 404 ERRORS ON STATUS CHECK
- **Problem**: Getting 404 when checking fine-tune status
- **Possible Causes**:
  - Wrong API endpoint URL structure
  - Fine-tune ID format issues
  - Timing issues (checking too soon after creation)
  - API key authentication problems

### 2. GENERATING RANDOM PEOPLE
- **Problem**: Fine-tuned model generates random people instead of trained persona
- **Possible Causes**:
  - Wrong training mode ("general" vs "character")
  - Incorrect trigger word usage
  - Low finetune_strength (1.0 vs 1.5)
  - Poor prompt engineering
  - ZIP file structure issues
  - Learning rate problems (LoRA needs 0.0001 vs 0.00001)

### 3. COST OPTIMIZATION SETTINGS
- **Current Settings**:
  - Model: FLUX Pro 1.1 Ultra
  - Training Type: LoRA
  - Iterations: 150 ($2 cost)
  - Learning Rate: 0.0001
  - Mode: "character"
  - Trigger Word: "MYCHAR"
  - Finetune Strength: 1.5

### 4. API ENDPOINTS USED
- **Training**: `https://api.us1.bfl.ai/v1/finetune`
- **Status**: `https://api.us1.bfl.ai/v1/get_result?id={finetune_id}`
- **Inference**: `https://api.us1.bfl.ai/v1/flux-pro-1.1-ultra-finetuned`

### 5. ZIP FILE STRUCTURE
```
training.zip
├── 1.jpg
├── 2.jpg
├── 3.jpg
...
├── 12.jpg
```

### 6. PROMPT ENHANCEMENT FOR INFERENCE
```
Original: "wearing a suit"
Enhanced: "MYCHAR, a professional portrait of MYCHAR, wearing a suit"
```

## DEBUGGING QUESTIONS FOR AI TOOL

1. **API Endpoint Issues**: Are the BFL API endpoints correct for fine-tuning and status checking?

2. **Training Parameters**: Are the training parameters (mode: "character", learning_rate: 0.0001, iterations: 150) correct for persona fine-tuning?

3. **Trigger Word Strategy**: Is using "MYCHAR" as a consistent trigger word the right approach?

4. **ZIP File Structure**: Is the simple sequential naming (1.jpg, 2.jpg, etc.) correct for BFL training?

5. **Inference Prompting**: Is the prompt enhancement strategy correct for character preservation?

6. **Status Checking Timing**: How long should we wait before checking fine-tune status to avoid 404s?

7. **Finetune Strength**: Is 1.5 the right finetune_strength for character preservation?

8. **Cost vs Quality**: Are we sacrificing too much quality for cost reduction with 150 iterations?

## EXPECTED BEHAVIOR
- Upload 12 images of user (Sheriff/Sharif)
- Train for ~15-20 minutes at $2 cost
- Generate images that look like the user, not random people
- Status checking should work without 404 errors

## ACTUAL BEHAVIOR
- 404 errors on status checking
- Generated images show random people
- $40+ lost on failed attempts

## USER CONTEXT
- User name: Sheriff (Arabic: Sharif)
- Has 312 credits available in BFL account
- Previously lost $40+ on failed training attempts
- Wants to minimize cost while ensuring functionality
- Has prepared ZIP file with 12 images ready for testing
