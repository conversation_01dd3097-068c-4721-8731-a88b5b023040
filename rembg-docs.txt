API Reference for rembg.js
@remove-background-ai/rembg.js is a zero-config Node.js wrapper for the free Rembg API, enabling background removal with simple, customizable parameters.

Parameters
Parameter	Type	Required	Default	Description
apiKey	string	
Required
–

Your Rembg API key
inputImage	string | Buffer | { base64: string }	
Required
–

Image file, buffer, or base64 payload
onDownloadProgress	(event) => void	
Optional
–

Hook for upload progress events
onUploadProgress	(event) => void	
Optional
–

Hook for download progress events
options.returnBase64	boolean	
Optional
false
Return Base64 string instead of file
options.returnMask	boolean	
Optional
false
Return only the alpha mask
options.w	number	
Optional
–

Target width (maintains aspect ratio)
options.h	number	
Optional
–

Target height (maintains aspect ratio)
options.exact_resize	boolean	
Optional
false
Force exact width × height (may distort)
Installation of rembg.js
latest npm versionGitHub starsnpm downloadslicenseGitHub issues

npm i @remove-background-ai/rembg.js
                        
API Usage in Node using rembg.js
When leveraging the rembg.js library in a Node environment, you can easily integrate background removal capabilities into your application. Below is a step-by-step guide on how to set up and use the API:

Setting up Your Environment: Ensure you have a .env file in your project root containing your API key.

Importing the Necessary Modules: Begin by importing the rembg function from @remove-background-ai/rembg.js and the dotenv module to handle environment variables.

Configuring Progress Callbacks: The library offers onDownloadProgress and onUploadProgress callbacks to track the progress of file operations. In the provided example, we're logging these events directly to the console.

Now, let's take a closer look at a sample usage:


// script.mjs file

import  { rembg } from '@remove-background-ai/rembg.js';
import dotenv from 'dotenv';
// Load environment variables from .env file
dotenv.config();

// API_KEY will be loaded from the .env file
const API_KEY = process.env.API_KEY;

// log upload and download progress
const onDownloadProgress = console.log;
const onUploadProgress = console.log;
rembg({
    apiKey: API_KEY,
    inputImage: './input.png', //inputImage can be one of these: string | Buffer | { base64: string };
    onDownloadProgress,
    onUploadProgress
}).then(({ outputImagePath, cleanup }) => {
    console.log('✅🎉 background removed and saved under path=', outputImagePath);
    // if called, it will cleanup (remove from disk) your removed background image
    // cleanup();
});
					
						
Remember, the cleanup function can be called if you wish to remove the processed image from your disk after background removal.

Showing Progress bar
When integrating a background removal service, it's often beneficial to provide users with feedback on the progress of their upload or download request. To facilitate this, you can define your own onDownloadProgress and onUploadProgress callbacks. Both of these callbacks accept AxiosProgressEvent as an event parameter. As the request proceeds, these callbacks are invoked multiple times, allowing you to, for instance, display a progress bar and adjust its length based on the progress.


 (base) root@DESKTOP-C0Q8KK7:~/rembg.js-test# node index.mjs
 {
   loaded: 65687,
   total: 68474,
   progress: 0.9592984198381868, <---- @95% progress 
   bytes: 65687,
   rate: undefined,
   estimated: undefined,
   upload: true  <---- upload progress
 }
 {
   loaded: 68474,
   total: 68474,
   progress: 1, <---- @100% progress 
   bytes: 2787,
   rate: undefined,
   estimated: undefined,
   upload: true <---- upload progress 
 }
 {
   loaded: 1002,
   total: 68824,
   progress: 0.014558874811112402, <---- @1% progress 
   bytes: 1002,
   rate: undefined,
   estimated: undefined,
   download: true <---- download progress 
 }
 {
   loaded: 68824,
   total: 68824,
   progress: 1, <---- @100% progress 
   bytes: 67822,
   rate: undefined,
   estimated: undefined,
   download: true <---- download progress
 }
 ✅🎉 background removed and saved under path=/tmp/rembg--3339-DBqqeJ2eOs4D-.png
					