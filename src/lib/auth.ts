import { createClient } from "@/src/lib/supabase/server";
import { getUser, updateUser, createUser } from "@/src/actions/userAction";
import { User as SupabaseUser } from "@supabase/supabase-js";
import { NextRequest } from "next/server";

// Request-scoped cache to prevent multiple auth calls within the same request
const requestAuthCache = new WeakMap<
  NextRequest,
  {
    supabaseUser: SupabaseUser;
    dbUser: any;
    timestamp: number;
  }
>();

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * Optimized authentication that uses request-scoped caching
 * Eliminates multiple user sync calls within the same request
 */
export async function getAuthenticatedUser(request?: NextRequest): Promise<{
  supabaseUser: SupabaseUser;
  dbUser: any;
}> {
  // Check request cache first if request object is available
  if (request) {
    const cached = requestAuthCache.get(request);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return {
        supabaseUser: cached.supabaseUser,
        dbUser: cached.dbUser,
      };
    }
  }

  const supabase = await createClient();

  try {
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    if (error || !user) {
      throw new Error("Unauthorized");
    }

    // Get user from database - this is the only required DB call
    let dbUser = await getUser(user.id);

    // If user doesn't exist in our database, create them
    if (!dbUser) {
      const name =
        user.user_metadata?.full_name || user.user_metadata?.name || null;
      const avatarUrl =
        user.user_metadata?.avatar_url || user.user_metadata?.picture || null;

      dbUser = await createUser({
        userId: user.id,
        email: user.email!,
        name,
        avatarUrl,
      });
    }
    // Only update if data has actually changed (avoid unnecessary writes)
    else {
      const name =
        user.user_metadata?.full_name || user.user_metadata?.name || null;
      const avatarUrl =
        user.user_metadata?.avatar_url || user.user_metadata?.picture || null;

      if (
        dbUser.email !== user.email ||
        dbUser.name !== name ||
        dbUser.avatarUrl !== avatarUrl
      ) {
        dbUser = await updateUser(user.id, { name, avatarUrl });
      }
    }

    const result = { supabaseUser: user, dbUser };

    // Cache the result for this request
    if (request) {
      requestAuthCache.set(request, {
        supabaseUser: user,
        dbUser,
        timestamp: Date.now(),
      });
    }

    return result;
  } catch (error) {
    console.error("Error getting authenticated user:", error);
    throw error;
  }
}

/**
 * Lightweight function to get just the user ID
 * Uses the same caching mechanism
 */
export async function getAuthenticatedUserId(
  request?: NextRequest
): Promise<string> {
  const { supabaseUser } = await getAuthenticatedUser(request);
  return supabaseUser.id;
}

/**
 * Legacy compatibility function - gradually replace with getAuthenticatedUser
 * @deprecated Use getAuthenticatedUser instead
 */
export async function getCurrentUser(): Promise<{
  supabaseUser: SupabaseUser;
  dbUser: any;
}> {
  return getAuthenticatedUser();
}

/**
 * Legacy compatibility function - gradually replace with getAuthenticatedUserId
 * @deprecated Use getAuthenticatedUserId instead
 */
export async function getCurrentUserId(): Promise<string> {
  return getAuthenticatedUserId();
}

/**
 * Clear the auth cache for a specific request (useful for testing)
 */
export function clearAuthCache(request: NextRequest): void {
  requestAuthCache.delete(request);
}

/**
 * Check if user is authenticated without throwing errors
 * Useful for optional authentication scenarios
 */
export async function isAuthenticated(request?: NextRequest): Promise<boolean> {
  try {
    await getAuthenticatedUser(request);
    return true;
  } catch {
    return false;
  }
}
