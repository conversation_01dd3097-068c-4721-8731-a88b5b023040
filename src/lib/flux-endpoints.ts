/**
 * Modular Flux API endpoints for face swapping
 * Supports Fill, Canny, Depth, and Ultra modes
 */

export type FluxMethod = "fill" | "canny" | "depth" | "ultra";

export interface FluxBaseRequest {
  finetune_id: string;
  finetune_strength: number;
  prompt: string;
  steps: number;
  seed?: number;
  output_format: "jpeg" | "png";
}

export interface FluxFillRequest extends FluxBaseRequest {
  image: string; // base64
  mask: string; // base64
  guidance: number;
}

export interface FluxCannyRequest extends FluxBaseRequest {
  control_image: string; // base64 canny edges
  canny_low_threshold: number;
  canny_high_threshold: number;
  guidance: number;
}

export interface FluxDepthRequest extends FluxBaseRequest {
  control_image: string; // base64 depth map
  guidance: number;
}

export interface FluxUltraRequest extends FluxBaseRequest {
  image_prompt: string; // base64
  image_prompt_strength: number;
  aspect_ratio: string;
}

export interface FluxResponse {
  id: string;
  status: string;
  result?: {
    sample: string; // URL to generated image
  };
}

/**
 * Core Flux API caller
 */
async function callFluxAPI(endpoint: string, body: any): Promise<FluxResponse> {
  console.log(`🚀 Calling Flux API: ${endpoint}`);
  console.log(`📦 Request body:`, JSON.stringify(body, null, 2));

  const response = await fetch(`https://api.us1.bfl.ai${endpoint}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-key": process.env.BFL_API_KEY!,
    },
    body: JSON.stringify(body),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`❌ Flux API error (${response.status}):`, errorText);
    throw new Error(`Flux API error (${response.status}): ${errorText}`);
  }

  const result = await response.json();
  console.log(`✅ Flux API response:`, JSON.stringify(result, null, 2));
  return result;
}

/**
 * Poll for inference completion and return image URL
 */
async function pollForCompletion(inferenceId: string): Promise<string> {
  const maxAttempts = 60; // 5 minutes max
  const pollInterval = 5000; // 5 seconds

  for (let attempt = 0; attempt < maxAttempts; attempt++) {
    try {
      console.log(
        `🔄 Polling inference ${inferenceId}, attempt ${
          attempt + 1
        }/${maxAttempts}`
      );

      const response = await fetch(
        `https://api.us1.bfl.ai/v1/get_result?id=${inferenceId}`,
        {
          headers: {
            "x-key": process.env.BFL_API_KEY!,
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to get result: ${response.status}`);
      }

      const result = await response.json();
      console.log(`📊 Inference status:`, result.status);

      switch (result.status) {
        case "Ready":
          if (!result.result?.sample) {
            throw new Error("Inference completed but no image URL returned");
          }
          console.log(
            `✅ Inference complete! Image URL: ${result.result.sample}`
          );
          return result.result.sample;

        case "Error":
          throw new Error(
            `Inference failed: ${result.error || "Unknown error"}`
          );

        case "Pending":
        case "Request Moderated":
          // Continue polling
          break;

        default:
          console.warn(`Unknown status: ${result.status}`);
          break;
      }

      // Wait before next poll
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    } catch (error) {
      console.error(`Error polling inference ${inferenceId}:`, error);
      throw error;
    }
  }

  throw new Error(
    `Inference ${inferenceId} timed out after ${maxAttempts} attempts`
  );
}

/**
 * Fill endpoint - Best for precise face swapping with masks
 */
export async function fluxFill(request: FluxFillRequest): Promise<string> {
  console.log("🎯 Using Flux Fill endpoint for face swapping");

  const response = await callFluxAPI(
    "/v1/flux-pro-1.0-fill-finetuned",
    request
  );

  // The API returns an inference ID, we need to poll for completion
  if (response.id) {
    console.log("📋 Got inference ID:", response.id);
    return await pollForCompletion(response.id);
  }

  throw new Error("No inference ID returned from Flux Fill");
}

/**
 * Canny edge control endpoint - Good for structural guidance
 */
export async function fluxCanny(request: FluxCannyRequest): Promise<string> {
  console.log("🎯 Using Flux Canny endpoint for face swapping");

  const response = await callFluxAPI(
    "/v1/flux-pro-1.0-canny-finetuned",
    request
  );

  if (response.id) {
    console.log("📋 Got inference ID:", response.id);
    return await pollForCompletion(response.id);
  }

  throw new Error("No inference ID returned from Flux Canny");
}

/**
 * Depth control endpoint - Good for 3D structural guidance
 */
export async function fluxDepth(request: FluxDepthRequest): Promise<string> {
  console.log("🎯 Using Flux Depth endpoint for face swapping");

  const response = await callFluxAPI(
    "/v1/flux-pro-1.0-depth-finetuned",
    request
  );

  if (response.id) {
    console.log("📋 Got inference ID:", response.id);
    return await pollForCompletion(response.id);
  }

  throw new Error("No inference ID returned from Flux Depth");
}

/**
 * Ultra mode endpoint - High-res generation with style guidance
 */
export async function fluxUltra(request: FluxUltraRequest): Promise<string> {
  console.log("🎯 Using Flux Ultra endpoint for face swapping");

  const response = await callFluxAPI(
    "/v1/flux-pro-1.1-ultra-finetuned",
    request
  );

  if (response.id) {
    console.log("📋 Got inference ID:", response.id);
    return await pollForCompletion(response.id);
  }

  throw new Error("No inference ID returned from Flux Ultra");
}

/**
 * Multi-endpoint face swap with automatic fallbacks
 */
export async function performFaceSwap(
  method: FluxMethod,
  imageB64: string,
  finetuneId: string,
  prompt: string,
  options: {
    maskB64?: string;
    controlImageB64?: string;
    aspectRatio?: string;
  } = {}
): Promise<string> {
  const baseConfig: FluxBaseRequest = {
    finetune_id: finetuneId,
    finetune_strength: 1.0,
    prompt,
    steps: 50,
    seed: Math.floor(Math.random() * 1000),
    output_format: "jpeg",
  };

  try {
    switch (method) {
      case "fill":
        if (!options.maskB64) {
          throw new Error("Mask required for fill method");
        }
        return await fluxFill({
          ...baseConfig,
          image: imageB64,
          mask: options.maskB64,
          guidance: 60,
        });

      case "canny":
        if (!options.controlImageB64) {
          throw new Error("Control image required for canny method");
        }
        return await fluxCanny({
          ...baseConfig,
          control_image: options.controlImageB64,
          canny_low_threshold: 100,
          canny_high_threshold: 200,
          guidance: 30,
        });

      case "depth":
        if (!options.controlImageB64) {
          throw new Error("Control image required for depth method");
        }
        return await fluxDepth({
          ...baseConfig,
          control_image: options.controlImageB64,
          guidance: 15,
        });

      case "ultra":
        return await fluxUltra({
          ...baseConfig,
          image_prompt: imageB64,
          image_prompt_strength: 0.2,
          aspect_ratio: options.aspectRatio || "16:9",
        });

      default:
        throw new Error(`Unsupported method: ${method}`);
    }
  } catch (error) {
    console.error(`Face swap failed with method ${method}:`, error);
    throw error;
  }
}

/**
 * Face swap with automatic fallback chain
 * Tries Fill -> Canny -> Ultra in order
 */
export async function performFaceSwapWithFallbacks(
  imageB64: string,
  finetuneId: string,
  prompt: string,
  maskB64?: string,
  cannyB64?: string
): Promise<{ imageUrl: string; method: FluxMethod }> {
  const methods: FluxMethod[] = ["fill", "canny", "ultra"];

  console.log(`🎯 Starting face swap with fallbacks. Available data:`);
  console.log(
    `   - Image: ${imageB64 ? "Yes" : "No"} (${imageB64?.length || 0} chars)`
  );
  console.log(
    `   - Mask: ${maskB64 ? "Yes" : "No"} (${maskB64?.length || 0} chars)`
  );
  console.log(
    `   - Canny: ${cannyB64 ? "Yes" : "No"} (${cannyB64?.length || 0} chars)`
  );
  console.log(`   - Finetune ID: ${finetuneId}`);

  for (const method of methods) {
    try {
      console.log(`🚀 Attempting face swap with method: ${method}`);

      let imageUrl: string;

      switch (method) {
        case "fill":
          if (!maskB64) {
            console.log("No mask available, skipping fill method");
            continue;
          }
          imageUrl = await performFaceSwap(
            method,
            imageB64,
            finetuneId,
            prompt,
            {
              maskB64,
            }
          );
          break;

        case "canny":
          if (!cannyB64) {
            console.log("No canny edges available, skipping canny method");
            continue;
          }
          imageUrl = await performFaceSwap(
            method,
            imageB64,
            finetuneId,
            prompt,
            {
              controlImageB64: cannyB64,
            }
          );
          break;

        case "ultra":
          imageUrl = await performFaceSwap(
            method,
            imageB64,
            finetuneId,
            prompt,
            {
              aspectRatio: "16:9",
            }
          );
          break;

        default:
          continue;
      }

      console.log(`Face swap successful with method: ${method}`);
      return { imageUrl, method };
    } catch (error) {
      console.warn(`Method ${method} failed:`, error);

      // If this is the last method, throw the error
      if (method === methods[methods.length - 1]) {
        throw error;
      }

      // Otherwise, continue to next method
      continue;
    }
  }

  throw new Error("All face swap methods failed");
}
