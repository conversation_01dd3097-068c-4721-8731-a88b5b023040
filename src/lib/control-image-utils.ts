/**
 * Utilities for generating control images (Canny edges, depth maps)
 * Used for Flux Canny and Depth endpoints
 */

/**
 * Generate Canny edge detection from an image
 * @param imageB64 - Base64 encoded image
 * @returns Promise that resolves to base64 encoded Canny edge image
 */
export async function generateCannyEdges(imageB64: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      try {
        // Create canvas for processing
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        
        canvas.width = img.width;
        canvas.height = img.height;
        
        // Draw image to canvas
        ctx.drawImage(img, 0, 0);
        
        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Convert to grayscale
        for (let i = 0; i < data.length; i += 4) {
          const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
          data[i] = gray;     // R
          data[i + 1] = gray; // G
          data[i + 2] = gray; // B
          // Alpha stays the same
        }
        
        // Apply simple edge detection (Sobel-like)
        const edgeData = applyEdgeDetection(data, canvas.width, canvas.height);
        
        // Create new image data with edges
        const edgeImageData = new ImageData(edgeData, canvas.width, canvas.height);
        
        // Clear canvas and draw edge data
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.putImageData(edgeImageData, 0, 0);
        
        // Convert to base64
        const cannyB64 = canvas.toDataURL('image/png').split(',')[1];
        resolve(cannyB64);
        
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => reject(new Error('Failed to load image for Canny processing'));
    img.src = `data:image/jpeg;base64,${imageB64}`;
  });
}

/**
 * Apply simple edge detection algorithm
 */
function applyEdgeDetection(data: Uint8ClampedArray, width: number, height: number): Uint8ClampedArray {
  const result = new Uint8ClampedArray(data.length);
  
  // Sobel kernels
  const sobelX = [-1, 0, 1, -2, 0, 2, -1, 0, 1];
  const sobelY = [-1, -2, -1, 0, 0, 0, 1, 2, 1];
  
  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      let gx = 0, gy = 0;
      
      // Apply Sobel kernels
      for (let ky = -1; ky <= 1; ky++) {
        for (let kx = -1; kx <= 1; kx++) {
          const idx = ((y + ky) * width + (x + kx)) * 4;
          const pixel = data[idx]; // Use red channel (grayscale)
          const kernelIdx = (ky + 1) * 3 + (kx + 1);
          
          gx += pixel * sobelX[kernelIdx];
          gy += pixel * sobelY[kernelIdx];
        }
      }
      
      // Calculate gradient magnitude
      const magnitude = Math.sqrt(gx * gx + gy * gy);
      const edge = magnitude > 50 ? 255 : 0; // Threshold
      
      const idx = (y * width + x) * 4;
      result[idx] = edge;     // R
      result[idx + 1] = edge; // G
      result[idx + 2] = edge; // B
      result[idx + 3] = 255;  // A
    }
  }
  
  return result;
}

/**
 * Generate a simple depth map from an image
 * This is a basic implementation - for production, consider using MiDaS or similar
 * @param imageB64 - Base64 encoded image
 * @returns Promise that resolves to base64 encoded depth map
 */
export async function generateDepthMap(imageB64: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      try {
        // Create canvas for processing
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d')!;
        
        canvas.width = img.width;
        canvas.height = img.height;
        
        // Draw image to canvas
        ctx.drawImage(img, 0, 0);
        
        // Get image data
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        // Simple depth estimation based on brightness and position
        // This is a basic heuristic - real depth estimation requires ML models
        const depthData = generateSimpleDepth(data, canvas.width, canvas.height);
        
        // Create new image data with depth
        const depthImageData = new ImageData(depthData, canvas.width, canvas.height);
        
        // Clear canvas and draw depth data
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.putImageData(depthImageData, 0, 0);
        
        // Convert to base64
        const depthB64 = canvas.toDataURL('image/png').split(',')[1];
        resolve(depthB64);
        
      } catch (error) {
        reject(error);
      }
    };
    
    img.onerror = () => reject(new Error('Failed to load image for depth processing'));
    img.src = `data:image/jpeg;base64,${imageB64}`;
  });
}

/**
 * Generate simple depth map using heuristics
 */
function generateSimpleDepth(data: Uint8ClampedArray, width: number, height: number): Uint8ClampedArray {
  const result = new Uint8ClampedArray(data.length);
  
  for (let y = 0; y < height; y++) {
    for (let x = 0; x < width; x++) {
      const idx = (y * width + x) * 4;
      
      // Get pixel brightness
      const r = data[idx];
      const g = data[idx + 1];
      const b = data[idx + 2];
      const brightness = (r + g + b) / 3;
      
      // Simple depth heuristic:
      // - Brighter pixels are closer (faces, foreground)
      // - Darker pixels are farther (shadows, background)
      // - Center pixels are slightly closer
      const centerX = width / 2;
      const centerY = height / 2;
      const distanceFromCenter = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
      const maxDistance = Math.sqrt(centerX ** 2 + centerY ** 2);
      const centerFactor = 1 - (distanceFromCenter / maxDistance) * 0.3;
      
      // Combine brightness and center bias for depth
      let depth = brightness * centerFactor;
      depth = Math.max(0, Math.min(255, depth));
      
      result[idx] = depth;     // R
      result[idx + 1] = depth; // G
      result[idx + 2] = depth; // B
      result[idx + 3] = 255;   // A
    }
  }
  
  return result;
}

/**
 * Generate control images for Flux endpoints
 * @param imageB64 - Base64 encoded source image
 * @returns Object with generated control images
 */
export async function generateControlImages(imageB64: string): Promise<{
  canny?: string;
  depth?: string;
}> {
  const results: { canny?: string; depth?: string } = {};
  
  try {
    console.log('Generating Canny edges...');
    results.canny = await generateCannyEdges(imageB64);
    console.log('Canny edges generated successfully');
  } catch (error) {
    console.warn('Failed to generate Canny edges:', error);
  }
  
  try {
    console.log('Generating depth map...');
    results.depth = await generateDepthMap(imageB64);
    console.log('Depth map generated successfully');
  } catch (error) {
    console.warn('Failed to generate depth map:', error);
  }
  
  return results;
}

/**
 * Check if we can generate control images in the current environment
 */
export function canGenerateControlImages(): boolean {
  return typeof document !== 'undefined' && typeof HTMLCanvasElement !== 'undefined';
}
