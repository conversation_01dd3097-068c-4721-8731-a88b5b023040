/**
 * Flux API utility for image generation using Flux KontexMax
 * Provides a clean interface for text-to-image and image-to-image generation with polling
 */

export interface FluxGenerationRequest {
  prompt: string;
  input_image?: string; // Base64 encoded image or URL for image-to-image generation
  aspect_ratio?: string;
  seed?: number;
  prompt_upsampling?: boolean;
  safety_tolerance?: number;
  output_format?: "jpeg" | "png";
  webhook_url?: string;
  webhook_secret?: string;
}

export interface FluxGenerationResponse {
  id: string;
  polling_url: string;
}

export interface FluxResultResponse {
  id: string;
  status:
    | "Task not found"
    | "Pending"
    | "Request Moderated"
    | "Content Moderated"
    | "Ready"
    | "Error";
  result?: {
    sample: string; // URL to the generated image
  };
  progress?: number;
  details?: Record<string, unknown>;
}

export class FluxAPIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public details?: unknown
  ) {
    super(message);
    this.name = "FluxAPIError";
  }
}

export class FluxAPI {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string = "https://api.bfl.ai") {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  /**
   * Submit a text-to-image generation request to Flux KontexMax
   */
  async submitGeneration(
    request: FluxGenerationRequest
  ): Promise<FluxGenerationResponse> {
    const response = await fetch(`${this.baseUrl}/v1/flux-kontext-max`, {
      method: "POST",
      headers: {
        accept: "application/json",
        "x-key": this.apiKey,
        "Content-Type": "application/json",
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxAPIError(
        `Failed to submit generation request: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxGenerationResponse;
  }

  /**
   * Poll for the result of a generation request
   */
  async pollResult(
    pollingUrl: string,
    _requestId?: string
  ): Promise<FluxResultResponse> {
    const response = await fetch(pollingUrl, {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-key": this.apiKey,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxAPIError(
        `Failed to poll result: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxResultResponse;
  }

  /**
   * Generate an image with progress tracking
   * This method provides real-time progress updates via callback
   */
  async generateImageWithProgress(
    request: FluxGenerationRequest,
    onProgress: (progress: number, stage: string) => void,
    options: {
      maxWaitTime?: number; // Maximum time to wait in milliseconds (default: 5 minutes)
      pollInterval?: number; // Polling interval in milliseconds (default: 500ms)
    } = {}
  ): Promise<string> {
    const { maxWaitTime = 5 * 60 * 1000, pollInterval = 500 } = options;

    // Submit the generation request
    onProgress(5, "submitting");
    const { id: requestId, polling_url: pollingUrl } =
      await this.submitGeneration(request);

    const startTime = Date.now();
    onProgress(10, "processing");

    // Poll for the result with progress updates
    while (Date.now() - startTime < maxWaitTime) {
      const result = await this.pollResult(pollingUrl, requestId);

      // Update progress based on API response
      if (result.progress !== undefined) {
        onProgress(result.progress, "generating");
      }

      switch (result.status) {
        case "Ready":
          if (!result.result?.sample) {
            throw new FluxAPIError(
              "Generation completed but no image URL returned"
            );
          }
          onProgress(100, "complete");
          return result.result.sample;

        case "Error":
          throw new FluxAPIError(
            "Generation failed",
            undefined,
            result.details
          );

        case "Request Moderated":
          throw new FluxAPIError(
            "Request was moderated. Please try rephrasing your prompt to focus on positive, creative content.",
            400
          );

        case "Content Moderated":
          throw new FluxAPIError(
            "Generated content was moderated. Please try a different prompt.",
            400
          );

        case "Task not found":
          throw new FluxAPIError("Generation task not found", 404);

        case "Pending":
          // Continue polling
          await new Promise((resolve) => setTimeout(resolve, pollInterval));
          break;

        default:
          throw new FluxAPIError(`Unknown status: ${result.status}`);
      }
    }

    throw new FluxAPIError("Generation timed out", 408);
  }

  /**
   * Generate an image and wait for completion
   * This is a convenience method that handles the full flow
   */
  async generateImage(
    request: FluxGenerationRequest,
    options: {
      maxWaitTime?: number; // Maximum time to wait in milliseconds (default: 5 minutes)
      pollInterval?: number; // Polling interval in milliseconds (default: 500ms)
    } = {}
  ): Promise<string> {
    const { maxWaitTime = 5 * 60 * 1000, pollInterval = 500 } = options;

    // Submit the generation request
    const { id: requestId, polling_url: pollingUrl } =
      await this.submitGeneration(request);

    const startTime = Date.now();

    // Poll for the result
    while (Date.now() - startTime < maxWaitTime) {
      const result = await this.pollResult(pollingUrl, requestId);

      switch (result.status) {
        case "Ready":
          if (!result.result?.sample) {
            throw new FluxAPIError(
              "Generation completed but no image URL returned"
            );
          }
          return result.result.sample;

        case "Error":
          throw new FluxAPIError(
            "Generation failed",
            undefined,
            result.details
          );

        case "Request Moderated":
          throw new FluxAPIError(
            "Request was moderated. Please try rephrasing your prompt to focus on positive, creative content.",
            400
          );

        case "Content Moderated":
          throw new FluxAPIError(
            "Generated content was moderated. Please try a different prompt.",
            400
          );

        case "Task not found":
          throw new FluxAPIError("Generation task not found", 404);

        case "Pending":
          // Continue polling
          await new Promise((resolve) => setTimeout(resolve, pollInterval));
          break;

        default:
          throw new FluxAPIError(`Unknown status: ${result.status}`);
      }
    }

    throw new FluxAPIError("Generation timed out", 408);
  }

  /**
   * Download image from Flux delivery URL and return as base64
   * Note: Flux delivery URLs are only valid for 10 minutes
   */
  async downloadImageAsBase64(imageUrl: string): Promise<string> {
    const response = await fetch(imageUrl);

    if (!response.ok) {
      throw new FluxAPIError(
        `Failed to download image: ${response.status} ${response.statusText}`,
        response.status
      );
    }

    const arrayBuffer = await response.arrayBuffer();
    const base64 = Buffer.from(arrayBuffer).toString("base64");

    return base64;
  }
}

/**
 * Create a Flux API instance with the API key from environment variables
 */
export function createFluxAPI(): FluxAPI {
  const apiKey = process.env.BFL_API_KEY;

  if (!apiKey) {
    throw new FluxAPIError("BFL_API_KEY environment variable is not set");
  }

  return new FluxAPI(apiKey);
}
