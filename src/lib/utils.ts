import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Prompt validation utilities for Flux models
export interface PromptValidationResult {
  isValid: boolean;
  characterCount: number;
  characterLimit: number;
  errorMessage?: string;
  warningMessage?: string;
}

export interface PromptValidationConfig {
  characterLimit: number;
  warningThreshold?: number; // Show warning at this percentage (e.g., 0.8 for 80%)
  modelName?: string;
}

// Character limits for different Flux models based on BFL documentation
export const FLUX_MODEL_LIMITS = {
  // FLUX.1 Kontext models have 512 token limit
  // Using conservative estimate: 512 tokens × 3.5 chars/token = ~1800 chars
  KONTEXT_MAX: 1800,
  KONTEXT_DEV: 1800,
  KONTEXT_PRO: 1800,

  // Fine-tuned models use same base limits
  FINETUNED: 1800,

  // Default safe limit for all Flux models
  DEFAULT: 1800,
} as const;

/**
 * Validate prompt character count against Flux model limits
 */
export function validatePrompt(
  prompt: string,
  config: PromptValidationConfig
): PromptValidationResult {
  const characterCount = prompt.length;
  const { characterLimit, warningThreshold = 0.8, modelName = "Flux" } = config;

  const result: PromptValidationResult = {
    isValid: characterCount <= characterLimit,
    characterCount,
    characterLimit,
  };

  if (characterCount > characterLimit) {
    const excess = characterCount - characterLimit;
    result.errorMessage = `Prompt exceeds ${modelName} limit by ${excess} character${
      excess === 1 ? "" : "s"
    }. Please shorten your prompt.`;
  } else if (characterCount >= characterLimit * warningThreshold) {
    const remaining = characterLimit - characterCount;
    result.warningMessage = `Approaching ${modelName} limit. ${remaining} character${
      remaining === 1 ? "" : "s"
    } remaining.`;
  }

  return result;
}

/**
 * Get validation config for different tools/models
 */
export function getPromptValidationConfig(
  tool: "thumbnail" | "recreate" | "faceswap" | "title",
  hasPersona: boolean = false
): PromptValidationConfig {
  const baseLimit = FLUX_MODEL_LIMITS.DEFAULT;

  switch (tool) {
    case "thumbnail":
      return {
        characterLimit: baseLimit,
        warningThreshold: 0.85,
        modelName: hasPersona ? "Flux Fine-tuned" : "Flux KontexMax",
      };

    case "recreate":
      return {
        characterLimit: baseLimit,
        warningThreshold: 0.85,
        modelName: hasPersona ? "Flux Pro Finetuned" : "Flux KontexMax",
      };

    case "faceswap":
      // Face swap uses hardcoded prompts, but keeping for consistency
      return {
        characterLimit: baseLimit,
        warningThreshold: 0.85,
        modelName: "Flux Fill Finetuned",
      };

    case "title":
      return {
        characterLimit: baseLimit,
        warningThreshold: 0.85,
        modelName: "OpenAI GPT-4",
      };

    default:
      return {
        characterLimit: baseLimit,
        warningThreshold: 0.85,
        modelName: "Flux",
      };
  }
}
