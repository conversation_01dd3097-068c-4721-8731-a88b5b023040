import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/src/lib/auth";

export interface AuthenticatedRequest extends NextRequest {
  user: {
    supabaseUser: any;
    dbUser: any;
    userId: string;
  };
}

/**
 * Higher-order function that wraps API routes with authentication
 * Eliminates the need to call getCurrentUser in every route
 */
export function withAuth<T extends any[]>(
  handler: (req: AuthenticatedRequest, ...args: T) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Get authenticated user with request-scoped caching
      const { supabaseUser, dbUser } = await getAuthenticatedUser(req);

      // Extend the request object with user data
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.user = {
        supabaseUser,
        dbUser,
        userId: supabaseUser.id,
      };

      // Call the original handler with authenticated request
      return await handler(authenticatedReq, ...args);
    } catch (error) {
      console.error("Authentication error:", error);

      // Return standardized auth error response
      return NextResponse.json(
        {
          success: false,
          error:
            error instanceof Error && error.message === "Unauthorized"
              ? "Authentication required"
              : "Authentication failed",
        },
        { status: 401 }
      );
    }
  };
}

/**
 * Authentication wrapper for streaming endpoints (Server-Sent Events)
 * Returns raw Response objects instead of NextResponse for streaming compatibility
 */
export function withAuthStreaming<T extends any[]>(
  handler: (req: AuthenticatedRequest, ...args: T) => Promise<Response>
) {
  return async (req: NextRequest, ...args: T): Promise<Response> => {
    try {
      // Get authenticated user with request-scoped caching
      const { supabaseUser, dbUser } = await getAuthenticatedUser(req);

      // Extend the request object with user data
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.user = {
        supabaseUser,
        dbUser,
        userId: supabaseUser.id,
      };

      // Call the original handler with authenticated request
      return await handler(authenticatedReq, ...args);
    } catch (error) {
      console.error("Authentication error:", error);

      // Return standardized auth error response as raw Response
      return new Response(
        JSON.stringify({
          success: false,
          error:
            error instanceof Error && error.message === "Unauthorized"
              ? "Authentication required"
              : "Authentication failed",
        }),
        {
          status: 401,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }
  };
}

/**
 * Optional authentication wrapper - doesn't throw if user is not authenticated
 * Useful for endpoints that work with or without authentication
 */
export function withOptionalAuth<T extends any[]>(
  handler: (
    req: AuthenticatedRequest | NextRequest,
    ...args: T
  ) => Promise<NextResponse>
) {
  return async (req: NextRequest, ...args: T): Promise<NextResponse> => {
    try {
      // Try to get authenticated user
      const { supabaseUser, dbUser } = await getAuthenticatedUser(req);

      // Extend the request object with user data
      const authenticatedReq = req as AuthenticatedRequest;
      authenticatedReq.user = {
        supabaseUser,
        dbUser,
        userId: supabaseUser.id,
      };

      return await handler(authenticatedReq, ...args);
    } catch (error) {
      // If authentication fails, continue without user data
      return await handler(req, ...args);
    }
  };
}

/**
 * Utility function to extract user data from authenticated request
 */
export function getUserFromRequest(req: AuthenticatedRequest) {
  return req.user;
}

/**
 * Type guard to check if request is authenticated
 */
export function isAuthenticatedRequest(
  req: NextRequest | AuthenticatedRequest
): req is AuthenticatedRequest {
  return "user" in req && req.user !== undefined;
}
