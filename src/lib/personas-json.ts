import fs from 'fs';
import path from 'path';

interface PersonaData {
  id: string;
  name: string;
  generatedImageUrl: string;
  createdAt: string;
  status: string;
}

interface PersonasFile {
  personas: PersonaData[];
}

const PERSONAS_FILE_PATH = path.join(process.cwd(), 'public', 'personas.json');

export async function addPersonaToFile(persona: PersonaData): Promise<void> {
  try {
    // Read existing file
    let existingData: PersonasFile = { personas: [] };
    
    if (fs.existsSync(PERSONAS_FILE_PATH)) {
      const fileContent = fs.readFileSync(PERSONAS_FILE_PATH, 'utf8');
      existingData = JSON.parse(fileContent);
    }
    
    // Add new persona (avoid duplicates)
    const existingIndex = existingData.personas.findIndex(p => p.id === persona.id);
    if (existingIndex >= 0) {
      existingData.personas[existingIndex] = persona;
    } else {
      existingData.personas.unshift(persona); // Add to beginning
    }
    
    // Write back to file
    fs.writeFileSync(PERSONAS_FILE_PATH, JSON.stringify(existingData, null, 2));
    console.log(`Added persona ${persona.name} to personas.json`);
  } catch (error) {
    console.error('Error adding persona to file:', error);
    throw error;
  }
}

export async function getPersonasFromFile(): Promise<PersonaData[]> {
  try {
    if (!fs.existsSync(PERSONAS_FILE_PATH)) {
      return [];
    }
    
    const fileContent = fs.readFileSync(PERSONAS_FILE_PATH, 'utf8');
    const data: PersonasFile = JSON.parse(fileContent);
    return data.personas || [];
  } catch (error) {
    console.error('Error reading personas file:', error);
    return [];
  }
}