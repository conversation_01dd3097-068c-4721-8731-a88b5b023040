/**
 * Utility functions for image processing and manipulation
 */

/**
 * Convert a file to base64 string
 */
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = () => reject(new Error('Failed to read file'));
    reader.readAsDataURL(file);
  });
};

/**
 * Convert image URL to base64
 */
export const imageUrlToBase64 = async (url: string): Promise<string> => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to convert image to base64'));
        }
      };
      reader.onerror = () => reject(new Error('Failed to read image'));
      reader.readAsDataURL(blob);
    });
  } catch (error) {
    throw new Error(`Failed to convert image URL to base64: ${error}`);
  }
};

/**
 * Load an image from URL and return HTMLImageElement
 */
export const loadImage = (src: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = src;
  });
};

/**
 * Calculate optimal image dimensions to fit within container
 */
export const calculateImageDimensions = (
  imageWidth: number,
  imageHeight: number,
  maxWidth: number,
  maxHeight: number
) => {
  const aspectRatio = imageWidth / imageHeight;
  
  let newWidth = maxWidth;
  let newHeight = maxWidth / aspectRatio;
  
  if (newHeight > maxHeight) {
    newHeight = maxHeight;
    newWidth = maxHeight * aspectRatio;
  }
  
  const x = (maxWidth - newWidth) / 2;
  const y = (maxHeight - newHeight) / 2;
  
  return {
    width: newWidth,
    height: newHeight,
    x,
    y,
  };
};

/**
 * Create a canvas element with specified dimensions
 */
export const createCanvas = (width: number, height: number): HTMLCanvasElement => {
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  return canvas;
};

/**
 * Download image from URL
 */
export const downloadImage = async (url: string, filename: string = 'image.jpg') => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.statusText}`);
    }
    const blob = await response.blob();
    const objectUrl = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = objectUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(objectUrl);
  } catch (error) {
    throw new Error(`Failed to download image: ${error}`);
  }
};

/**
 * Validate if file is a valid image
 */
export const isValidImageFile = (file: File): boolean => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  return validTypes.includes(file.type);
};

/**
 * Get image dimensions from file
 */
export const getImageDimensions = (file: File): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.width, height: img.height });
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Resize image to fit within max dimensions while maintaining aspect ratio
 */
export const resizeImage = (
  file: File,
  maxWidth: number,
  maxHeight: number,
  quality: number = 0.9
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      reject(new Error('Failed to get canvas context'));
      return;
    }
    
    const img = new Image();
    img.onload = () => {
      const { width, height } = calculateImageDimensions(
        img.width,
        img.height,
        maxWidth,
        maxHeight
      );
      
      canvas.width = width;
      canvas.height = height;
      
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create blob'));
          }
        },
        'image/jpeg',
        quality
      );
    };
    
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

/**
 * Convert canvas to data URL with specified format and quality
 */
export const canvasToDataUrl = (
  canvas: HTMLCanvasElement,
  format: string = 'image/png',
  quality: number = 1.0
): string => {
  return canvas.toDataURL(format, quality);
};

/**
 * Create a transparent mask canvas from stroke data
 */
export const createMaskCanvas = (
  strokes: Array<{ points: number[]; tool: string; size: number }>,
  imageWidth: number,
  imageHeight: number,
  imagePosition: { x: number; y: number },
  imageSize: { width: number; height: number }
): HTMLCanvasElement => {
  const canvas = createCanvas(imageWidth, imageHeight);
  const ctx = canvas.getContext('2d');
  
  if (!ctx) {
    throw new Error('Failed to create canvas context');
  }
  
  // Fill with black (masked areas will be white)
  ctx.fillStyle = 'black';
  ctx.fillRect(0, 0, imageWidth, imageHeight);
  
  // Calculate scale factors
  const scaleX = imageWidth / imageSize.width;
  const scaleY = imageHeight / imageSize.height;
  
  // Draw mask strokes
  strokes.forEach((stroke) => {
    if (stroke.tool === 'eraser') {
      ctx.globalCompositeOperation = 'destination-out';
    } else {
      ctx.globalCompositeOperation = 'source-over';
      ctx.fillStyle = 'white';
    }
    
    ctx.lineWidth = stroke.size * scaleX;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    if (stroke.points.length >= 4) {
      ctx.beginPath();
      
      // Adjust points for image position and scale
      const adjustedPoints = stroke.points.map((point, index) => {
        if (index % 2 === 0) {
          // X coordinate
          return (point - imagePosition.x) * scaleX;
        } else {
          // Y coordinate
          return (point - imagePosition.y) * scaleY;
        }
      });
      
      ctx.moveTo(adjustedPoints[0], adjustedPoints[1]);
      for (let i = 2; i < adjustedPoints.length; i += 2) {
        ctx.lineTo(adjustedPoints[i], adjustedPoints[i + 1]);
      }
      
      ctx.stroke();
      
      // Also fill the path for better coverage
      if (stroke.tool !== 'eraser') {
        ctx.fill();
      }
    }
  });
  
  return canvas;
};