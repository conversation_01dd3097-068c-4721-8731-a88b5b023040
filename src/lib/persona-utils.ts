/**
 * Utility functions for persona creation and fine-tuning
 */

import <PERSON><PERSON><PERSON><PERSON> from "jszip";

export interface ImageFile {
  name: string;
  data: <PERSON><PERSON><PERSON>;
  mimeType: string;
}

export interface ProcessedImage {
  filename: string;
  buffer: ArrayBuffer;
}

/**
 * Validate image file for fine-tuning
 */
export function validateImageForFinetune(file: File): {
  valid: boolean;
  error?: string;
} {
  // Check file size (max 10MB per image)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return { valid: false, error: "Image must be smaller than 10MB" };
  }

  // Check minimum file size (at least 1KB to ensure it's a real image)
  const minSize = 1024; // 1KB
  if (file.size < minSize) {
    return { valid: false, error: "File size too small - may be corrupted" };
  }

  // Check file type
  const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];
  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: "Only JPEG, PNG, and WebP images are supported",
    };
  }

  // Check file extension matches MIME type
  const extension = file.name.split(".").pop()?.toLowerCase();
  const validExtensions = ["jpg", "jpeg", "png", "webp"];
  if (!extension || !validExtensions.includes(extension)) {
    return {
      valid: false,
      error:
        "File must have a valid image extension (.jpg, .jpeg, .png, .webp)",
    };
  }

  return { valid: true };
}

/**
 * Validate multiple images for fine-tuning
 */
export function validateImagesForFinetune(files: File[]): {
  valid: boolean;
  error?: string;
} {
  // Check minimum number of images for character training
  if (files.length < 8) {
    return {
      valid: false,
      error:
        "For best character training results, please upload at least 8-12 high-quality images of yourself from different angles and poses",
    };
  }

  // Check maximum number of images (optimal range for character training)
  if (files.length > 25) {
    return {
      valid: false,
      error:
        "Maximum 25 images recommended for character training. Too many images can reduce training quality.",
    };
  }

  // Validate each image
  for (const file of files) {
    const validation = validateImageForFinetune(file);
    if (!validation.valid) {
      return validation;
    }
  }

  return { valid: true };
}

/**
 * Process image file to ensure it meets fine-tuning requirements
 * For now, just pass through the original image since BFL can handle resizing
 * TODO: Add server-side image processing with sharp or similar library
 */
export async function processImageForFinetune(
  file: File
): Promise<ProcessedImage> {
  const buffer = await file.arrayBuffer();

  // Generate a clean filename
  const extension = file.name.split(".").pop()?.toLowerCase() || "jpg";
  const cleanName = file.name
    .replace(/[^a-zA-Z0-9.-]/g, "_")
    .replace(/_{2,}/g, "_")
    .toLowerCase();

  const filename = `${cleanName.split(".")[0]}.${extension}`;

  return {
    filename,
    buffer,
  };
}

/**
 * Create a ZIP file containing training images for Flux fine-tuning
 */
export async function createTrainingZip(files: File[]): Promise<string> {
  console.log(`Creating training ZIP with ${files.length} files`);
  const zip = new JSZip();

  // Process each image and add to ZIP with simple naming (following BFL best practices)
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    console.log(
      `Processing file ${i + 1}: ${file.name} (${file.size} bytes, ${
        file.type
      })`
    );

    const processedImage = await processImageForFinetune(file);

    // Use simple sequential naming as recommended by BFL/Replicate guides
    const extension = processedImage.filename.split(".").pop() || "jpg";
    const filename = `${i + 1}.${extension}`;
    console.log(
      `Adding to ZIP: ${filename} (${processedImage.buffer.byteLength} bytes)`
    );
    zip.file(filename, processedImage.buffer);

    // Using auto-captioning - no manual caption files needed
  }

  console.log("Generating ZIP file...");
  // Generate ZIP file as base64
  const zipBlob = await zip.generateAsync({
    type: "base64",
    compression: "DEFLATE",
    compressionOptions: {
      level: 6,
    },
  });

  console.log(`ZIP file generated: ${zipBlob.length} characters (base64)`);
  return zipBlob;
}

/**
 * Generate a unique trigger word for the persona
 */
export function generateTriggerWord(_personaName: string): string {
  // Use consistent trigger word as per finetunning-fix.md guide
  // This ensures better character preservation and consistency
  return "MYCHAR";
}

/**
 * Estimate fine-tuning time based on number of images and settings
 */
export function estimateFinetuningTime(
  imageCount: number,
  iterations: number = 300
): string {
  // Realistic time estimation for Flux fine-tuning: ~0.1-0.15 minutes per iteration
  const baseTimePerIteration = 0.1; // minutes (6 seconds per iteration)
  const totalMinutes = Math.ceil(iterations * baseTimePerIteration);

  // Add some buffer time based on image count
  const bufferTime = Math.ceil(imageCount * 0.5); // 30 seconds per image for processing
  const finalTime = totalMinutes + bufferTime;

  if (finalTime < 60) {
    return `${finalTime} minutes`;
  } else {
    const hours = Math.floor(finalTime / 60);
    const minutes = finalTime % 60;
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours} hours`;
  }
}

/**
 * Get fine-tuning recommendations based on image count
 */
export function getFinetuningRecommendations(_imageCount: number): {
  iterations: number;
  learningRate: number;
  priority: "speed" | "quality" | "high_res_only";
} {
  // Character training recommendations based on BFL best practices
  // Following the finetuning guide defaults exactly
  // Guide shows: iterations=300, learning_rate=0.00001, priority="quality"

  return {
    iterations: 150, // As per finetunning-fix.md guide for character training
    learningRate: 0.0001, // Correct LoRA learning rate (10x higher than full)
    priority: "quality", // Quality priority for better character preservation
  };
}

/**
 * Generate a persona description for the fine-tuning comment
 */
export function generatePersonaDescription(
  personaName: string,
  imageCount: number
): string {
  return `Character fine-tune for ${personaName} (MYCHAR) trained on ${imageCount} high-quality images. This model generates consistent character images using the trigger word MYCHAR. Trained in character mode for optimal persona preservation.`;
}

/**
 * Validate persona name
 */
export function validatePersonaName(name: string): {
  valid: boolean;
  error?: string;
} {
  if (!name || name.trim().length === 0) {
    return { valid: false, error: "Persona name is required" };
  }

  if (name.length < 2) {
    return {
      valid: false,
      error: "Persona name must be at least 2 characters long",
    };
  }

  if (name.length > 50) {
    return {
      valid: false,
      error: "Persona name must be less than 50 characters",
    };
  }

  // Check for valid characters (letters, numbers, spaces, hyphens, underscores)
  const validNameRegex = /^[a-zA-Z0-9\s\-_]+$/;
  if (!validNameRegex.test(name)) {
    return {
      valid: false,
      error:
        "Persona name can only contain letters, numbers, spaces, hyphens, and underscores",
    };
  }

  return { valid: true };
}

/**
 * Get status display text for persona status
 */
export function getPersonaStatusDisplay(status: string): {
  text: string;
  color: string;
} {
  switch (status) {
    case "pending":
      return { text: "Preparing", color: "text-yellow-600" };
    case "training":
      return { text: "Training", color: "text-blue-600" };
    case "ready":
      return { text: "Ready", color: "text-green-600" };
    case "error":
      return { text: "Failed", color: "text-red-600" };
    default:
      return { text: "Unknown", color: "text-gray-600" };
  }
}

/**
 * Check if persona can be used for generation
 */
export function canUsePersona(status: string): boolean {
  return status === "ready";
}
