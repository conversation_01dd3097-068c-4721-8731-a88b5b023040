/**
 * Flux Fine-tuning API utility for persona creation
 * Provides a clean interface for training custom models and generating persona images
 */

export interface FluxFinetuneRequest {
  file_data: string; // Base64 encoded ZIP file
  finetune_comment: string;
  trigger_word?: string;
  mode?: "character" | "product" | "style" | "general";
  iterations?: number;
  learning_rate?: number;
  captioning?: boolean;
  priority?: "speed" | "quality" | "high_res_only";
  finetune_type?: "lora";
  lora_rank?: number;
}

export interface FluxFinetuneResponse {
  finetune_id: string;
  polling_url?: string;
}

export interface FluxFinetuneStatus {
  id: string;
  status: string; // "Ready", "Pending", "Error", "Failed"
  result: Record<string, unknown>;
  progress: number | null;
  details: Record<string, unknown>;
}

export interface FluxFinetuneDetails {
  finetune_details: {
    finetune_id: string;
    finetune_comment: string;
    trigger_word: string;
    mode: string;
    iterations: number;
    learning_rate: number;
    captioning: boolean;
    priority: string;
    finetune_type: string;
    lora_rank: number;
    created_at: string;
    status: string;
  };
}

export interface FluxFinetuneInferenceRequest {
  prompt: string;
  finetune_id: string;
  finetune_strength?: number;
  aspect_ratio?: string;
  seed?: number;
  safety_tolerance?: number;
  output_format?: "jpeg" | "png";
}

export interface FluxFinetuneFillRequest {
  finetune_id: string;
  image: string; // base64 encoded image
  mask: string; // base64 encoded mask
  prompt?: string;
  steps?: number;
  guidance?: number;
  finetune_strength?: number;
  output_format?: "jpeg" | "png";
  safety_tolerance?: number;
  prompt_upsampling?: boolean; // Disable for more predictable results with finetunes
}

export interface FluxFinetuneInferenceResponse {
  id: string;
  polling_url?: string; // May not be available for legacy endpoints
}

export class FluxFinetuneAPIError extends Error {
  constructor(
    message: string,
    public status?: number,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = "FluxFinetuneAPIError";
  }
}

export class FluxFinetuneAPI {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, region: "us" | "eu" = "us") {
    this.apiKey = apiKey;
    // Use region-specific endpoints for fine-tuning
    this.baseUrl =
      region === "us" ? "https://api.us1.bfl.ai" : "https://api.eu1.bfl.ai";
  }

  /**
   * Generate trigger word from persona name (optimized for face accuracy)
   * Following latest best practice: use person's actual name for better face consistency
   */
  private generateTriggerWord(personaName: string): string {
    // Use the person's actual name as trigger word for better face consistency
    const cleanName = personaName
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, "")
      .replace(/\s+/g, ""); // Remove spaces but keep the actual name

    return cleanName; // e.g., "john", "sarah", "johnsmith"
  }

  /**
   * Get the trigger word for a specific finetune by fetching its details
   */
  private async getTriggerWordForFinetune(finetuneId: string): Promise<string> {
    try {
      const details = await this.getFinetuneDetails(finetuneId);
      return details.finetune_details.trigger_word || "person";
    } catch {
      console.warn(
        `Could not fetch trigger word for ${finetuneId}, using default`
      );
      return "person"; // Fallback trigger word
    }
  }

  /**
   * Submit a fine-tuning request following Flux best practices
   */
  async submitFinetune(
    request: FluxFinetuneRequest
  ): Promise<FluxFinetuneResponse> {
    // Generate a contextual trigger word based on persona name if not provided
    const defaultTriggerWord =
      request.trigger_word ||
      this.generateTriggerWord(request.finetune_comment);

    // Prepare the payload following Flux documentation best practices (optimized for face accuracy)
    const payload = {
      file_data: request.file_data,
      finetune_comment: request.finetune_comment,
      trigger_word: defaultTriggerWord, // Use contextual trigger word instead of generic "TOK"
      mode: request.mode || "character", // Use character mode for persona training
      iterations: request.iterations || 400, // Increased for better face detail learning (was 300)
      learning_rate: request.learning_rate || 0.00008, // Optimized for more stable face feature learning (was 0.0001)
      captioning: request.captioning !== undefined ? request.captioning : true,
      priority: request.priority || "quality", // Use quality priority for better results
      finetune_type: "lora", // Use LoRA for efficiency
      lora_rank: request.lora_rank || 32, // Keep 32 for maximum face detail preservation
    };

    console.log(
      "Submitting optimized fine-tune request to:",
      `${this.baseUrl}/v1/finetune`
    );
    console.log("Payload keys:", Object.keys(payload));
    console.log("File data length:", payload.file_data?.length || 0);
    console.log("Trigger word:", payload.trigger_word);
    console.log("Mode:", payload.mode);
    console.log(
      "Iterations (optimized for face accuracy):",
      payload.iterations
    );
    console.log(
      "Learning rate (optimized for stability):",
      payload.learning_rate
    );
    console.log("Captioning:", payload.captioning);
    console.log("Priority:", payload.priority);
    console.log("Finetune type:", payload.finetune_type);
    console.log("LoRA rank:", payload.lora_rank);

    const response = await fetch(`${this.baseUrl}/v1/finetune`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Key": this.apiKey,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      let errorData;
      try {
        errorData = JSON.parse(errorText);
      } catch {
        errorData = { message: errorText };
      }

      console.error("Fine-tune submission failed:", {
        status: response.status,
        statusText: response.statusText,
        error: errorData,
      });

      throw new FluxFinetuneAPIError(
        `Failed to submit fine-tune request: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    console.log("Fine-tune submission successful:", data);
    return data as FluxFinetuneResponse;
  }

  /**
   * Check fine-tuning status using get_result endpoint
   */
  async getFinetuneStatus(finetuneId: string): Promise<FluxFinetuneStatus> {
    // CORRECT: Use get_result endpoint for status checking as per BFL docs
    const url = new URL(`${this.baseUrl}/v1/get_result`);
    url.searchParams.append("id", finetuneId);

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-key": this.apiKey,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to get fine-tune status: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxFinetuneStatus;
  }

  /**
   * Get fine-tune details
   */
  async getFinetuneDetails(finetuneId: string): Promise<FluxFinetuneDetails> {
    const url = new URL(`${this.baseUrl}/v1/finetune_details`);
    url.searchParams.append("finetune_id", finetuneId);

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-key": this.apiKey,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to get fine-tune details: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxFinetuneDetails;
  }

  /**
   * List all user's fine-tunes
   */
  async listFinetunes(): Promise<{ finetunes: Record<string, unknown>[] }> {
    const response = await fetch(`${this.baseUrl}/v1/my_finetunes`, {
      method: "GET",
      headers: {
        accept: "application/json",
        "x-key": this.apiKey,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to list fine-tunes: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data;
  }

  /**
   * Test generate with a specific finetune_id using the CORRECT BFL approach
   */
  async testGenerateWithModel(
    finetuneId: string,
    prompt: string = "professional headshot portrait, looking directly at camera, clean background",
    triggerWord?: string,
    options?: {
      aspect_ratio?: string;
      width?: number;
      height?: number;
      finetune_strength?: number;
      safety_tolerance?: number;
      output_format?: "jpeg" | "png";
      prompt_upsampling?: boolean;
    }
  ): Promise<string> {
    // Get the trigger word from finetune details if not provided
    const actualTriggerWord =
      triggerWord || (await this.getTriggerWordForFinetune(finetuneId));

    // Use the actual trigger word in the prompt following Flux best practices
    const enhancedPrompt = `${actualTriggerWord}, a professional portrait of ${actualTriggerWord}, ${prompt}`;

    console.log("Testing finetune_id:", finetuneId);
    console.log("Using trigger word:", actualTriggerWord);
    console.log("Test prompt:", enhancedPrompt);

    // CORRECT APPROACH: Use finetuned endpoint with finetune_id parameter
    const response = await fetch(
      `${this.baseUrl}/v1/flux-pro-1.1-ultra-finetuned`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Key": this.apiKey,
        },
        body: JSON.stringify({
          finetune_id: finetuneId, // CORRECT: Use finetune_id, not model
          prompt: enhancedPrompt,
          aspect_ratio: options?.aspect_ratio || "1:1",
          output_format: options?.output_format || "jpeg",
          safety_tolerance: options?.safety_tolerance || 2,
          width: options?.width || 1024,
          height: options?.height || 1024,
          steps: 28,
          guidance: 3.5,
          finetune_strength: options?.finetune_strength || 1.0, // Control finetune influence
          prompt_upsampling: options?.prompt_upsampling || false,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to test generate: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    const inferenceId = data.id;

    // Poll for completion and return image URL
    return await this.waitForInferenceCompletion(inferenceId);
  }

  /**
   * Generate image using fine-tuned model with enhanced character preservation
   */
  async generateWithFinetune(
    request: FluxFinetuneInferenceRequest & {
      finetune_id: string;
      trigger_word?: string;
    }
  ): Promise<FluxFinetuneInferenceResponse> {
    // Get the actual trigger word for this finetune
    const actualTriggerWord =
      request.trigger_word ||
      (await this.getTriggerWordForFinetune(request.finetune_id));

    // Enhanced prompt using the actual trigger word following Flux best practices
    const enhancedPrompt = `${actualTriggerWord}, a professional portrait of ${actualTriggerWord}, ${request.prompt}`;

    console.log("Original prompt:", request.prompt);
    console.log("Using trigger word:", actualTriggerWord);
    console.log("Enhanced prompt:", enhancedPrompt);
    console.log("Using finetune_id:", request.finetune_id);

    // CORRECT APPROACH: Use finetuned endpoint with finetune_id parameter
    const response = await fetch(
      `${this.baseUrl}/v1/flux-pro-1.1-ultra-finetuned`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Key": this.apiKey,
        },
        body: JSON.stringify({
          finetune_id: request.finetune_id, // CORRECT: Use finetune_id parameter
          prompt: enhancedPrompt,
          aspect_ratio: request.aspect_ratio || "1:1",
          output_format: request.output_format || "jpeg",
          safety_tolerance: request.safety_tolerance || 2,
          width: 1024,
          height: 1024,
          steps: 28,
          guidance: 3.5,
          finetune_strength: 1.0, // Control finetune influence
          seed: request.seed,
        }),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to generate with fine-tune: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data as FluxFinetuneInferenceResponse;
  }

  /**
   * Poll for inference completion and return image URL
   */
  async waitForInferenceCompletion(
    inferenceId: string,
    options: {
      maxWaitTime?: number; // Maximum time to wait in milliseconds (default: 5 minutes)
      pollInterval?: number; // Polling interval in milliseconds (default: 2 seconds)
    } = {}
  ): Promise<string> {
    const { maxWaitTime = 5 * 60 * 1000, pollInterval = 2000 } = options;
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const result = await this.getInferenceResult(inferenceId);

      switch (result.status) {
        case "Ready":
          if (
            !result.result ||
            typeof result.result !== "object" ||
            !("sample" in result.result)
          ) {
            throw new FluxFinetuneAPIError(
              "Inference completed but no image URL returned"
            );
          }
          return result.result.sample as string;

        case "Error":
          throw new FluxFinetuneAPIError(
            "Inference failed",
            undefined,
            result.details as Record<string, unknown>
          );

        case "Request Moderated":
          throw new FluxFinetuneAPIError(
            "Inference request was moderated",
            400
          );

        case "Content Moderated":
          throw new FluxFinetuneAPIError(
            "Generated content was moderated",
            400
          );

        case "Task not found":
          throw new FluxFinetuneAPIError("Inference task not found", 404);

        case "Pending":
          // Continue polling
          await new Promise((resolve) => setTimeout(resolve, pollInterval));
          break;

        default:
          throw new FluxFinetuneAPIError(
            `Unknown inference status: ${result.status}`
          );
      }
    }

    throw new FluxFinetuneAPIError("Inference timed out", 408);
  }

  /**
   * Get inference result by ID
   */
  async getInferenceResult(
    inferenceId: string
  ): Promise<Record<string, unknown>> {
    const url = new URL(`${this.baseUrl}/v1/get_result`);
    url.searchParams.append("id", inferenceId);

    const response = await fetch(url.toString(), {
      method: "GET",
      headers: {
        accept: "application/json",
        "X-Key": this.apiKey,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to get inference result: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    return data;
  }

  /**
   * Poll for fine-tuning completion
   */
  async waitForFinetuneCompletion(
    finetuneId: string,
    options: {
      maxWaitTime?: number; // Maximum time to wait in milliseconds (default: 30 minutes)
      pollInterval?: number; // Polling interval in milliseconds (default: 10 seconds)
    } = {}
  ): Promise<FluxFinetuneStatus> {
    const { maxWaitTime = 30 * 60 * 1000, pollInterval = 10000 } = options;

    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.getFinetuneStatus(finetuneId);

      // get_result returns status directly
      const finetuneStatus = status.status;

      switch (finetuneStatus) {
        case "Ready":
          console.log(
            `✅ Fine-tuning completed successfully! Finetune ID: ${finetuneId}`
          );
          return status;

        case "Error":
        case "Failed":
          throw new FluxFinetuneAPIError("Fine-tuning failed", undefined, {
            status,
          });

        case "Request Moderated":
          throw new FluxFinetuneAPIError(
            "Fine-tuning request was moderated. Please check your training images.",
            400
          );

        case "queued":
        case "running":
        case "Pending":
          // Continue polling
          break;

        case "Content Moderated":
          throw new FluxFinetuneAPIError(
            "Fine-tuning content was moderated. Please use appropriate training images.",
            400
          );

        case "Task not found":
          throw new FluxFinetuneAPIError("Fine-tuning task not found", 404);

        case "Pending":
          // Continue polling
          await new Promise((resolve) => setTimeout(resolve, pollInterval));
          break;

        default:
          throw new FluxFinetuneAPIError(`Unknown status: ${finetuneStatus}`);
      }
    }

    throw new FluxFinetuneAPIError("Fine-tuning timed out", 408);
  }

  /**
   * Download image from URL and return as base64
   */
  async downloadImageAsBase64(imageUrl: string): Promise<string> {
    const response = await fetch(imageUrl);

    if (!response.ok) {
      throw new FluxFinetuneAPIError(
        `Failed to download image: ${response.status} ${response.statusText}`,
        response.status
      );
    }

    const arrayBuffer = await response.arrayBuffer();
    const base64 = Buffer.from(arrayBuffer).toString("base64");

    return base64;
  }

  /**
   * Delete a fine-tune
   */
  async deleteFinetune(finetuneId: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/v1/delete_finetune`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Key": this.apiKey,
      },
      body: JSON.stringify({
        finetune_id: finetuneId,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new FluxFinetuneAPIError(
        `Failed to delete fine-tune: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }
  }

  /**
   * Fill (inpaint) using fine-tuned model - optimized for face swapping
   * Uses enhanced prompting and optimal parameters for face swapping scenarios
   */
  async fillWithFinetune(request: FluxFinetuneFillRequest): Promise<string> {
    console.log("Using FLUX Fill with finetune_id:", request.finetune_id);
    console.log("Base URL:", this.baseUrl);
    console.log("Image size (base64):", request.image.length, "characters");
    console.log("Mask size (base64):", request.mask.length, "characters");

    // Optimize parameters for face swapping based on BFL documentation
    const payload = {
      finetune_id: request.finetune_id,
      image: request.image,
      mask: request.mask,
      prompt: request.prompt || "", // Should include trigger word for best results
      steps: request.steps || 50, // Good balance of quality and speed
      guidance: request.guidance || 7.5, // Moderate guidance for natural results
      finetune_strength: request.finetune_strength || 1.0, // Full strength for persona consistency
      output_format: request.output_format || "jpeg",
      safety_tolerance: request.safety_tolerance || 2,
      prompt_upsampling: false, // Disable for more predictable results with finetunes
    };

    console.log("Fill request payload:", {
      ...payload,
      image: `[${payload.image.length} chars]`,
      mask: `[${payload.mask.length} chars]`,
    });

    // Use the correct Fill endpoint for finetuned models
    const response = await fetch(
      `${this.baseUrl}/v1/flux-pro-1.0-fill-finetuned`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Key": this.apiKey,
        },
        body: JSON.stringify(payload),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error("Fill error details:", errorData);
      console.error("Response status:", response.status, response.statusText);
      throw new FluxFinetuneAPIError(
        `Failed to fill with fine-tune: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }

    const data = await response.json();
    const inferenceId = data.id;

    // Poll for completion and return image URL
    return await this.waitForInferenceCompletion(inferenceId);
  }
}

/**
 * Create a Flux Fine-tune API instance with the API key from environment variables
 */
export function createFluxFinetuneAPI(
  region: "us" | "eu" = "us"
): FluxFinetuneAPI {
  const apiKey = process.env.BFL_API_KEY;

  if (!apiKey) {
    throw new FluxFinetuneAPIError(
      "BFL_API_KEY environment variable is not set"
    );
  }

  return new FluxFinetuneAPI(apiKey, region);
}
