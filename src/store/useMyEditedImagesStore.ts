import { create } from "zustand";

export interface EditedImage {
  id: string;
  originalImageUrl: string;
  editedImageUrl: string;
  sourceType: "generated" | "recreated" | "faceswap" | "background_removal";
  sourceId: string;
  prompt: string;
  personaUsed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Pagination {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
}

interface MyEditedImagesState {
  // Data
  editedImages: EditedImage[];
  pagination: Pagination | null;

  // UI State
  loading: boolean;
  error: string | null;

  // Filters
  searchQuery: string;
  sourceTypeFilter: string;
  sortBy: "createdAt" | "sourceType" | "prompt";
  sortOrder: "asc" | "desc";

  // Actions
  fetchEditedImages: (page?: number) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setSourceTypeFilter: (sourceType: string) => void;
  setSortBy: (sortBy: "createdAt" | "sourceType" | "prompt") => void;
  setSortOrder: (sortOrder: "asc" | "desc") => void;
  deleteEditedImage: (id: string) => Promise<boolean>;
  refreshEditedImages: () => Promise<void>;
  clearFilters: () => void;
  reset: () => void;
}

const initialState = {
  editedImages: [],
  pagination: null,
  loading: false,
  error: null,
  searchQuery: "",
  sourceTypeFilter: "",
  sortBy: "createdAt" as const,
  sortOrder: "desc" as const,
};

export const useMyEditedImagesStore = create<MyEditedImagesState>(
  (set, get) => ({
    ...initialState,

    fetchEditedImages: async (page = 1) => {
      set({ loading: true, error: null });

      try {
        const { searchQuery, sourceTypeFilter, sortBy, sortOrder } = get();

        const params = new URLSearchParams({
          page: page.toString(),
          limit: "12",
          ...(searchQuery && { search: searchQuery }),
          ...(sourceTypeFilter && { sourceType: sourceTypeFilter }),
          sortBy,
          sortOrder,
        });

        const response = await fetch(`/api/my-edited?${params}`);

        if (!response.ok) {
          throw new Error("Failed to fetch edited images");
        }

        const data = await response.json();

        if (data.success) {
          set({
            editedImages: data.data.editedImages,
            pagination: data.data.pagination,
            loading: false,
          });
        } else {
          throw new Error(data.error || "Failed to fetch edited images");
        }
      } catch (error) {
        console.error("Error fetching edited images:", error);
        set({
          error: error instanceof Error ? error.message : "Unknown error",
          loading: false,
        });
      }
    },

    setSearchQuery: (searchQuery) => {
      set({ searchQuery });
    },

    setSourceTypeFilter: (sourceTypeFilter) => {
      set({ sourceTypeFilter });
    },

    setSortBy: (sortBy) => {
      set({ sortBy });
    },

    setSortOrder: (sortOrder) => {
      set({ sortOrder });
    },

    deleteEditedImage: async (id: string) => {
      try {
        const response = await fetch(`/api/my-edited/${id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error("Failed to delete edited image");
        }

        const data = await response.json();

        if (data.success) {
          // Remove from local state
          set((state) => ({
            editedImages: state.editedImages.filter((img) => img.id !== id),
          }));
          return true;
        } else {
          throw new Error(data.error || "Failed to delete edited image");
        }
      } catch (error) {
        console.error("Error deleting edited image:", error);
        set({
          error: error instanceof Error ? error.message : "Unknown error",
        });
        return false;
      }
    },

    refreshEditedImages: async () => {
      const state = get();
      const currentPage = state.pagination?.currentPage || 1;
      await get().fetchEditedImages(currentPage);
    },

    clearFilters: () => {
      set({
        searchQuery: "",
        sourceTypeFilter: "",
        sortBy: "createdAt",
        sortOrder: "desc",
      });
    },

    reset: () => {
      set(initialState);
    },
  })
);
