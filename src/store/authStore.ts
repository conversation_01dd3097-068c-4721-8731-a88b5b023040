// store/authStore.ts
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { User, Session, AuthChangeEvent } from "@supabase/supabase-js";
import { supabase } from "@/src/lib/supabase/client";
import { toast } from "sonner"; // Import toast from sonner

interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  isInitialized: boolean;
  displayName: string | null;
  initializeAuthListener: () => (() => void) | undefined; // Ensure it can return unsubscribe
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  setUser: (u: User | null) => void;
  setSession: (s: Session | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (msg: string | null) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      session: null,
      isLoading: true,
      error: null,
      isInitialized: false,
      displayName: null,

      initializeAuthListener: () => {
        if (get().isInitialized) {
          // Prevent re-initializing if already done
          const {
            data: { subscription },
          } = supabase.auth.onAuthStateChange(
            (_event: AuthChangeEvent, session: Session | null) => {
              set({
                session,
                user: session?.user ?? null,
                isLoading: false,
                isInitialized: true,
                displayName: getDisplayName(session?.user ?? null),
              });
            }
          );
          return () => {
            subscription?.unsubscribe();
          };
        }

        set({ isLoading: true });
        supabase.auth
          .getSession()
          .then(({ data }) => {
            set({
              session: data.session ?? null,
              user: data.session?.user ?? null,
              isLoading: false,
              isInitialized: true,
              displayName: getDisplayName(data.session?.user ?? null),
            });
          })
          .catch(() => {
            set({ isLoading: false, isInitialized: true, displayName: null });
          });

        const {
          data: { subscription },
        } = supabase.auth.onAuthStateChange(
          (_event: AuthChangeEvent, session: Session | null) => {
            set({
              session,
              user: session?.user ?? null,
              isLoading: false,
              isInitialized: true,
              displayName: getDisplayName(session?.user ?? null),
            });
          }
        );

        set({ isInitialized: true });

        return () => {
          subscription?.unsubscribe();
        };
      },

      signInWithGoogle: async () => {
        set({ isLoading: true, error: null });
        try {
          const { error } = await supabase.auth.signInWithOAuth({
            provider: "google",
            options: {
              redirectTo: `${window.location.origin}/auth/callback`,
              queryParams: {
                access_type: "offline",
                prompt: "consent",
              },
              skipBrowserRedirect: false,
            },
          });
          if (error) throw error;
        } catch (err: unknown) {
          let errorMessage = "An unknown error occurred during Google sign-in.";
          if (err instanceof Error) {
            errorMessage = err.message;
          }
          set({ error: errorMessage, isLoading: false }); // Ensure isLoading is false on error
          toast.error(errorMessage);
        }
        // No finally block needed here for isLoading if set in catch
      },

      signOut: async () => {
        set({ isLoading: true, error: null });
        try {
          const { error } = await supabase.auth.signOut();
          if (error) throw error;
          set({ user: null, session: null, isLoading: false });
          toast.success("Successfully signed out.");
          // Perform a hard redirect to the auth page after sign out
          if (typeof window !== "undefined") {
            window.location.href = "/auth";
          }
        } catch (err: unknown) {
          let errorMessage = "An unknown error occurred during sign-out.";
          if (err instanceof Error) {
            errorMessage = err.message;
          }
          set({ error: errorMessage, isLoading: false });
          toast.error(errorMessage);
        }
        // No finally block needed if isLoading is set in all paths
      },

      setUser: (u: User | null) =>
        set({ user: u, displayName: getDisplayName(u) }),
      setSession: (s: Session | null) => set({ session: s }),
      setLoading: (loading: boolean) => set({ isLoading: loading }),
      setError: (msg: string | null) => set({ error: msg }),
    }),
    {
      name: "auth-storage",
      storage: createJSONStorage(() => sessionStorage),
    }
  )
);

function getDisplayName(user: User | null): string | null {
  if (!user) return null;
  const fullName = user.user_metadata?.full_name;
  if (fullName && fullName.trim().length > 0) return fullName;
  const email = user.email;
  if (!email) return null;
  return email.replace(/@.+$/, "");
}
