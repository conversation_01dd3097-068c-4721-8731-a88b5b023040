import { create } from "zustand";

export interface GeneratedThumbnail {
  id: string;
  imageUrl: string;
  prompt: string;
  title?: string;
  tags: string[];
  createdAt: string;
}

interface Thumbnail {
  id: string;
  imageUrl: string;
  prompt: string;
  title?: string;
  tags: string[];
  createdAt: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface MyThumbnailsState {
  thumbnails: Thumbnail[];
  pagination: PaginationInfo | null;
  loading: boolean;
  error: string | null;
  searchQuery: string;
  sortBy: "createdAt" | "title" | "prompt";
  sortOrder: "asc" | "desc";
  currentPage: number;
}

interface MyThumbnailsActions {
  fetchThumbnails: (page?: number) => Promise<void>;
  deleteThumbnail: (id: string) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setSortBy: (sortBy: "createdAt" | "title" | "prompt") => void;
  setSortOrder: (order: "asc" | "desc") => void;
  setCurrentPage: (page: number) => void;
  clearError: () => void;
}

type MyThumbnailsStore = MyThumbnailsState & MyThumbnailsActions;

export const useMyThumbnailsStore = create<MyThumbnailsStore>((set, get) => ({
  // Initial state
  thumbnails: [],
  pagination: null,
  loading: false,
  error: null,
  searchQuery: "",
  sortBy: "createdAt",
  sortOrder: "desc",
  currentPage: 1,

  // Actions
  fetchThumbnails: async (page?: number) => {
    const state = get();
    const targetPage = page ?? state.currentPage;

    // Prevent duplicate requests
    if (state.loading) {
      return;
    }

    set({ loading: true, error: null });

    try {
      const params = new URLSearchParams({
        page: targetPage.toString(),
        limit: "12",
        search: state.searchQuery,
        sortBy: state.sortBy,
        sortOrder: state.sortOrder,
      });

      const response = await fetch(`/api/my-thumbnails?${params}`, {
        headers: {
          "Cache-Control": "max-age=30",
        },
      });
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to fetch thumbnails");
      }

      if (result.success) {
        set({
          thumbnails: result.data.thumbnails,
          pagination: result.data.pagination,
          currentPage: targetPage,
          loading: false,
        });
      } else {
        throw new Error(result.error || "Failed to fetch thumbnails");
      }
    } catch (error: any) {
      set({
        error: error.message,
        loading: false,
      });
    }
  },

  deleteThumbnail: async (id: string) => {
    set({ loading: true, error: null });

    try {
      const response = await fetch(`/api/my-thumbnails/${id}`, {
        method: "DELETE",
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to delete thumbnail");
      }

      if (result.success) {
        // Remove the deleted thumbnail from the current list
        const state = get();
        const updatedThumbnails = state.thumbnails.filter(
          (thumb) => thumb.id !== id
        );

        set({
          thumbnails: updatedThumbnails,
          loading: false,
        });

        // If the current page is empty after deletion and it's not page 1, go to previous page
        if (updatedThumbnails.length === 0 && state.currentPage > 1) {
          get().fetchThumbnails(state.currentPage - 1);
        } else if (updatedThumbnails.length === 0) {
          // If we're on page 1 and it's empty, refresh to get updated pagination
          get().fetchThumbnails(1);
        }
      } else {
        throw new Error(result.error || "Failed to delete thumbnail");
      }
    } catch (error: any) {
      set({
        error: error.message,
        loading: false,
      });
    }
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query, currentPage: 1 });
  },

  setSortBy: (sortBy: "createdAt" | "title" | "prompt") => {
    set({ sortBy, currentPage: 1 });
  },

  setSortOrder: (order: "asc" | "desc") => {
    set({ sortOrder: order, currentPage: 1 });
  },

  setCurrentPage: (page: number) => {
    set({ currentPage: page });
  },

  clearError: () => {
    set({ error: null });
  },
}));
