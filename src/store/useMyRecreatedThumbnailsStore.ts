import { create } from "zustand";

export interface RecreatedThumbnail {
  id: string;
  imageUrl: string;
  sourceType: "youtube_url" | "upload_file";
  prompt: string;
  personaUsed: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

interface MyRecreatedThumbnailsState {
  // State
  thumbnails: RecreatedThumbnail[];
  pagination: PaginationInfo | null;
  loading: boolean;
  error: string | null;

  // Filters
  searchQuery: string;
  sourceTypeFilter: string;
  personaFilter: string;
  sortBy: string;
  sortOrder: string;

  // Actions
  setThumbnails: (thumbnails: RecreatedThumbnail[]) => void;
  setPagination: (pagination: PaginationInfo) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Filter actions
  setSearchQuery: (query: string) => void;
  setSourceTypeFilter: (sourceType: string) => void;
  setPersonaFilter: (persona: string) => void;
  setSortBy: (sortBy: string) => void;
  setSortOrder: (sortOrder: string) => void;

  // API actions
  fetchThumbnails: (page?: number) => Promise<void>;
  deleteThumbnail: (id: string) => Promise<boolean>;
  refreshThumbnails: () => Promise<void>;

  // Utility actions
  clearFilters: () => void;
  reset: () => void;
}

const initialState = {
  thumbnails: [],
  pagination: null,
  loading: false,
  error: null,
  searchQuery: "",
  sourceTypeFilter: "",
  personaFilter: "",
  sortBy: "createdAt",
  sortOrder: "desc",
};

export const useMyRecreatedThumbnailsStore = create<MyRecreatedThumbnailsState>(
  (set, get) => ({
    ...initialState,

    // Basic setters
    setThumbnails: (thumbnails) => set({ thumbnails }),
    setPagination: (pagination) => set({ pagination }),
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error }),

    // Filter setters
    setSearchQuery: (searchQuery) => set({ searchQuery }),
    setSourceTypeFilter: (sourceTypeFilter) => set({ sourceTypeFilter }),
    setPersonaFilter: (personaFilter) => set({ personaFilter }),
    setSortBy: (sortBy) => set({ sortBy }),
    setSortOrder: (sortOrder) => set({ sortOrder }),

    // Fetch thumbnails with current filters
    fetchThumbnails: async (page = 1) => {
      const state = get();

      // Prevent duplicate requests
      if (state.loading) {
        return;
      }

      set({ loading: true, error: null });

      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: "12",
          sortBy: state.sortBy,
          sortOrder: state.sortOrder,
        });

        if (state.searchQuery) {
          params.append("search", state.searchQuery);
        }
        if (state.sourceTypeFilter) {
          params.append("sourceType", state.sourceTypeFilter);
        }
        if (state.personaFilter) {
          params.append("personaUsed", state.personaFilter);
        }

        const response = await fetch(`/api/my-recreated?${params}`, {
          headers: {
            "Cache-Control": "max-age=30",
          },
        });
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch thumbnails");
        }

        if (data.success) {
          set({
            thumbnails: data.data.thumbnails,
            pagination: data.data.pagination,
            loading: false,
          });
        } else {
          throw new Error(data.error || "Failed to fetch thumbnails");
        }
      } catch (error) {
        console.error("Error fetching recreated thumbnails:", error);
        set({
          error: error instanceof Error ? error.message : "Unknown error",
          loading: false,
        });
      }
    },

    // Delete a thumbnail
    deleteThumbnail: async (id: string) => {
      try {
        const response = await fetch(`/api/my-recreated/${id}`, {
          method: "DELETE",
        });
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to delete thumbnail");
        }

        if (data.success) {
          // Remove from local state
          const state = get();
          const updatedThumbnails = state.thumbnails.filter(
            (thumbnail) => thumbnail.id !== id
          );
          set({ thumbnails: updatedThumbnails });

          // If this was the last item on the page and we're not on page 1,
          // go to the previous page
          if (
            updatedThumbnails.length === 0 &&
            state.pagination &&
            state.pagination.currentPage > 1
          ) {
            await get().fetchThumbnails(state.pagination.currentPage - 1);
          } else {
            // Otherwise, refresh the current page to update counts
            await get().refreshThumbnails();
          }

          return true;
        } else {
          throw new Error(data.error || "Failed to delete thumbnail");
        }
      } catch (error) {
        console.error("Error deleting thumbnail:", error);
        set({
          error: error instanceof Error ? error.message : "Unknown error",
        });
        return false;
      }
    },

    // Refresh current page
    refreshThumbnails: async () => {
      const state = get();
      const currentPage = state.pagination?.currentPage || 1;
      await get().fetchThumbnails(currentPage);
    },

    // Clear all filters
    clearFilters: () => {
      set({
        searchQuery: "",
        sourceTypeFilter: "",
        personaFilter: "",
        sortBy: "createdAt",
        sortOrder: "desc",
      });
    },

    // Reset entire store
    reset: () => {
      set(initialState);
    },
  })
);
