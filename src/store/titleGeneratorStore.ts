"use client";

import { create } from "zustand";
import { v4 as uuidv4 } from "uuid";
import { persist, createJSONStorage } from "zustand/middleware";

export interface TitleOption {
  id: string;
  text: string;
  style:
    | "seo"
    | "dramatic"
    | "curiosity"
    | "revealed"
    | "snappy"
    | "emphasize"
    | "clickbait"
    | "professional"
    | "creative";
}

export interface MessageType {
  id: string;
  type: "user" | "assistant";
  content: string;
  titles?: TitleOption[];
  timestamp: Date;
  status?: "generating" | "success" | "error";
  error?: string;
}

export interface ChatSession {
  id: string;
  title?: string;
  firstPrompt: string;
  createdAt: Date;
  updatedAt: Date;
}

interface TitleGeneratorState {
  // Current session state
  currentSessionId: string | null;
  currentSession: ChatSession | null;
  prompt: string;
  messages: MessageType[];
  isGenerating: boolean;
  isHydrated: boolean; // Track if store has been hydrated from persistence

  // Session management
  setCurrentSession: (session: ChatSession | null) => void;
  setCurrentSessionId: (sessionId: string | null) => void;
  loadSessionMessages: (messages: MessageType[]) => void;
  setHydrated: (hydrated: boolean) => void;

  // Message management
  setPrompt: (prompt: string) => void;
  addMessage: (message: Omit<MessageType, "id" | "timestamp">) => string;
  updateMessage: (id: string, updates: Partial<MessageType>) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  clearMessages: () => void;

  // Session actions
  createNewSession: () => void;
  resetToNewSession: () => void;
  clearAllState: () => void;
}

export const useTitleGeneratorStore = create<TitleGeneratorState>()(
  persist(
    (set, get) => ({
      // Initial state
      currentSessionId: null,
      currentSession: null,
      prompt: "",
      messages: [],
      isGenerating: false,
      isHydrated: false,

      // Session management
      setCurrentSession: (session) => set({ currentSession: session }),

      setCurrentSessionId: (sessionId) => set({ currentSessionId: sessionId }),

      loadSessionMessages: (messages) => set({ messages }),

      setHydrated: (hydrated) => set({ isHydrated: hydrated }),

      // Message management
      setPrompt: (prompt) => set({ prompt }),

      addMessage: (message) => {
        const id = uuidv4();
        const newMessage: MessageType = {
          ...message,
          id,
          timestamp: new Date(),
        };
        set((state) => ({
          messages: [...state.messages, newMessage],
        }));
        return id;
      },

      updateMessage: (id, updates) => {
        set((state) => ({
          messages: state.messages.map((msg) =>
            msg.id === id ? { ...msg, ...updates } : msg
          ),
        }));
      },

      setIsGenerating: (isGenerating) => set({ isGenerating }),

      clearMessages: () => set({ messages: [] }),

      // Session actions
      createNewSession: () => {
        set({
          currentSessionId: null,
          currentSession: null,
          messages: [],
          prompt: "",
          isGenerating: false,
        });
      },

      resetToNewSession: () => {
        set({
          currentSessionId: null,
          currentSession: null,
          messages: [],
          prompt: "",
          isGenerating: false,
        });
      },

      clearAllState: () => {
        set({
          currentSessionId: null,
          currentSession: null,
          messages: [],
          prompt: "",
          isGenerating: false,
          isHydrated: false,
        });
      },
    }),
    {
      name: "title-generator-storage",
      storage: createJSONStorage(() => sessionStorage),
      // Persist session ID, current session, and messages for better UX
      partialize: (state) => ({
        currentSessionId: state.currentSessionId,
        currentSession: state.currentSession,
        messages: state.messages, // Also persist messages to avoid flickering
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.setHydrated(true);
        }
      },
    }
  )
);
