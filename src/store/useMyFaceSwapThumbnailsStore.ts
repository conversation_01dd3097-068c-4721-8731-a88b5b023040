import { create } from "zustand";

export interface FaceSwapThumbnail {
  id: string;
  imageUrl: string;
  originalImageUrl: string;
  personaImageUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  limit: number;
}

interface MyFaceSwapThumbnailsState {
  thumbnails: FaceSwapThumbnail[];
  pagination: PaginationInfo | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  fetchThumbnails: (page?: number, limit?: number) => Promise<void>;
  deleteThumbnail: (id: string) => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

export const useMyFaceSwapThumbnailsStore = create<MyFaceSwapThumbnailsState>(
  (set, get) => ({
    thumbnails: [],
    pagination: null,
    isLoading: false,
    error: null,

    fetchThumbnails: async (page = 1, limit = 12) => {
      const state = get();

      // Prevent duplicate requests
      if (state.isLoading) {
        return;
      }

      set({ isLoading: true, error: null });

      try {
        const response = await fetch(
          `/api/my-faceswaps?page=${page}&limit=${limit}&sortBy=createdAt&sortOrder=desc`,
          {
            headers: {
              "Cache-Control": "max-age=30",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          set({
            thumbnails: data.data.thumbnails,
            pagination: data.data.pagination,
            isLoading: false,
          });
        } else {
          throw new Error(data.error || "Failed to fetch thumbnails");
        }
      } catch (error) {
        console.error("Error fetching faceswap thumbnails:", error);
        set({
          error: error instanceof Error ? error.message : "An error occurred",
          isLoading: false,
        });
      }
    },

    deleteThumbnail: async (id: string) => {
      try {
        const response = await fetch(`/api/my-faceswaps?id=${id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          // Remove the deleted thumbnail from the state
          const { thumbnails } = get();
          set({
            thumbnails: thumbnails.filter((thumbnail) => thumbnail.id !== id),
          });
        } else {
          throw new Error(data.error || "Failed to delete thumbnail");
        }
      } catch (error) {
        console.error("Error deleting faceswap thumbnail:", error);
        set({
          error: error instanceof Error ? error.message : "An error occurred",
        });
      }
    },

    clearError: () => set({ error: null }),

    reset: () =>
      set({
        thumbnails: [],
        pagination: null,
        isLoading: false,
        error: null,
      }),
  })
);
