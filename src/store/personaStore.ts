import { create } from "zustand";

export interface Persona {
  id: string;
  userId: string;
  name: string;
  finetuneId: string | null;
  triggerWord: string;
  status: "pending" | "training" | "ready" | "error";
  generatedImageUrl: string | null;
  trainingImagesCount: number;
  error: string | null;
  createdAt: Date;
  updatedAt: Date;
}

interface PersonaState {
  // Selected persona for thumbnail generation
  selectedPersona: Persona | null;

  // All user personas
  personas: Persona[];

  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isGeneratingImage: boolean;

  // Error state
  error: string | null;

  // Actions
  setSelectedPersona: (persona: Persona | null) => void;
  setPersonas: (personas: Persona[]) => void;
  addPersona: (persona: Persona) => void;
  updatePersona: (personaId: string, updates: Partial<Persona>) => void;
  removePersona: (personaId: string) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setGeneratingImage: (generating: boolean) => void;
  setError: (error: string | null) => void;

  // Get ready personas for selection
  getReadyPersonas: () => Persona[];

  // Clear all state
  reset: () => void;
}

export const usePersonaStore = create<PersonaState>((set, get) => ({
  selectedPersona: null,
  personas: [],
  isLoading: false,
  isCreating: false,
  isGeneratingImage: false,
  error: null,

  setSelectedPersona: (persona) => set({ selectedPersona: persona }),

  setPersonas: (personas) => set({ personas }),

  addPersona: (persona) =>
    set((state) => ({
      personas: [persona, ...state.personas],
    })),

  updatePersona: (personaId, updates) =>
    set((state) => ({
      personas: state.personas.map((p) =>
        p.id === personaId ? { ...p, ...updates } : p
      ),
      selectedPersona:
        state.selectedPersona?.id === personaId
          ? { ...state.selectedPersona, ...updates }
          : state.selectedPersona,
    })),

  removePersona: (personaId) =>
    set((state) => ({
      personas: state.personas.filter((p) => p.id !== personaId),
      selectedPersona:
        state.selectedPersona?.id === personaId ? null : state.selectedPersona,
    })),

  setLoading: (loading) => set({ isLoading: loading }),

  setCreating: (creating) => set({ isCreating: creating }),

  setGeneratingImage: (generating) => set({ isGeneratingImage: generating }),

  setError: (error) => set({ error }),

  getReadyPersonas: () => {
    const { personas } = get();
    return personas.filter((p) => p.status === "ready");
  },

  reset: () =>
    set({
      selectedPersona: null,
      personas: [],
      isLoading: false,
      isCreating: false,
      isGeneratingImage: false,
      error: null,
    }),
}));
