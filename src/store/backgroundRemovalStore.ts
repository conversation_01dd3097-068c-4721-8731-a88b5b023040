import { create } from "zustand";

export type BackgroundRemovalStatus =
  | "idle"
  | "processing"
  | "error"
  | "success";

export interface BackgroundRemovalState {
  originalImage: string | null;
  processedImage: string | null;
  status: BackgroundRemovalStatus;
  error: string | null;
  isProcessing: boolean;
  format: "png" | "jpg" | "webp";
  progress: { upload: number; download: number } | null;
  savedBackgroundRemovalId: string | null;

  setOriginalImage: (image: string | null) => void;
  setProcessedImage: (image: string | null) => void;
  setStatus: (status: BackgroundRemovalStatus) => void;
  setError: (error: string | null) => void;
  setIsProcessing: (isProcessing: boolean) => void;
  setFormat: (format: "png" | "jpg" | "webp") => void;
  setProgress: (progress: { upload: number; download: number } | null) => void;
  setSavedBackgroundRemovalId: (id: string | null) => void;
  removeBackground: (
    imageUrl: string,
    format?: "png" | "jpg" | "webp"
  ) => Promise<string | null>;
  reset: () => void;
}

export const useBackgroundRemovalStore = create<BackgroundRemovalState>(
  (set, get) => ({
    originalImage: null,
    processedImage: null,
    status: "idle",
    error: null,
    isProcessing: false,
    format: "png",
    progress: null,
    savedBackgroundRemovalId: null,

    setOriginalImage: (image) => set({ originalImage: image }),
    setProcessedImage: (image) => set({ processedImage: image }),
    setStatus: (status) => set({ status }),
    setError: (error) => set({ error }),
    setIsProcessing: (isProcessing) => set({ isProcessing }),
    setFormat: (format) => set({ format }),
    setProgress: (progress) => set({ progress }),
    setSavedBackgroundRemovalId: (id) => set({ savedBackgroundRemovalId: id }),

    removeBackground: async (imageUrl: string, format = "png") => {
      const {
        setStatus,
        setError,
        setIsProcessing,
        setProcessedImage,
        setProgress,
        setSavedBackgroundRemovalId,
      } = get();

      try {
        setIsProcessing(true);
        setStatus("processing");
        setError(null);
        setProgress(null);

        // Call the REMBG API
        const response = await fetch("/api/remove-background", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            imageUrl,
            format,
            returnBase64: true,
          }),
        });

        // Handle non-JSON responses (like HTML error pages)
        const contentType = response.headers.get("content-type");
        let data;

        if (contentType && contentType.includes("application/json")) {
          data = await response.json();
        } else {
          // If we get HTML or other non-JSON response, it's likely a server error
          const text = await response.text();
          console.error("Non-JSON response received:", text);

          // Provide user-friendly error based on status code
          let errorMessage = "Background removal failed";
          if (response.status === 500) {
            errorMessage =
              "The background removal service is temporarily unavailable. Please try again later.";
          } else if (response.status === 413) {
            errorMessage =
              "The image is too large for processing. Please try with a smaller image (max 25MB for 4K processing).";
          } else if (response.status === 400) {
            errorMessage =
              "The uploaded file is not supported. Please try with a JPG, PNG, or WebP image.";
          } else if (response.status >= 500) {
            errorMessage = "Server error occurred. Please try again later.";
          }

          throw new Error(errorMessage);
        }

        if (!data.success) {
          throw new Error(data.error || "Background removal failed");
        }

        // Update progress if available
        if (data.progress) {
          setProgress({
            upload: data.progress.upload || 0,
            download: data.progress.download || 0,
          });
        }

        setProcessedImage(data.processedImage);
        setSavedBackgroundRemovalId(data.backgroundRemovalId);
        setStatus("success");
        setProgress(null);
        return data.processedImage;
      } catch (error) {
        console.error("Background removal error:", error);

        let errorMessage = "Background removal failed";

        if (error instanceof Error) {
          // Clean up the error message - remove technical details
          let cleanMessage = error.message;

          // Remove HTML tags if present
          cleanMessage = cleanMessage.replace(/<[^>]*>/g, "");

          // Remove common technical prefixes
          cleanMessage = cleanMessage.replace(
            /^(Error:|Background removal failed:)\s*/i,
            ""
          );

          // If the message is too technical or contains HTML, provide a user-friendly alternative
          if (
            cleanMessage.includes("<!doctype") ||
            cleanMessage.includes("<html") ||
            cleanMessage.includes("Internal Server Error") ||
            cleanMessage.length > 200
          ) {
            errorMessage =
              "Unable to remove background from this image. Please try with a different image or try again later.";
          } else if (cleanMessage.trim()) {
            errorMessage = cleanMessage.trim();
          }
        }

        setError(errorMessage);
        setStatus("error");
        setProgress(null);
        return null;
      } finally {
        setIsProcessing(false);
      }
    },

    reset: () =>
      set({
        originalImage: null,
        processedImage: null,
        status: "idle",
        error: null,
        isProcessing: false,
        format: "png",
        progress: null,
        savedBackgroundRemovalId: null,
      }),
  })
);
