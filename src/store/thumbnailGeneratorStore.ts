"use client";

import { create } from "zustand";
import { v4 as uuidv4 } from "uuid";

export interface MessageType {
  id: string;
  type: "user" | "assistant";
  content: string;
  imageUrl?: string;
  timestamp: Date;
  status?: "generating" | "success" | "error";
  error?: string;
}

interface ThumbnailGeneratorState {
  prompt: string;
  messages: MessageType[];
  isGenerating: boolean;

  setPrompt: (prompt: string) => void;
  addMessage: (message: Omit<MessageType, "id" | "timestamp">) => string;
  updateMessage: (id: string, updates: Partial<MessageType>) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  clearMessages: () => void;
}

export const useThumbnailGeneratorStore = create<ThumbnailGeneratorState>(
  (set, get) => ({
    prompt: "",
    messages: [],
    isGenerating: false,

    setPrompt: (prompt) => set({ prompt }),

    addMessage: (message) => {
      const id = uuidv4();
      const newMessage: MessageType = {
        ...message,
        id,
        timestamp: new Date(),
      };
      set((state) => ({
        messages: [...state.messages, newMessage],
      }));
      return id;
    },

    updateMessage: (id, updates) => {
      set((state) => ({
        messages: state.messages.map((msg) =>
          msg.id === id ? { ...msg, ...updates } : msg
        ),
      }));
    },

    setIsGenerating: (isGenerating) => set({ isGenerating }),

    clearMessages: () => set({ messages: [] }),
  })
);
