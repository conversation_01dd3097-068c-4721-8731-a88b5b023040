import { create } from "zustand";

interface SidebarState {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
}

// Get initial state based on screen size
const getInitialState = () => {
  if (typeof window !== "undefined") {
    return window.innerWidth >= 1024; // lg breakpoint
  }
  return false;
};

export const useSidebarStore = create<SidebarState>((set) => ({
  isSidebarOpen: getInitialState(),
  toggleSidebar: () =>
    set((state) => ({ isSidebarOpen: !state.isSidebarOpen })),
}));
