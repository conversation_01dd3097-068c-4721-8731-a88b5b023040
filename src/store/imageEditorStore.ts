"use client";

import { create } from "zustand";
import { toast } from "sonner";

export interface ThumbnailEditorState {
  // Modal state
  isOpen: boolean;
  originalImageUrl: string | null;
  editedImageUrl: string | null;

  // Edit state
  prompt: string;
  isProcessing: boolean;
  isDownloading: boolean;
  error: string | null;
  personaId: string | null; // Optional persona for fine-tuned editing

  // Metadata for saving edited images
  sourceImageId: string | null; // ID of the original image
  sourceImageType:
    | "generated"
    | "recreated"
    | "faceswap"
    | "background_removal"
    | null;

  // Actions
  openEditor: (
    imageUrl: string,
    personaId?: string,
    sourceImageId?: string,
    sourceImageType?:
      | "generated"
      | "recreated"
      | "faceswap"
      | "background_removal"
  ) => void;
  closeEditor: () => void;
  setPrompt: (prompt: string) => void;
  setPersonaId: (personaId: string | null) => void;
  setIsProcessing: (isProcessing: boolean) => void;
  setIsDownloading: (isDownloading: boolean) => void;
  setError: (error: string | null) => void;
  setEditedImageUrl: (url: string | null) => void;
  generateEditedImage: (onSuccess?: (url: string) => void) => Promise<void>;
  downloadEditedImage: () => Promise<void>;
  startReEdit: () => void;
  reset: () => void;
}

const initialState = {
  isOpen: false,
  originalImageUrl: null,
  editedImageUrl: null,
  prompt: "",
  isProcessing: false,
  isDownloading: false,
  error: null,
  personaId: null,
  sourceImageId: null,
  sourceImageType: null,
};

export const useThumbnailEditorStore = create<ThumbnailEditorState>(
  (set, get) => ({
    ...initialState,

    openEditor: (
      imageUrl: string,
      personaId?: string,
      sourceImageId?: string,
      sourceImageType?:
        | "generated"
        | "recreated"
        | "faceswap"
        | "background_removal"
    ) => {
      set({
        isOpen: true,
        originalImageUrl: imageUrl,
        editedImageUrl: null,
        prompt: "",
        error: null,
        personaId: personaId || null,
        sourceImageId: sourceImageId || null,
        sourceImageType: sourceImageType || null,
      });
    },

    closeEditor: () => {
      set(initialState);
    },

    setPrompt: (prompt) => {
      set({ prompt });
    },

    setPersonaId: (personaId) => {
      set({ personaId });
    },

    setIsProcessing: (isProcessing) => {
      set({ isProcessing });
    },

    setIsDownloading: (isDownloading) => {
      set({ isDownloading });
    },

    setError: (error) => {
      set({ error });
    },

    setEditedImageUrl: (url) => {
      set({ editedImageUrl: url });
    },

    generateEditedImage: async (onSuccess) => {
      const { originalImageUrl, prompt, personaId } = get();

      if (!originalImageUrl) {
        toast.error("No image to edit");
        return;
      }

      if (!prompt || !prompt.trim()) {
        toast.error(
          "Please enter a description of how you want to edit the image"
        );
        return;
      }

      set({ isProcessing: true, error: null });

      try {
        // Call the new prompt-based thumbnail editor API
        const response = await fetch("/api/edit-thumbnail", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            imageUrl: originalImageUrl,
            prompt: prompt.trim(),
            personaId: personaId, // Include persona for fine-tuned editing
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to edit thumbnail");
        }

        const data = await response.json();

        if (data.imageUrl) {
          set({ editedImageUrl: data.imageUrl });
          const method = personaId ? "with persona" : "with Flux KontexMax";
          toast.success(`Thumbnail edited successfully ${method}!`);

          // Save edited image as separate entry in edited_images table
          const { originalImageUrl, sourceImageId, sourceImageType } = get();

          if (originalImageUrl && sourceImageId && sourceImageType) {
            try {
              const saveResponse = await fetch("/api/save-edited-image", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  originalImageUrl,
                  editedImageUrl: data.imageUrl,
                  sourceType: sourceImageType,
                  sourceId: sourceImageId,
                  prompt: prompt.trim(),
                  personaUsed: Boolean(personaId),
                }),
              });

              if (saveResponse.ok) {
                console.log("✅ Edited thumbnail saved to edited_images table");
                toast.success("Edited thumbnail saved!");
              } else {
                console.warn("⚠️ Failed to save edited thumbnail to database");
                const errorData = await saveResponse.json();
                console.error("Save error details:", errorData);
              }
            } catch (saveError) {
              console.error("Error saving edited thumbnail:", saveError);
              // Don't show error to user as the edit was successful
            }
          } else {
            console.warn(
              "⚠️ Missing metadata for saving edited thumbnail - image will not be saved to database"
            );
          }

          onSuccess?.(data.imageUrl);
        } else {
          throw new Error("No edited thumbnail returned");
        }
      } catch (error: any) {
        console.error("Error editing thumbnail:", error);
        const errorMessage = error.message || "Failed to edit thumbnail";
        set({ error: errorMessage });
        toast.error(errorMessage);
      } finally {
        set({ isProcessing: false });
      }
    },

    downloadEditedImage: async () => {
      const { editedImageUrl, isDownloading } = get();

      if (!editedImageUrl) {
        toast.error("No edited thumbnail to download");
        return;
      }

      if (isDownloading) {
        return; // Prevent multiple downloads
      }

      try {
        set({ isDownloading: true });

        // Fetch the image as a blob
        const response = await fetch(editedImageUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch image: ${response.statusText}`);
        }

        const blob = await response.blob();

        // Create a download link with the blob
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `edited-thumbnail-${Date.now()}.jpg`;

        // Trigger download
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the blob URL
        window.URL.revokeObjectURL(url);

        toast.success("Thumbnail downloaded successfully!");
      } catch (error) {
        console.error("Download failed:", error);
        toast.error("Failed to download thumbnail. Please try again.");
      } finally {
        set({ isDownloading: false });
      }
    },

    startReEdit: () => {
      set({ editedImageUrl: null });
    },

    reset: () => {
      set(initialState);
    },
  })
);
