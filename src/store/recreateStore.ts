"use client";

import { create } from "zustand";

export type GenerateStatus = "idle" | "generating" | "error" | "success";

export interface Message {
  id: string;
  type: "user" | "assistant";
  content: string;
  imageUrl?: string;
  timestamp: Date;
  status?: GenerateStatus;
  error?: string;
  personaUsed?: boolean;
  personaName?: string;
}

interface RecreateState {
  prompt: string;
  messages: Message[];
  isGenerating: boolean;
  selectedPersonaId: string | null;

  setPrompt: (prompt: string) => void;
  addMessage: (message: Omit<Message, "id" | "timestamp">) => string;
  updateMessage: (id: string, updates: Partial<Message>) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  setSelectedPersonaId: (personaId: string | null) => void;
  clearMessages: () => void;
  reset: () => void;
}

export const useRecreateStore = create<RecreateState>((set, get) => ({
  prompt: "",
  messages: [],
  isGenerating: false,
  selectedPersonaId: null,

  setPrompt: (prompt) => set({ prompt }),

  addMessage: (message) => {
    const newMessage: Message = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    };
    set((state) => ({ messages: [...state.messages, newMessage] }));
    return newMessage.id;
  },

  updateMessage: (id, updates) => {
    set((state) => ({
      messages: state.messages.map((msg) =>
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }));
  },

  setIsGenerating: (isGenerating) => set({ isGenerating }),

  setSelectedPersonaId: (personaId) => set({ selectedPersonaId: personaId }),

  clearMessages: () => set({ messages: [] }),

  reset: () =>
    set({
      prompt: "",
      messages: [],
      isGenerating: false,
      selectedPersonaId: null,
    }),
}));
