import { create } from "zustand";

export interface BackgroundRemoval {
  id: string;
  imageUrl: string;
  createdAt: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface MyBackgroundRemovalsState {
  backgroundRemovals: BackgroundRemoval[];
  pagination: PaginationInfo | null;
  loading: boolean;
  error: string | null;
  searchQuery: string;
  sortBy: "createdAt" | "updatedAt";
  sortOrder: "asc" | "desc";
  currentPage: number;
}

interface MyBackgroundRemovalsActions {
  fetchBackgroundRemovals: (page?: number) => Promise<void>;
  deleteBackgroundRemoval: (id: string) => Promise<void>;
  setSearchQuery: (query: string) => void;
  setSortBy: (sortBy: "createdAt" | "updatedAt") => void;
  setSortOrder: (order: "asc" | "desc") => void;
  setCurrentPage: (page: number) => void;
  clearError: () => void;
}

type MyBackgroundRemovalsStore = MyBackgroundRemovalsState &
  MyBackgroundRemovalsActions;

export const useMyBackgroundRemovalsStore = create<MyBackgroundRemovalsStore>(
  (set, get) => ({
    // Initial state
    backgroundRemovals: [],
    pagination: null,
    loading: false,
    error: null,
    searchQuery: "",
    sortBy: "createdAt",
    sortOrder: "desc",
    currentPage: 1,

    // Actions
    fetchBackgroundRemovals: async (page = 1) => {
      const { searchQuery, sortBy, sortOrder } = get();

      set({ loading: true, error: null });

      try {
        const params = new URLSearchParams({
          page: page.toString(),
          limit: "12",
          search: searchQuery,
          sortBy,
          sortOrder,
        });

        const response = await fetch(`/api/my-background-removals?${params}`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || "Failed to fetch background removals");
        }

        set({
          backgroundRemovals: data.data.backgroundRemovals,
          pagination: {
            currentPage: data.data.pagination.page,
            totalPages: data.data.pagination.totalPages,
            totalCount: data.data.pagination.totalCount,
            hasNextPage: data.data.pagination.hasNextPage,
            hasPreviousPage: data.data.pagination.hasPreviousPage,
          },
          currentPage: page,
          loading: false,
        });
      } catch (error) {
        console.error("Error fetching background removals:", error);
        set({
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch background removals",
          loading: false,
        });
      }
    },

    deleteBackgroundRemoval: async (id: string) => {
      try {
        const response = await fetch(`/api/my-background-removals/${id}`, {
          method: "DELETE",
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || "Failed to delete background removal");
        }

        // Remove from local state
        const { backgroundRemovals } = get();
        set({
          backgroundRemovals: backgroundRemovals.filter(
            (item) => item.id !== id
          ),
        });

        // If this was the last item on the current page and we're not on page 1,
        // go to the previous page
        const { pagination, currentPage } = get();
        if (pagination && backgroundRemovals.length === 1 && currentPage > 1) {
          get().fetchBackgroundRemovals(currentPage - 1);
        } else {
          // Refresh current page to update pagination
          get().fetchBackgroundRemovals(currentPage);
        }
      } catch (error) {
        console.error("Error deleting background removal:", error);
        set({
          error:
            error instanceof Error
              ? error.message
              : "Failed to delete background removal",
        });
      }
    },

    setSearchQuery: (query: string) => {
      set({ searchQuery: query, currentPage: 1 });
      // Debounce search - you might want to implement this
      get().fetchBackgroundRemovals(1);
    },

    setSortBy: (sortBy: "createdAt" | "updatedAt") => {
      set({ sortBy, currentPage: 1 });
    },

    setSortOrder: (order: "asc" | "desc") => {
      set({ sortOrder: order, currentPage: 1 });
    },

    setCurrentPage: (page: number) => {
      set({ currentPage: page });
      get().fetchBackgroundRemovals(page);
    },

    clearError: () => {
      set({ error: null });
    },
  })
);
