import { create } from "zustand";

export interface ThumbnailAnalysis {
  faces: {
    detected: boolean;
    count: number;
    person_details: Array<{
      name: string;
      confidence: number;
    }>;
  };
  intrigue: {
    elicit: number;
    enigmatic: number;
    provocative: number;
    intriguing: number;
    ominous: number;
  };
  objects: Array<{
    name: string;
    confidence: number;
  }>;
  labels: Array<{
    name: string;
    confidence: number;
  }>;
  rating: {
    score: number;
    qualitative: "Poor" | "Fair" | "Good" | "Very Good" | "Excellent";
    message: string;
  };
  improvements: {
    thumbnail: Array<{
      title: string;
      description: string;
    }>;
    title: string[];
  };
  compliance: {
    dimensions: { width: number; height: number; valid: boolean };
    minWidth: { value: number; valid: boolean };
    aspectRatio: { ratio: string; valid: boolean };
    fileSize: { size: number; valid: boolean };
    fileType: { type: string; valid: boolean };
    overall: boolean;
  };
}

export interface ThumbnailRatingInput {
  image: File | null;
  title: string;
}

interface ThumbnailRatingState {
  input: ThumbnailRatingInput;
  analysis: ThumbnailAnalysis | null;
  isAnalyzing: boolean;
  activeTab: string;

  setInput: (input: Partial<ThumbnailRatingInput>) => void;
  setAnalysis: (analysis: ThumbnailAnalysis | null) => void;
  setIsAnalyzing: (isAnalyzing: boolean) => void;
  setActiveTab: (tab: string) => void;
  resetInput: () => void;
}

const initialInput: ThumbnailRatingInput = {
  image: null,
  title: "",
};

export const useThumbnailRatingStore = create<ThumbnailRatingState>((set) => ({
  input: initialInput,
  analysis: null,
  isAnalyzing: false,
  activeTab: "faces",

  setInput: (newInput) =>
    set((state) => ({
      input: { ...state.input, ...newInput },
    })),

  setAnalysis: (analysis) => set({ analysis }),

  setIsAnalyzing: (isAnalyzing) => set({ isAnalyzing }),

  setActiveTab: (activeTab) => set({ activeTab }),

  resetInput: () => set({ input: initialInput, analysis: null }),
}));
