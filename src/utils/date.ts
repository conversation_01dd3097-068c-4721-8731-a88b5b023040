/**
 * Date utility functions for getting current date information
 */

/**
 * Get the current year
 * @returns Current year as a number (e.g., 2024)
 */
export function getCurrentYear(): number {
  return new Date().getFullYear();
}

/**
 * Get the current month name
 * @returns Current month name (e.g., "January", "February")
 */
export function getCurrentMonth(): string {
  const months = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];
  return months[new Date().getMonth()];
}

/**
 * Get the current month number
 * @returns Current month as a number (1-12)
 */
export function getCurrentMonthNumber(): number {
  return new Date().getMonth() + 1;
}

/**
 * Get the current date
 * @returns Current date as a number (1-31)
 */
export function getCurrentDate(): number {
  return new Date().getDate();
}

/**
 * Get the current day of the week
 * @returns Current day name (e.g., "Monday", "Tuesday")
 */
export function getCurrentDayOfWeek(): string {
  const days = [
    "Sunday", "Monday", "Tuesday", "Wednesday", 
    "Thursday", "Friday", "Saturday"
  ];
  return days[new Date().getDay()];
}

/**
 * Get formatted current date string
 * @param format Format type: "short" (MM/DD/YYYY), "long" (Month DD, YYYY), "iso" (YYYY-MM-DD)
 * @returns Formatted date string
 */
export function getFormattedCurrentDate(format: "short" | "long" | "iso" = "long"): string {
  const now = new Date();
  
  switch (format) {
    case "short":
      return `${(now.getMonth() + 1).toString().padStart(2, '0')}/${now.getDate().toString().padStart(2, '0')}/${now.getFullYear()}`;
    
    case "iso":
      return now.toISOString().split('T')[0];
    
    case "long":
    default:
      return `${getCurrentMonth()} ${getCurrentDate()}, ${getCurrentYear()}`;
  }
}

/**
 * Get current season based on the month
 * @returns Current season ("Spring", "Summer", "Fall", "Winter")
 */
export function getCurrentSeason(): string {
  const month = getCurrentMonthNumber();
  
  if (month >= 3 && month <= 5) return "Spring";
  if (month >= 6 && month <= 8) return "Summer";
  if (month >= 9 && month <= 11) return "Fall";
  return "Winter";
}

/**
 * Get current date context for AI prompts
 * @returns Object with comprehensive current date information
 */
export function getCurrentDateContext() {
  return {
    year: getCurrentYear(),
    month: getCurrentMonth(),
    monthNumber: getCurrentMonthNumber(),
    date: getCurrentDate(),
    dayOfWeek: getCurrentDayOfWeek(),
    season: getCurrentSeason(),
    formattedDate: getFormattedCurrentDate("long"),
    shortDate: getFormattedCurrentDate("short"),
    isoDate: getFormattedCurrentDate("iso"),
  };
}

/**
 * Check if current year is a leap year
 * @returns True if current year is a leap year
 */
export function isCurrentYearLeapYear(): boolean {
  const year = getCurrentYear();
  return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
}

/**
 * Get quarter of the year
 * @returns Current quarter (1, 2, 3, or 4)
 */
export function getCurrentQuarter(): number {
  const month = getCurrentMonthNumber();
  return Math.ceil(month / 3);
}
