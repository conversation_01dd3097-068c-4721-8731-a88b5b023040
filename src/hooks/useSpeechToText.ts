"use client";

import { useState, useCallback, useRef } from "react";
import { useAudioRecording } from "./useAudioRecording";
import {
  transcribeAudio,
  getToolPrompt,
  validateAudioWithDuration,
  TranscriptionProgress,
} from "@/src/services/speechToTextService";

export interface SpeechToTextState {
  isRecording: boolean;
  isTranscribing: boolean;
  isProcessing: boolean; // Recording OR transcribing
  transcribedText: string;
  error: string | null;
  progress: TranscriptionProgress | null;
  recordingDuration: number;
  failureCount: number; // Track consecutive failures
  isBlocked: boolean; // Circuit breaker state
}

export interface UseSpeechToTextOptions {
  tool?: "thumbnail" | "title" | "recreate" | "faceswap";
  onTranscriptionComplete?: (text: string) => void;
  onError?: (error: string) => void;
  autoTranscribe?: boolean; // Auto-transcribe when recording stops
  maxFailures?: number; // Maximum consecutive failures before blocking (default: 3)
  blockDuration?: number; // Duration to block in milliseconds (default: 30 seconds)
}

export interface UseSpeechToTextReturn {
  state: SpeechToTextState;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  transcribeLastRecording: () => Promise<void>;
  clearAll: () => void;
  resetCircuitBreaker: () => void; // Manual reset for circuit breaker
  isSupported: boolean;
}

/**
 * Combined hook for speech-to-text functionality
 * Handles both audio recording and transcription in one interface
 */
export function useSpeechToText({
  tool = "thumbnail",
  onTranscriptionComplete,
  onError,
  autoTranscribe = true,
  maxFailures = 3,
  blockDuration = 30000, // 30 seconds
}: UseSpeechToTextOptions = {}): UseSpeechToTextReturn {
  const [transcriptionState, setTranscriptionState] = useState({
    isTranscribing: false,
    transcribedText: "",
    error: null as string | null,
    progress: null as TranscriptionProgress | null,
    failureCount: 0,
    isBlocked: false,
  });

  const blockTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const audioRecording = useAudioRecording();

  // Combined state
  const state: SpeechToTextState = {
    isRecording: audioRecording.state.isRecording,
    isTranscribing: transcriptionState.isTranscribing,
    isProcessing:
      audioRecording.state.isRecording ||
      audioRecording.state.isProcessing ||
      transcriptionState.isTranscribing,
    transcribedText: transcriptionState.transcribedText,
    error: audioRecording.state.error || transcriptionState.error,
    progress: transcriptionState.progress,
    recordingDuration: audioRecording.state.duration,
    failureCount: transcriptionState.failureCount,
    isBlocked: transcriptionState.isBlocked,
  };

  // Handle transcription progress
  const handleProgress = useCallback((progress: TranscriptionProgress) => {
    setTranscriptionState((prev) => ({ ...prev, progress }));
  }, []);

  // Circuit breaker functions
  const blockTranscription = useCallback(() => {
    setTranscriptionState((prev) => ({
      ...prev,
      isBlocked: true,
      error: `Too many failures. Please wait ${
        blockDuration / 1000
      } seconds before trying again.`,
    }));

    // Clear block after duration
    if (blockTimeoutRef.current) {
      clearTimeout(blockTimeoutRef.current);
    }

    blockTimeoutRef.current = setTimeout(() => {
      setTranscriptionState((prev) => ({
        ...prev,
        isBlocked: false,
        failureCount: 0,
        error: null,
      }));
    }, blockDuration);
  }, [blockDuration]);

  const resetFailureCount = useCallback(() => {
    setTranscriptionState((prev) => ({
      ...prev,
      failureCount: 0,
    }));
  }, []);

  // Transcribe audio blob
  const transcribeAudioBlob = useCallback(
    async (audioBlob: Blob) => {
      // Check circuit breaker
      if (transcriptionState.isBlocked) {
        onError?.("Please wait before trying again.");
        return;
      }

      try {
        setTranscriptionState((prev) => ({
          ...prev,
          isTranscribing: true,
          error: null,
          progress: null,
        }));

        // Enhanced validation with duration check
        const validation = validateAudioWithDuration(
          audioBlob,
          audioRecording.state.duration
        );
        if (!validation.valid) {
          throw new Error(validation.error);
        }

        // Get context prompt for the tool
        const prompt = getToolPrompt(tool);

        // Transcribe
        const result = await transcribeAudio(
          audioBlob,
          { prompt, temperature: 0 },
          handleProgress
        );

        if (!result.success) {
          throw new Error(result.error || "Transcription failed");
        }

        setTranscriptionState((prev) => ({
          ...prev,
          transcribedText: result.text,
          isTranscribing: false,
          progress: { stage: "complete", message: "Transcription complete!" },
          failureCount: 0, // Reset failure count on success
        }));

        // Call completion callback
        onTranscriptionComplete?.(result.text);

        // Clear the audio recording to prevent re-processing
        audioRecording.clearRecording();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Transcription failed";

        setTranscriptionState((prev) => {
          const newFailureCount = prev.failureCount + 1;
          return {
            ...prev,
            error: errorMessage,
            isTranscribing: false,
            progress: { stage: "error", message: errorMessage },
            failureCount: newFailureCount,
          };
        });

        // Check if we should block after this failure
        const newFailureCount = transcriptionState.failureCount + 1;
        if (newFailureCount >= maxFailures) {
          blockTranscription();
        }

        onError?.(errorMessage);
      }
    },
    [
      tool,
      onTranscriptionComplete,
      onError,
      handleProgress,
      transcriptionState.failureCount,
      maxFailures,
      blockTranscription,
      audioRecording,
    ]
  );

  // Track processed audio blobs to prevent duplicate transcriptions
  const processedBlobsRef = useRef<Set<string>>(new Set());

  // Auto-transcribe when recording completes
  React.useEffect(() => {
    if (
      autoTranscribe &&
      audioRecording.state.audioBlob &&
      !audioRecording.state.isRecording &&
      !audioRecording.state.isProcessing &&
      !transcriptionState.isTranscribing
    ) {
      // Create a unique identifier for this audio blob
      const blobId = `${audioRecording.state.audioBlob.size}-${
        audioRecording.state.audioBlob.type
      }-${Date.now()}`;

      // Only transcribe if we haven't processed this blob before
      if (!processedBlobsRef.current.has(blobId)) {
        processedBlobsRef.current.add(blobId);
        transcribeAudioBlob(audioRecording.state.audioBlob);

        // Clean up old blob IDs to prevent memory leaks (keep only last 10)
        if (processedBlobsRef.current.size > 10) {
          const entries = Array.from(processedBlobsRef.current);
          processedBlobsRef.current.clear();
          entries.slice(-5).forEach((id) => processedBlobsRef.current.add(id));
        }
      }
    }
  }, [
    autoTranscribe,
    audioRecording.state.audioBlob,
    audioRecording.state.isRecording,
    audioRecording.state.isProcessing,
    transcriptionState.isTranscribing,
    transcribeAudioBlob,
  ]);

  // Start recording
  const startRecording = useCallback(async () => {
    // Clear previous transcription
    setTranscriptionState((prev) => ({
      ...prev,
      transcribedText: "",
      error: null,
      progress: null,
    }));

    await audioRecording.startRecording();
  }, [audioRecording]);

  // Stop recording
  const stopRecording = useCallback(() => {
    audioRecording.stopRecording();
  }, [audioRecording]);

  // Manually transcribe last recording
  const transcribeLastRecording = useCallback(async () => {
    if (audioRecording.state.audioBlob) {
      await transcribeAudioBlob(audioRecording.state.audioBlob);
    }
  }, [audioRecording.state.audioBlob, transcribeAudioBlob]);

  // Manual reset for circuit breaker
  const resetCircuitBreaker = useCallback(() => {
    if (blockTimeoutRef.current) {
      clearTimeout(blockTimeoutRef.current);
    }
    setTranscriptionState((prev) => ({
      ...prev,
      isBlocked: false,
      failureCount: 0,
      error: null,
    }));
  }, []);

  // Clear all state
  const clearAll = useCallback(() => {
    audioRecording.clearRecording();
    if (blockTimeoutRef.current) {
      clearTimeout(blockTimeoutRef.current);
    }
    setTranscriptionState({
      isTranscribing: false,
      transcribedText: "",
      error: null,
      progress: null,
      failureCount: 0,
      isBlocked: false,
    });
  }, [audioRecording]);

  return {
    state,
    startRecording,
    stopRecording,
    transcribeLastRecording,
    clearAll,
    resetCircuitBreaker,
    isSupported: audioRecording.isSupported,
  };
}

// Import React for useEffect
import React from "react";
