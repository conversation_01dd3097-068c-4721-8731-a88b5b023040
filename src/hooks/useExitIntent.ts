"use client";

import { useState, useEffect, useCallback } from "react";

interface UseExitIntentOptions {
  enabled?: boolean;
  delay?: number;
  sensitivity?: number;
}

export function useExitIntent(options: UseExitIntentOptions = {}) {
  const { enabled = true, delay = 1000, sensitivity = 20 } = options;
  const [showExitIntent, setShowExitIntent] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  const handleMouseLeave = useCallback((e: MouseEvent) => {
    // Only trigger if mouse is leaving from the top of the page
    if (e.clientY <= sensitivity && !hasTriggered) {
      setTimeout(() => {
        setShowExitIntent(true);
        setHasTriggered(true);
      }, delay);
    }
  }, [sensitivity, delay, hasTriggered]);

  const handleMouseEnter = useCallback(() => {
    // Reset if user comes back quickly
    if (!hasTriggered) {
      setShowExitIntent(false);
    }
  }, [hasTriggered]);

  const closeExitIntent = useCallback(() => {
    setShowExitIntent(false);
    setHasTriggered(true);
  }, []);

  const resetExitIntent = useCallback(() => {
    setShowExitIntent(false);
    setHasTriggered(false);
  }, []);

  useEffect(() => {
    if (!enabled) return;

    // Add event listeners
    document.addEventListener("mouseleave", handleMouseLeave);
    document.addEventListener("mouseenter", handleMouseEnter);

    // Cleanup
    return () => {
      document.removeEventListener("mouseleave", handleMouseLeave);
      document.removeEventListener("mouseenter", handleMouseEnter);
    };
  }, [enabled, handleMouseLeave, handleMouseEnter]);

  // Also trigger on scroll up behavior (alternative exit intent)
  useEffect(() => {
    if (!enabled) return;

    let lastScrollY = window.scrollY;
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const currentScrollY = window.scrollY;
          
          // If user scrolls up significantly and is near top of page
          if (
            currentScrollY < lastScrollY && 
            currentScrollY < 100 && 
            lastScrollY - currentScrollY > 50 &&
            !hasTriggered
          ) {
            setTimeout(() => {
              setShowExitIntent(true);
              setHasTriggered(true);
            }, delay);
          }
          
          lastScrollY = currentScrollY;
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", handleScroll, { passive: true });

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [enabled, delay, hasTriggered]);

  // Time-based trigger (fallback)
  useEffect(() => {
    if (!enabled) return;

    const timeoutId = setTimeout(() => {
      if (!hasTriggered) {
        setShowExitIntent(true);
        setHasTriggered(true);
      }
    }, 30000); // Show after 30 seconds if no other trigger

    return () => clearTimeout(timeoutId);
  }, [enabled, hasTriggered]);

  return {
    showExitIntent,
    closeExitIntent,
    resetExitIntent,
    hasTriggered,
  };
}
