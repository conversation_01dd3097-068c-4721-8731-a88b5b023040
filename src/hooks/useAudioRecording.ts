"use client";

import { useState, useRef, useCallback } from "react";

export interface AudioRecordingState {
  isRecording: boolean;
  isProcessing: boolean;
  audioBlob: Blob | null;
  duration: number;
  error: string | null;
}

export interface UseAudioRecordingReturn {
  state: AudioRecordingState;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  clearRecording: () => void;
  isSupported: boolean;
}

/**
 * Custom hook for audio recording using MediaRecorder API
 * Provides clean interface for recording audio in the browser
 */
export function useAudioRecording(): UseAudioRecordingReturn {
  const [state, setState] = useState<AudioRecordingState>({
    isRecording: false,
    isProcessing: false,
    audioBlob: null,
    duration: 0,
    error: null,
  });

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const startTimeRef = useRef<number>(0);
  const durationIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Check if MediaRecorder is supported
  const isSupported =
    typeof window !== "undefined" && "MediaRecorder" in window;

  const updateDuration = useCallback(() => {
    if (startTimeRef.current) {
      const elapsed = (Date.now() - startTimeRef.current) / 1000;
      setState((prev) => ({ ...prev, duration: elapsed }));
    }
  }, []);

  const startRecording = useCallback(async () => {
    if (!isSupported) {
      setState((prev) => ({
        ...prev,
        error: "Audio recording is not supported in this browser",
      }));
      return;
    }

    try {
      setState((prev) => ({ ...prev, error: null, isProcessing: true }));

      // Request microphone access with optimized settings for speech recognition
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true, // Automatically adjust microphone gain
          sampleRate: 16000, // 16kHz is optimal for speech recognition (lower than 44.1kHz)
          channelCount: 1, // Mono audio for speech
        },
      });

      streamRef.current = stream;
      chunksRef.current = [];

      // Create MediaRecorder with optimal settings for speech recognition
      let mimeType = "audio/webm";
      let audioBitsPerSecond = 128000; // 128kbps for good quality speech

      // Choose best available format for speech
      if (MediaRecorder.isTypeSupported("audio/webm;codecs=opus")) {
        mimeType = "audio/webm;codecs=opus";
        audioBitsPerSecond = 64000; // Opus is more efficient, 64kbps is sufficient
      } else if (MediaRecorder.isTypeSupported("audio/mp4;codecs=mp4a.40.2")) {
        mimeType = "audio/mp4;codecs=mp4a.40.2";
        audioBitsPerSecond = 128000; // AAC needs higher bitrate
      } else if (MediaRecorder.isTypeSupported("audio/mp4")) {
        mimeType = "audio/mp4";
      }

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType,
        audioBitsPerSecond,
      });

      mediaRecorderRef.current = mediaRecorder;

      // Handle data available
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      // Handle recording stop
      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(chunksRef.current, {
          type: mediaRecorder.mimeType,
        });

        setState((prev) => ({
          ...prev,
          audioBlob,
          isRecording: false,
          isProcessing: false,
        }));

        // Clean up
        if (streamRef.current) {
          streamRef.current.getTracks().forEach((track) => track.stop());
          streamRef.current = null;
        }

        if (durationIntervalRef.current) {
          clearInterval(durationIntervalRef.current);
          durationIntervalRef.current = null;
        }
      };

      // Handle errors
      mediaRecorder.onerror = (event) => {
        console.error("MediaRecorder error:", event);
        setState((prev) => ({
          ...prev,
          error: "Recording failed. Please try again.",
          isRecording: false,
          isProcessing: false,
        }));
      };

      // Start recording
      mediaRecorder.start(100); // Collect data every 100ms
      startTimeRef.current = Date.now();

      setState((prev) => ({
        ...prev,
        isRecording: true,
        isProcessing: false,
        duration: 0,
      }));

      // Start duration timer
      durationIntervalRef.current = setInterval(updateDuration, 100);
    } catch (error) {
      console.error("Failed to start recording:", error);
      let errorMessage = "Failed to access microphone. ";

      if (error instanceof Error) {
        if (error.name === "NotAllowedError") {
          errorMessage += "Please allow microphone access and try again.";
        } else if (error.name === "NotFoundError") {
          errorMessage += "No microphone found.";
        } else {
          errorMessage += error.message;
        }
      }

      setState((prev) => ({
        ...prev,
        error: errorMessage,
        isProcessing: false,
      }));
    }
  }, [isSupported, updateDuration]);

  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && state.isRecording) {
      setState((prev) => ({ ...prev, isProcessing: true }));
      mediaRecorderRef.current.stop();
    }
  }, [state.isRecording]);

  const clearRecording = useCallback(() => {
    // Stop recording if active
    if (mediaRecorderRef.current && state.isRecording) {
      mediaRecorderRef.current.stop();
    }

    // Clean up stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }

    // Clear duration timer
    if (durationIntervalRef.current) {
      clearInterval(durationIntervalRef.current);
      durationIntervalRef.current = null;
    }

    // Reset state
    setState({
      isRecording: false,
      isProcessing: false,
      audioBlob: null,
      duration: 0,
      error: null,
    });

    chunksRef.current = [];
    startTimeRef.current = 0;
  }, [state.isRecording]);

  return {
    state,
    startRecording,
    stopRecording,
    clearRecording,
    isSupported,
  };
}
