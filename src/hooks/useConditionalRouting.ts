"use client";

import { useRouter } from "next/navigation";
import { useAuthStore } from "@/src/store/authStore";

/**
 * Hook to handle conditional routing based on authentication status
 * Redirects authenticated users to tools versions of public pages
 */
export function useConditionalRouting() {
  const router = useRouter();
  const { user, isInitialized } = useAuthStore();

  const redirectToAuthenticatedVersion = (publicPath: string) => {
    if (!isInitialized) return; // Wait for auth to initialize

    // Map public routes to their authenticated equivalents
    const routeMap: Record<string, string> = {
      "/thumbnail-rating": "/tools/thumbnail-rating",
      "/thumbnail-download": "/tools/thumbnail-downloader",
      "/thumbnail-preview": "/tools/thumbnail-preview",
    };

    const authenticatedRoute = routeMap[publicPath];

    if (user && authenticatedRoute) {
      router.replace(authenticatedRoute);
    }
  };

  return { redirectToAuthenticatedVersion };
}
