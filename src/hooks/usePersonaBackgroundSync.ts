"use client";

import { useEffect, useRef } from "react";
import { usePersonaStore } from "@/src/store/personaStore";
import { toast } from "sonner";

/**
 * Hook to sync persona status in the background
 * This ensures personas continue training even when modals are closed
 */
export function usePersonaBackgroundSync() {
  const { personas, updatePersona, setPersonas } = usePersonaStore();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isCheckingRef = useRef(false);

  // Get training personas that need status checks
  const trainingPersonas = personas.filter(
    (p) => p.status === "training" || p.status === "pending"
  );

  const checkPersonaStatus = async (personaId: string) => {
    try {
      const response = await fetch(`/api/personas/${personaId}/status`);
      const data = await response.json();

      if (data.success && data.persona) {
        const oldPersona = personas.find((p) => p.id === personaId);
        const newStatus = data.persona.status;

        // Update the persona in the store
        updatePersona(personaId, {
          status: newStatus,
          generatedImageUrl: data.persona.generatedImageUrl,
          error: data.persona.error,
          updatedAt: new Date(data.persona.updatedAt),
        });

        // Show notification if status changed to ready or error
        if (oldPersona && oldPersona.status !== newStatus) {
          if (newStatus === "ready") {
            toast.success(`Persona "${oldPersona.name}" is ready!`, {
              description:
                "You can now use this persona for thumbnail generation.",
              duration: 5000,
            });
          } else if (newStatus === "error") {
            toast.error(`Persona "${oldPersona.name}" training failed`, {
              description:
                data.persona.error || "Please try creating the persona again.",
              duration: 8000,
            });
          }
        }

        // If persona is now ready or failed, we can stop checking it
        return newStatus === "ready" || newStatus === "error";
      }
    } catch (error) {
      console.error(`Error checking persona ${personaId} status:`, error);
    }
    return false;
  };

  const checkAllTrainingPersonas = async () => {
    if (isCheckingRef.current || trainingPersonas.length === 0) {
      return;
    }

    isCheckingRef.current = true;

    try {
      // Check status for all training personas
      const statusChecks = trainingPersonas.map((persona) =>
        checkPersonaStatus(persona.id)
      );

      await Promise.all(statusChecks);
    } catch (error) {
      console.error("Error checking training personas:", error);
    } finally {
      isCheckingRef.current = false;
    }
  };

  // Load initial personas
  const loadPersonas = async () => {
    try {
      const response = await fetch("/api/personas");
      const data = await response.json();

      if (data.success) {
        const personasWithDates = data.personas.map((p: any) => ({
          ...p,
          createdAt: new Date(p.createdAt),
          updatedAt: new Date(p.updatedAt),
        }));
        setPersonas(personasWithDates);
      }
    } catch (error) {
      console.error("Error loading personas:", error);
    }
  };

  // Set up background sync
  useEffect(() => {
    // Load personas on mount
    loadPersonas();

    // Set up interval for checking training personas
    if (trainingPersonas.length > 0) {
      // Check immediately
      checkAllTrainingPersonas();

      // Then check every 15 seconds
      intervalRef.current = setInterval(() => {
        checkAllTrainingPersonas();
      }, 15000);
    } else {
      // Clear interval if no training personas
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    // Cleanup on unmount or when training personas change
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [trainingPersonas.length]); // Re-run when number of training personas changes

  // Also check when the page becomes visible again (user returns to tab)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && trainingPersonas.length > 0) {
        // Page became visible, check status immediately
        checkAllTrainingPersonas();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [trainingPersonas.length]);

  return {
    trainingPersonas,
    isChecking: isCheckingRef.current,
    checkPersonaStatus,
    loadPersonas,
  };
}
