"use client";

import { useState, useCallback, useRef } from "react";

export interface StreamingProgress {
  progress: number;
  stage: "submitting" | "processing" | "generating" | "uploading" | "complete";
  message?: string;
}

export interface StreamingResult {
  success: boolean;
  imageUrl?: string;
  responseId?: string;
  thumbnailId?: string;
  error?: string;
}

export interface UseStreamingGenerationOptions {
  onProgress?: (progress: StreamingProgress) => void;
  onComplete?: (result: StreamingResult) => void;
  onError?: (error: string) => void;
}

export function useStreamingGeneration(options: UseStreamingGenerationOptions = {}) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState<StreamingProgress | null>(null);
  const [result, setResult] = useState<StreamingResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  const eventSourceRef = useRef<EventSource | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const generateThumbnail = useCallback(async (
    prompt: string,
    persona?: {
      id: string;
      name: string;
      generatedImageUrl?: string;
      finetuneId?: string;
    },
    title?: string,
    tags?: string[]
  ) => {
    // Reset state
    setIsGenerating(true);
    setProgress(null);
    setResult(null);
    setError(null);

    // Clean up any existing connections
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      // Create abort controller for the initial request
      abortControllerRef.current = new AbortController();

      // First, make a POST request to start the streaming generation
      const response = await fetch("/api/generate-thumbnail-stream", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          persona,
          title,
          tags,
        }),
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check if the response is actually a stream
      const contentType = response.headers.get("content-type");
      if (!contentType?.includes("text/event-stream")) {
        // Fallback to regular JSON response
        const jsonResult = await response.json();
        if (jsonResult.success) {
          const finalResult: StreamingResult = {
            success: true,
            imageUrl: jsonResult.imageUrl,
            responseId: jsonResult.responseId,
            thumbnailId: jsonResult.thumbnailId,
          };
          setResult(finalResult);
          options.onComplete?.(finalResult);
        } else {
          throw new Error(jsonResult.error || "Generation failed");
        }
        return;
      }

      // Handle streaming response
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error("Failed to get response reader");
      }

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              if (data.error) {
                throw new Error(data.error);
              }
              
              if (data.complete) {
                // Generation completed
                const finalResult: StreamingResult = {
                  success: data.success,
                  imageUrl: data.imageUrl,
                  responseId: data.responseId,
                  thumbnailId: data.thumbnailId,
                };
                setResult(finalResult);
                options.onComplete?.(finalResult);
              } else if (data.progress !== undefined) {
                // Progress update
                const progressUpdate: StreamingProgress = {
                  progress: data.progress,
                  stage: data.stage || "processing",
                  message: data.message,
                };
                setProgress(progressUpdate);
                options.onProgress?.(progressUpdate);
              }
            } catch (parseError) {
              console.warn("Failed to parse SSE data:", parseError);
            }
          }
        }
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Generation failed";
      setError(errorMessage);
      options.onError?.(errorMessage);
    } finally {
      setIsGenerating(false);
      
      // Clean up
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      if (abortControllerRef.current) {
        abortControllerRef.current = null;
      }
    }
  }, [options]);

  const cancelGeneration = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setIsGenerating(false);
    setProgress(null);
  }, []);

  return {
    generateThumbnail,
    cancelGeneration,
    isGenerating,
    progress,
    result,
    error,
  };
}

/**
 * Fallback hook for non-streaming generation
 * Uses the original API endpoint for compatibility
 */
export function useRegularGeneration(options: UseStreamingGenerationOptions = {}) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [result, setResult] = useState<StreamingResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const generateThumbnail = useCallback(async (
    prompt: string,
    persona?: {
      id: string;
      name: string;
      generatedImageUrl?: string;
      finetuneId?: string;
    },
    title?: string,
    tags?: string[]
  ) => {
    setIsGenerating(true);
    setResult(null);
    setError(null);

    try {
      const response = await fetch("/api/generate-thumbnail", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          prompt,
          persona,
          title,
          tags,
        }),
      });

      const data = await response.json();

      if (data.success) {
        const finalResult: StreamingResult = {
          success: true,
          imageUrl: data.imageUrl,
          responseId: data.responseId,
          thumbnailId: data.thumbnailId,
        };
        setResult(finalResult);
        options.onComplete?.(finalResult);
      } else {
        throw new Error(data.error || "Generation failed");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Generation failed";
      setError(errorMessage);
      options.onError?.(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  }, [options]);

  return {
    generateThumbnail,
    isGenerating,
    result,
    error,
  };
}
