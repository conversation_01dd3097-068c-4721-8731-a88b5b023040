import {
  UserCircle2,
  Users,
  HelpCircle,
  MessageSquareQuote,
  Home,
  Image,
  RefreshCw,
  User<PERSON><PERSON><PERSON>,
  Eraser,
  Type,
  Edit3,
  YoutubeIcon
} from "lucide-react";

export const menuItems = [
  {
    href: "/account",
    icon: UserCircle2,
    label: "Account",
  },
  {
    href: "/my-personas",
    icon: Users,
    label: "Personas",
  },
  {
    href: "/tutorials",
    icon: HelpCircle,
    label: "Tutorials",
  },
  {
    href: "https://youtube.com",
    icon: YoutubeIcon,
    label: "Free YouTube Mastery Course",
    isExternal: true,
  },
];

export const mainNavItems = [
  { href: "/dashboard", icon: Home, label: "Tools" },
];

export const contentSections = [
  {
    id: 1,
    title: "My Thumbnails",
    url: "/my-thumbnails",
    icon: Image,
  },
  {
    id: 2,
    title: "My Recreated",
    url: "/my-recreated",
    icon: RefreshCw,
  },
  {
    id: 3,
    title: "My Faceswaps",
    url: "/my-faceswaps",
    icon: User<PERSON><PERSON><PERSON>,
  },
  {
    id: 4,
    title: "My Edited",
    url: "/my-edited",
    icon: Edit3,
  },
  {
    id: 5,
    title: "My Personas",
    url: "/my-personas",
    icon: Users,
  },
  {
    id: 6,
    title: "My Titles",
    url: "/my-titles",
    icon: Type,
  },
  {
    id: 7,
    title: "BG Removed",
    url: "/bg-removed",
    icon: Eraser,
  },
];
