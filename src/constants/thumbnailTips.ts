/**
 * YouTube thumbnail optimization tips for display during loading states
 * These tips are shown to users while their thumbnails are being generated
 */

export interface ThumbnailTip {
  id: string;
  text: string;
  category: 'design' | 'psychology' | 'technical' | 'strategy' | 'color' | 'text';
}

export const thumbnailTips: ThumbnailTip[] = [
  // Design & Composition Tips
  {
    id: 'faces-ctr',
    text: '💡 Tip: 70% of top channels use faces in thumbnails—test it yourself!',
    category: 'design'
  },
  {
    id: 'rule-of-thirds',
    text: '📐 Pro Tip: Use the rule of thirds to position your main subject for maximum impact',
    category: 'design'
  },
  {
    id: 'negative-space',
    text: '🎯 Design Tip: Leave negative space around your subject to make it pop',
    category: 'design'
  },
  {
    id: 'focal-point',
    text: '👁️ Focus Tip: One clear focal point performs better than cluttered designs',
    category: 'design'
  },

  // Color & Contrast Tips
  {
    id: 'bright-contrast',
    text: '🧠 Quick Tip: Use bright contrast in your thumbnails to stand out in dark mode',
    category: 'color'
  },
  {
    id: 'color-psychology',
    text: '🌈 Color Tip: Red and orange create urgency, blue builds trust, green suggests growth',
    category: 'color'
  },
  {
    id: 'complementary-colors',
    text: '🎨 Pro Tip: Use complementary colors (opposite on color wheel) for maximum contrast',
    category: 'color'
  },
  {
    id: 'saturation',
    text: '✨ Brightness Tip: Slightly oversaturated colors perform better on mobile screens',
    category: 'color'
  },

  // Text & Typography Tips
  {
    id: 'text-size',
    text: '📱 Mobile Tip: Text should be readable at 150x84 pixels (mobile size)',
    category: 'text'
  },
  {
    id: 'word-limit',
    text: '📝 Text Tip: Keep text to 4 words or less for maximum readability',
    category: 'text'
  },
  {
    id: 'text-contrast',
    text: '🔤 Typography Tip: Add stroke/outline to text for better visibility over images',
    category: 'text'
  },
  {
    id: 'font-weight',
    text: '💪 Bold Tip: Use bold, sans-serif fonts—they\'re more readable at small sizes',
    category: 'text'
  },

  // Psychology & Emotion Tips
  {
    id: 'emotion-faces',
    text: '😮 Psychology Tip: Surprised or excited facial expressions increase click rates',
    category: 'psychology'
  },
  {
    id: 'curiosity-gap',
    text: '🤔 Curiosity Tip: Create a "curiosity gap"—show the setup but not the payoff',
    category: 'psychology'
  },
  {
    id: 'before-after',
    text: '⚡ Drama Tip: Before/after comparisons create compelling visual stories',
    category: 'psychology'
  },
  {
    id: 'eye-direction',
    text: '👀 Gaze Tip: Direct the subject\'s gaze toward your title or key elements',
    category: 'psychology'
  },

  // Technical & Platform Tips
  {
    id: 'aspect-ratio',
    text: '📺 Format Tip: Always use 16:9 aspect ratio (1280x720) for YouTube thumbnails',
    category: 'technical'
  },
  {
    id: 'file-size',
    text: '💾 Upload Tip: Keep file size under 2MB for faster loading across all devices',
    category: 'technical'
  },
  {
    id: 'mobile-first',
    text: '📱 Mobile Tip: 70% of YouTube views are on mobile—design for small screens first',
    category: 'technical'
  },
  {
    id: 'high-res',
    text: '🔍 Quality Tip: Use high resolution images—they look better on retina displays',
    category: 'technical'
  },

  // Strategy & Testing Tips
  {
    id: 'ab-testing',
    text: '🧪 Testing Tip: A/B test different thumbnails to see what resonates with your audience',
    category: 'strategy'
  },
  {
    id: 'competitor-analysis',
    text: '🔍 Research Tip: Study top performers in your niche for inspiration',
    category: 'strategy'
  },
  {
    id: 'brand-consistency',
    text: '🎯 Brand Tip: Maintain consistent style across thumbnails to build recognition',
    category: 'strategy'
  },
  {
    id: 'trending-elements',
    text: '📈 Trend Tip: Incorporate current visual trends while staying true to your brand',
    category: 'strategy'
  },
  {
    id: 'seasonal-relevance',
    text: '🗓️ Timing Tip: Update thumbnails seasonally to maintain relevance',
    category: 'strategy'
  },

  // Advanced Tips
  {
    id: 'visual-hierarchy',
    text: '📊 Layout Tip: Create clear visual hierarchy—most important element should be largest',
    category: 'design'
  },
  {
    id: 'pattern-interrupt',
    text: '⚡ Attention Tip: Break visual patterns to make your thumbnail stand out in feeds',
    category: 'psychology'
  },
  {
    id: 'storytelling',
    text: '📖 Story Tip: Tell a visual story that makes viewers want to know more',
    category: 'psychology'
  },
  {
    id: 'social-proof',
    text: '👥 Trust Tip: Include subtle social proof elements like viewer counts or testimonials',
    category: 'strategy'
  }
];

/**
 * Get a random tip from the collection
 */
export function getRandomTip(): ThumbnailTip {
  return thumbnailTips[Math.floor(Math.random() * thumbnailTips.length)];
}

/**
 * Get tips by category
 */
export function getTipsByCategory(category: ThumbnailTip['category']): ThumbnailTip[] {
  return thumbnailTips.filter(tip => tip.category === category);
}

/**
 * Get a sequence of tips for rotation during loading
 * Ensures no immediate repeats and good variety
 */
export function getTipSequence(count: number = 10): ThumbnailTip[] {
  const shuffled = [...thumbnailTips].sort(() => Math.random() - 0.5);
  return shuffled.slice(0, count);
}
