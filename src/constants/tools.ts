// constants/tools.ts

export type Tool = {
  image: string;
  title: string;
  description: string;
  url: string;
};

export const tools: Tool[] = [
  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Thumbnail Generator",
    description: "Generate eye-catching thumbnails from text prompts",
    url: "/tools/thumbnail-generator",
  },

  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Thumbnail Recreate",
    description: "Recreate Existing thumbnails",
    url: "/tools/recreate",
  },

  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Face Swap",
    description: "Swap faces in your thumbnails easily",
    url: "/tools/faceswap",
  },

  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Train AI Model",
    description: "Train your own custom AI model on your images",
    url: "/my-personas",
  },

  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Title Generator",
    description: "Generate click-worthy video titles that boost your CTR",
    url: "/tools/title-generator",
  },

  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Thumbnail Upscale",
    description: "Generate click-worthy video titles that boost your CTR",
    url: "/tools/thumbnail-upscale",
  },

  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Background Remover",
    description: "Remove background from any image instantly",
    url: "/tools/background-remover",
  },
  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Thumbnail Rating",
    description:
      "Get AI-powered analysis and ratings for your YouTube thumbnails with improvement suggestions",
    url: "/tools/thumbnail-rating",
  },

  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Thumbnail Preview",
    description:
      "Preview how your thumbnails look across different devices and YouTube layouts",
    url: "/tools/thumbnail-preview",
  },
  {
    image:
      "https://res.cloudinary.com/da5yphcen/image/upload/v1749540767/thumbnails/recreated_thumb_1749540762247.jpg",
    title: "Youtube Thumbnail Downloader",
    description: "Extract thumbnails from YouTube videos",
    url: "/tools/thumbnail-downloader",
  },
];
