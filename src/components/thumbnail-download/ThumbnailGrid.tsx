import React from "react";
import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { Button } from "@/src/components/ui/button";
import { Download, Loader2, CheckCircle } from "lucide-react";
import Image from "next/image";
import { buildThumbnailUrl, Quality } from "@/src/lib/downloadThumbnail";
import { THUMBNAIL_SIZES } from "./constants";

interface ThumbnailGridProps {
  videoId: string;
  handleDownload: (quality: Quality) => void;
  isDownloading: (quality: Quality) => boolean;
  isDownloaded: (quality: Quality) => boolean;
}

export const ThumbnailGrid = ({
  videoId,
  handleDownload,
  isDownloading,
  isDownloaded,
}: ThumbnailGridProps) => {
  const secondarySizes = THUMBNAIL_SIZES.filter((size) => !size.isPrimary);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {secondarySizes.map((size) => (
        <Card
          key={size.quality}
          className="group hover:shadow-md transition-shadow"
        >
          <CardContent className="p-4 space-y-3">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <h4 className="font-medium">{size.label}</h4>
                  {size.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {size.badge}
                    </Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {size.description}
                </p>
              </div>
            </div>

            <div className="relative rounded-md overflow-hidden bg-muted border">
              <Image
                src={buildThumbnailUrl(videoId, size.quality)}
                alt={`YouTube Thumbnail - ${size.label}`}
                width={size.width}
                height={size.height}
                className="w-full h-auto"
              />
            </div>

            <Button
              onClick={() => handleDownload(size.quality)}
              variant="outline"
              disabled={isDownloading(size.quality)}
              className="w-full"
            >
              {isDownloading(size.quality) ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Downloading...
                </>
              ) : isDownloaded(size.quality) ? (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Downloaded
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
