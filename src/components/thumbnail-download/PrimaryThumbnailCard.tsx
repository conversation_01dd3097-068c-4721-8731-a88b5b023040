import React from "react";
import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { Button } from "@/src/components/ui/button";
import { Download, Loader2, CheckCircle } from "lucide-react";
import Image from "next/image";
import { buildThumbnailUrl, Quality } from "@/src/lib/downloadThumbnail";
import { THUMBNAIL_SIZES } from "./constants";

interface PrimaryThumbnailCardProps {
  videoId: string;
  handleDownload: (quality: Quality) => void;
  isDownloading: (quality: Quality) => boolean;
  isDownloaded: (quality: Quality) => boolean;
}

export const PrimaryThumbnailCard = ({
  videoId,
  handleDownload,
  isDownloading,
  isDownloaded,
}: PrimaryThumbnailCardProps) => {
  const primarySizes = THUMBNAIL_SIZES.filter((size) => size.isPrimary);

  return (
    <Card className="border-primary/20 bg-primary/5">
      <CardContent className="p-6">
        {primarySizes.map((size) => (
          <div key={size.quality} className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <h4 className="text-lg font-semibold">{size.label}</h4>
                  <Badge variant="default">{size.badge}</Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {size.description}
                </p>
              </div>
              <Button
                onClick={() => handleDownload(size.quality)}
                disabled={isDownloading(size.quality)}
                size="lg"
                className="min-w-[120px]"
              >
                {isDownloading(size.quality) ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Downloading
                  </>
                ) : isDownloaded(size.quality) ? (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Downloaded
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Download HD
                  </>
                )}
              </Button>
            </div>
            <div className="relative rounded-lg overflow-hidden bg-muted border">
              <Image
                src={buildThumbnailUrl(videoId, size.quality)}
                alt={`YouTube Thumbnail - ${size.label}`}
                width={size.width}
                height={size.height}
                className="w-full h-auto"
              />
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};
