import { useState } from "react";
import { Quality, buildThumbnailUrl } from "@/src/lib/downloadThumbnail";
import { THUMBNAIL_SIZES } from "./constants";

export const useDownloadHandler = (videoId: string | null) => {
  const [downloadingStates, setDownloadingStates] = useState<
    Record<string, boolean>
  >({});
  const [downloadedStates, setDownloadedStates] = useState<
    Record<string, boolean>
  >({});

  const handleDownload = async (quality: Quality) => {
    if (!videoId) return;

    setDownloadingStates((prev) => ({ ...prev, [quality]: true }));

    try {
      const imageUrl = buildThumbnailUrl(videoId, quality);
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;

      // Use the correct filename mapping
      const thumbnailSize = THUMBNAIL_SIZES.find(
        (size) => size.quality === quality
      );
      const filename = thumbnailSize?.filename || quality;
      a.download = `youtube-thumbnail-${filename}.jpg`;

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(downloadUrl);

      setDownloadedStates((prev) => ({ ...prev, [quality]: true }));
      setTimeout(() => {
        setDownloadedStates((prev) => ({ ...prev, [quality]: false }));
      }, 3000);
    } catch (err) {
      console.error("Download failed:", err);
    } finally {
      setDownloadingStates((prev) => ({ ...prev, [quality]: false }));
    }
  };

  const isDownloading = (quality: Quality) =>
    downloadingStates[quality] || false;
  const isDownloaded = (quality: Quality) => downloadedStates[quality] || false;

  return {
    handleDownload,
    isDownloading,
    isDownloaded,
  };
};
