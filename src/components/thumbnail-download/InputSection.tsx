import React from "react";
import { Input } from "@/src/components/ui/input";
import { But<PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Image as ImageIcon, RefreshCw, Link } from "lucide-react";

import { Quality } from "@/src/lib/downloadThumbnail";

interface InputSectionProps {
  url: string;
  setUrl: (url: string) => void;
  generate: (quality?: Quality, showVariants?: boolean) => void;
  reset: () => void;
}

export const InputSection = ({
  url,
  setUrl,
  generate,
  reset,
}: InputSectionProps) => {
  return (
    <Card className="border-2 border-dashed border-muted-foreground/25 bg-muted/30">
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="text-center space-y-2">
            <h2 className="text-2xl font-semibold tracking-tight">
              Extract YouTube Thumbnails
            </h2>
            <p className="text-muted-foreground">
              Paste any YouTube URL below to download thumbnails in multiple
              quality options
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 max-w-2xl mx-auto">
            <div className="relative flex-1">
              <Link className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="url"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                placeholder="https://youtube.com/watch?v=..."
                className="pl-10 h-12"
              />
            </div>
            <Button
              onClick={() => generate("maxresdefault", true)}
              size="lg"
              className="h-12 px-8"
              disabled={!url.trim()}
            >
              <ImageIcon className="mr-2 h-4 w-4" />
              Get Thumbnails
            </Button>
            <Button
              variant="outline"
              onClick={reset}
              size="lg"
              className="h-12"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
