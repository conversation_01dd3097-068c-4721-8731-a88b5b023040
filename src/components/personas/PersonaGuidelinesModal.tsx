"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { CheckCircle, X, Upload } from "lucide-react";

interface PersonaGuidelinesModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue: () => void;
}

export function PersonaGuidelinesModal({
  isOpen,
  onClose,
  onContinue,
}: PersonaGuidelinesModalProps) {
  const goodExamples = [
    {
      url: "https://images.unsplash.com/photo-1633332755192-727a05c4013d?w=200&h=200&fit=crop&crop=face",
      alt: "Professional headshot",
    },
    {
      url: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=200&h=200&fit=crop&crop=face",
      alt: "Clear facial features",
    },
    {
      url: "https://images.unsplash.com/photo-1521119989659-a83eee488004?w=200&h=200&fit=crop&crop=face",
      alt: "Good portrait lighting",
    },
    {
      url: "https://images.unsplash.com/photo-1632765854612-9b02b6ec2b15?w=200&h=200&fit=crop&crop=face",
      alt: "Natural expression",
    },
  ];

  const badExamples = [
    {
      url: "https://plus.unsplash.com/premium_photo-1675791188810-3a01768c1e2f?w=200&h=200&fit=crop",
      alt: "Group photo - multiple people",
    },
    {
      url: "https://images.unsplash.com/36/X7L5hgFXQZazzPaK3goC_14084990857_88cabf3b6d_o.jpg?w=200&h=200&fit=crop",
      alt: "Poor lighting and quality",
    },
    {
      url: "https://images.unsplash.com/uploads/1413395496082cbc91228/43e39040?w=200&h=200&fit=crop",
      alt: "Blurry or low quality",
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg xl:max-w-3xl max-h-[90vh] flex flex-col p-0">
        {/* Fixed Header */}
        <div className="flex-shrink-0 px-6 py-4 border-b bg-background">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold">
              Photo Guidelines for Best Results
            </DialogTitle>
          </DialogHeader>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto px-6 py-4 space-y-8">
          {/* Good Examples Section */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <h3 className="text-lg font-semibold text-green-600">
                UPLOAD 20+ PHOTOS FOR BEST RESULTS
              </h3>
            </div>
            <p className="text-muted-foreground mb-4">
              Upload high-quality images of one person. The more images you
              provide, the better the result - show different angles, clear
              facial expressions, and consistent identity
            </p>

            <div className="grid grid-cols-4 gap-4">
              {goodExamples.map((example, index) => (
                <div key={index} className="relative">
                  <img
                    src={example.url}
                    alt={example.alt}
                    className="w-full aspect-square object-cover rounded-lg"
                  />
                  <div className="absolute top-2 right-2">
                    <CheckCircle className="w-5 h-5 text-green-500 bg-white rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Bad Examples Section */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              <X className="w-5 h-5 text-red-600" />
              <h3 className="text-lg font-semibold text-red-600">
                AVOID THESE TYPES OF PHOTOS
              </h3>
            </div>
            <p className="text-muted-foreground mb-4">
              No duplicates, group shots, pets, nudes, filters, face-covering
              accessories, or masks
            </p>

            <div className="grid grid-cols-3 gap-4">
              {badExamples.map((example, index) => (
                <div key={index} className="relative">
                  <img
                    src={example.url}
                    alt={example.alt}
                    className="w-full aspect-square object-cover rounded-lg opacity-75"
                  />
                  <div className="absolute top-2 right-2">
                    <X className="w-5 h-5 text-red-500 bg-white rounded-full" />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Tips Section */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">💡 Pro Tips:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Use photos with good lighting and clear facial features</li>
              <li>• Include different angles: front, side, and 3/4 views</li>
              <li>• Vary expressions: smiling, serious, looking away</li>
              <li>• Include both close-ups and medium shots</li>
              <li>• Ensure consistent identity across all photos</li>
            </ul>
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="flex-shrink-0 px-6 py-4 pb-6 border-t bg-background">
          <div className="flex gap-3">
            <Button onClick={onContinue} className="flex-1 gap-2">
              <Upload className="w-4 h-4" />
              Upload Images
            </Button>
            <Button variant="outline" onClick={onClose} className="px-6">
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
