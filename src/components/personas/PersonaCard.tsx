"use client";

import { useState, useEffect } from "react";
import { Persona } from "@/src/store/personaStore";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { Progress } from "@/src/components/ui/progress";
import {
  User,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  Trash2,
  RefreshCw,
  Image as ImageIcon,
} from "lucide-react";
import { getPersonaStatusDisplay } from "@/src/lib/persona-utils";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/src/components/ui/alert-dialog";

interface PersonaCardProps {
  persona: Persona;
  onRefresh: () => void;
}

export function PersonaCard({ persona, onRefresh }: PersonaCardProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(false);
  const [progress, setProgress] = useState(0);

  const statusDisplay = getPersonaStatusDisplay(persona.status);

  // Auto-refresh training personas
  useEffect(() => {
    if (persona.status === "training" || persona.status === "pending") {
      const interval = setInterval(() => {
        checkStatus();
      }, 10000); // Check every 10 seconds

      return () => clearInterval(interval);
    }
  }, [persona.status, persona.id]);

  // Simulate progress for training personas
  useEffect(() => {
    if (persona.status === "training") {
      const interval = setInterval(() => {
        setProgress((prev) => {
          const newProgress = prev + Math.random() * 2;
          return newProgress > 95 ? 95 : newProgress;
        });
      }, 2000);

      return () => clearInterval(interval);
    } else {
      setProgress(persona.status === "ready" ? 100 : 0);
    }
  }, [persona.status]);

  const checkStatus = async () => {
    if (isCheckingStatus) return;

    setIsCheckingStatus(true);
    try {
      const response = await fetch(`/api/personas/${persona.id}/status`);
      const data = await response.json();

      if (data.success) {
        // If status changed, refresh the list
        if (data.persona.status !== persona.status) {
          onRefresh();
        }
      }
    } catch (error) {
      console.error("Error checking persona status:", error);
    } finally {
      setIsCheckingStatus(false);
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/personas?id=${persona.id}`, {
        method: "DELETE",
      });

      const data = await response.json();
      if (data.success) {
        onRefresh();
      } else {
        console.error("Failed to delete persona:", data.error);
      }
    } catch (error) {
      console.error("Error deleting persona:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  const getStatusIcon = () => {
    switch (persona.status) {
      case "ready":
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case "training":
      case "pending":
        return <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />;
      case "error":
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <Card className="relative overflow-hidden hover:shadow-lg transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-2">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg flex items-center gap-2 truncate">
              <User className="w-5 h-5 flex-shrink-0" />
              <span className="truncate">{persona.name}</span>
            </CardTitle>
            <div className="flex items-center gap-2 mt-2">
              {getStatusIcon()}
              <Badge
                variant={persona.status === "ready" ? "default" : "secondary"}
              >
                {statusDisplay.text}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-1 flex-shrink-0">
            {(persona.status === "training" ||
              persona.status === "pending") && (
              <Button
                variant="ghost"
                size="sm"
                onClick={checkStatus}
                disabled={isCheckingStatus}
                className="h-8 w-8 p-0"
              >
                <RefreshCw
                  className={`w-4 h-4 ${
                    isCheckingStatus ? "animate-spin" : ""
                  }`}
                />
              </Button>
            )}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  disabled={isDeleting}
                  className="h-8 w-8 p-0"
                >
                  <Trash2 className="w-4 h-4 text-red-600" />
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Persona</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{persona.name}"? This
                    action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Persona Image */}
        <div className="aspect-square bg-muted rounded-lg flex items-center justify-center overflow-hidden">
          {persona.generatedImageUrl ? (
            <img
              src={persona.generatedImageUrl}
              alt={persona.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-center">
              <ImageIcon className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                {persona.status === "training" ? "Generating..." : "No image"}
              </p>
            </div>
          )}
        </div>

        {/* Training Progress */}
        {(persona.status === "training" || persona.status === "pending") && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Training Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
            <p className="text-xs text-muted-foreground">
              This usually takes 30-45 minutes
            </p>
          </div>
        )}

        {/* Error Message */}
        {persona.status === "error" && persona.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-800">{persona.error}</p>
          </div>
        )}

        {/* Persona Details */}
        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex justify-between items-center">
            <span>Training Images:</span>
            <span className="font-medium">{persona.trainingImagesCount}</span>
          </div>
          {persona.triggerWord && (
            <div className="flex justify-between items-start gap-2">
              <span className="flex-shrink-0">Trigger Word:</span>
              <code className="text-xs bg-muted px-2 py-1 rounded break-all text-right">
                {persona.triggerWord}
              </code>
            </div>
          )}
          <div className="flex justify-between items-center">
            <span>Created:</span>
            <span className="font-medium">
              {persona.createdAt.toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Ready Status Actions */}
        {persona.status === "ready" && (
          <div className="pt-2 border-t">
            <p className="text-sm text-green-600 font-medium">
              ✓ Ready for thumbnail generation
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
