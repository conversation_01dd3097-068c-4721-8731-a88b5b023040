"use client";

import { useState } from "react";
import { <PERSON>a } from "@/src/store/personaStore";
import { PersonaCard } from "./PersonaCard";
import { Button } from "@/src/components/ui/button";
import { RefreshCw } from "lucide-react";

interface PersonaListProps {
  personas: Persona[];
  isLoading: boolean;
  onRefresh: () => void;
}

export function PersonaList({ personas, isLoading, onRefresh }: PersonaListProps) {
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  // Group personas by status
  const readyPersonas = personas.filter(p => p.status === "ready");
  const trainingPersonas = personas.filter(p => p.status === "training" || p.status === "pending");
  const errorPersonas = personas.filter(p => p.status === "error");

  return (
    <div className="space-y-8">
      {/* Header with refresh */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Your Personas</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={refreshing}
          className="gap-2"
        >
          <RefreshCw className={`w-4 h-4 ${refreshing ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </div>

      {/* Ready Personas */}
      {readyPersonas.length > 0 && (
        <div>
          <h3 className="text-lg font-medium mb-4 text-green-600">
            Ready ({readyPersonas.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {readyPersonas.map((persona) => (
              <PersonaCard
                key={persona.id}
                persona={persona}
                onRefresh={onRefresh}
              />
            ))}
          </div>
        </div>
      )}

      {/* Training Personas */}
      {trainingPersonas.length > 0 && (
        <div>
          <h3 className="text-lg font-medium mb-4 text-blue-600">
            Training ({trainingPersonas.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {trainingPersonas.map((persona) => (
              <PersonaCard
                key={persona.id}
                persona={persona}
                onRefresh={onRefresh}
              />
            ))}
          </div>
        </div>
      )}

      {/* Error Personas */}
      {errorPersonas.length > 0 && (
        <div>
          <h3 className="text-lg font-medium mb-4 text-red-600">
            Failed ({errorPersonas.length})
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {errorPersonas.map((persona) => (
              <PersonaCard
                key={persona.id}
                persona={persona}
                onRefresh={onRefresh}
              />
            ))}
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoading && personas.length === 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="h-64 bg-muted rounded-lg animate-pulse"
            />
          ))}
        </div>
      )}
    </div>
  );
}
