"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { User } from "lucide-react";
import { usePersonaStore, Persona } from "@/src/store/personaStore";
import { PersonaSelectionModal } from "./PersonaSelectionModal";

interface PersonasButtonProps {
  className?: string;
}

export function PersonasButton({ className }: PersonasButtonProps) {
  const { selectedPersona } = usePersonaStore();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        variant="outline"
        className={`gap-2 ${className || ""}`}
        onClick={() => setIsModalOpen(true)}
      >
        <User className="w-4 h-4" />
        {selectedPersona ? (
          <>
            {selectedPersona.name}
            <Badge variant="secondary" className="ml-1">
              Active
            </Badge>
          </>
        ) : (
          "Select Persona"
        )}
      </Button>

      <PersonaSelectionModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
}
