"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { usePersonaStore } from "@/src/store/personaStore";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Progress } from "@/src/components/ui/progress";
import { CheckCircle, Loader2, X, ImageIcon } from "lucide-react";
import {
  validatePersonaName,
  validateImageForFinetune,
  estimateFinetuningTime,
} from "@/src/lib/persona-utils";
import Image from "next/image";
import { toast } from "sonner";

interface CreatePersonaModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function CreatePersonaModal({
  isOpen,
  onClose,
  onSuccess,
}: CreatePersonaModalProps) {
  const [name, setName] = useState("");
  const [files, setFiles] = useState<File[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [success, setSuccess] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { personas } = usePersonaStore();
  const [createdPersonaId, setCreatedPersonaId] = useState<string | null>(null);
  const [creationStep, setCreationStep] = useState<
    "idle" | "zipping" | "uploading" | "training"
  >("idle");

  // Custom validation for 10-20 images
  const validateImageCount = (fileList: File[]) => {
    if (fileList.length < 10) {
      return {
        valid: false,
        error: "Minimum 10 images required for optimal persona training",
      };
    }
    if (fileList.length > 20) {
      return {
        valid: false,
        error: "Maximum 20 images allowed to maintain training quality",
      };
    }
    return { valid: true };
  };

  const handleFileSelect = useCallback(
    (selectedFiles: FileList | null) => {
      if (!selectedFiles) return;

      const newFiles = Array.from(selectedFiles);
      const allFiles = [...files, ...newFiles];

      // Only validate individual files, not count (allow users to add incrementally)
      for (const file of newFiles) {
        const validation = validateImageForFinetune(file);
        if (!validation.valid) {
          toast.error(validation.error!);
          return;
        }
      }

      // Check if adding would exceed maximum
      if (allFiles.length > 20) {
        toast.error(
          "Maximum 20 images allowed. Please remove some images first."
        );
        return;
      }

      setFiles(allFiles);
    },
    [files]
  );

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index));
  };

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      const droppedFiles = e.dataTransfer.files;
      handleFileSelect(droppedFiles);
    },
    [handleFileSelect]
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const nameValidation = validatePersonaName(name);
    if (!nameValidation.valid) {
      toast.error(nameValidation.error!);
      return;
    }

    // Validate uploaded images with our custom validation
    const countValidation = validateImageCount(files);
    if (!countValidation.valid) {
      toast.error(countValidation.error!);
      return;
    }

    setIsCreating(true);
    setCreationStep("zipping");
    setUploadProgress(0);

    try {
      // Simulate zipping progress
      const zipProgressInterval = setInterval(() => {
        setUploadProgress((prev) => Math.min(prev + 10, 90));
      }, 200);

      const formData = new FormData();
      formData.append("name", name);

      // Add all uploaded images (ZIP upload option removed)
      files.forEach((file) => {
        formData.append("images", file);
      });

      clearInterval(zipProgressInterval);
      setUploadProgress(100);
      setCreationStep("uploading");

      const response = await fetch("/api/personas/create", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(true);
        onSuccess();
        setCreatedPersonaId(data.persona.id);
        setCreationStep("training");
        handleClose();
      } else {
        toast.error(data.error || "Failed to create persona");
        setCreationStep("idle");
      }
    } catch (err) {
      toast.error("Failed to create persona. Please try again.");
      console.error("Error creating persona:", err);
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    if (isCreating && creationStep !== "training") return;
    setName("");
    setFiles([]);
    setSuccess(false);
    setUploadProgress(0);
    setCreationStep("idle");
    onClose();
  };

  const getEstimatedTime = () => {
    if (files.length > 0) {
      return estimateFinetuningTime(files.length);
    }
    return null;
  };

  const estimatedTime = getEstimatedTime();

  useEffect(() => {
    if (createdPersonaId) {
      const interval = setInterval(async () => {
        try {
          const response = await fetch("/api/personas/sync", {
            method: "POST",
          });
          const data = await response.json();
          if (data.success) {
            const persona = personas.find((p) => p.id === createdPersonaId);
            if (persona && (persona.status === "ready" || persona.status === "error")) {
              clearInterval(interval);
            }
          }
        } catch (error) {
          console.error("Failed to sync persona status:", error);
        }
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [createdPersonaId, personas]);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="w-full sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl h-[90vh] flex flex-col overflow-hidden p-6">
        <DialogHeader className="flex-shrink-0 pb-4">
          <DialogTitle>Create New Persona</DialogTitle>
        </DialogHeader>

        {success ? (
          <div className="text-center py-8">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Persona Created!</h3>
            <p className="text-muted-foreground">
              Your persona is now training. This will take approximately{" "}
              {estimatedTime}.
            </p>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="flex flex-col h-full">
            {/* Fixed Header Section */}
            <div className="flex-shrink-0 space-y-4 pb-4">
              <div className="space-y-2">
                <Label htmlFor="name">Persona Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="e.g., John Doe"
                  disabled={isCreating}
                />
              </div>

              <div className="space-y-2">
                <Label>Upload Training Images</Label>
                <p className="text-sm text-muted-foreground">
                  Upload 10-20 high-quality images of yourself to train your
                  persona. More diverse images lead to better results.
                </p>
              </div>
            </div>

            {/* Scrollable Content Section */}
            <div className="flex-1 overflow-y-auto pr-2 space-y-4 py-2 pb-4">
              {/* Drag and Drop Area - Only show when less than 20 images */}
              {files.length < 20 && (
                <div
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                  className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                    isDragOver
                      ? "border-primary bg-primary/5"
                      : "border-muted-foreground/25 hover:border-muted-foreground/50"
                  } ${isCreating ? "pointer-events-none opacity-50" : ""}`}
                >
                  <ImageIcon className="w-10 h-10 mx-auto mb-3 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground mb-2">
                    Drag and drop images here, or click to browse
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isCreating}
                  >
                    Choose Files
                  </Button>
                </div>
              )}

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/jpeg,image/jpg,image/png,image/webp"
                onChange={(e) => handleFileSelect(e.target.files)}
                className="hidden"
              />

              {/* Image Preview Grid */}
              {files.length > 0 && (
                <div className="space-y-2">
                  <Label>Selected Images ({files.length})</Label>
                  <div className="grid grid-cols-4 sm:grid-cols-5 md:grid-cols-6 lg:grid-cols-7 gap-1">
                    {files.map((file, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-square relative rounded-lg overflow-hidden bg-muted w-16 h-16">
                          <Image
                            src={URL.createObjectURL(file)}
                            alt={`Upload ${index + 1}`}
                            fill
                            className="object-cover"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="sm"
                            className="absolute top-0.5 right-0.5 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => removeFile(index)}
                            disabled={isCreating}
                          >
                            <X className="h-2.5 w-2.5" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {isCreating && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>
                      {creationStep === "zipping"
                        ? "Compressing images..."
                        : "Uploading..."}
                    </span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}
            </div>

            {/* Fixed Footer Section */}
            <div className="flex-shrink-0 pt-4 pb-8 border-t bg-background">
              <div className="flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleClose}
                  disabled={isCreating}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={
                    isCreating ||
                    !name.trim() ||
                    files.length < 10 ||
                    files.length > 20
                  }
                  className="gap-2"
                >
                  {isCreating ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create Persona"
                  )}
                </Button>
              </div>
            </div>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
