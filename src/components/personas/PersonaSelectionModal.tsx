"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import { Button } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { Card, CardContent } from "@/src/components/ui/card";
import {
  User,
  Plus,
  Check,
  Clock,
  Loader2,
  Image as ImageIcon,
  Users,
} from "lucide-react";
import { usePersonaStore, Persona } from "@/src/store/personaStore";
import { CreatePersonaModal } from "./CreatePersonaModal";
import { PersonaGuidelinesModal } from "./PersonaGuidelinesModal";
import { getPersonaStatusDisplay } from "@/src/lib/persona-utils";
import Link from "next/link";

interface PersonaSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPersonaSelect?: (persona: Persona | null) => void;
}

export function PersonaSelectionModal({
  isOpen,
  onClose,
  onPersonaSelect,
}: PersonaSelectionModalProps) {
  const [isGuidelinesModalOpen, setIsGuidelinesModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const {
    selectedPersona,
    personas,
    isLoading,
    setSelectedPersona,
    setPersonas,
    setLoading,
  } = usePersonaStore();

  // Load personas when modal opens and set up auto-refresh for training personas
  useEffect(() => {
    if (isOpen) {
      loadPersonas();

      // Set up interval to refresh if there are training personas
      const interval = setInterval(() => {
        const trainingCount = personas.filter(
          (p) => p.status === "training" || p.status === "pending"
        ).length;

        if (trainingCount > 0) {
          loadPersonas();
        }
      }, 10000); // Refresh every 10 seconds

      return () => clearInterval(interval);
    }
  }, [isOpen, personas.length]);

  const loadPersonas = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/personas");
      const data = await response.json();

      if (data.success) {
        const personasWithDates = data.personas.map((p: any) => ({
          ...p,
          createdAt: new Date(p.createdAt),
          updatedAt: new Date(p.updatedAt),
        }));
        setPersonas(personasWithDates);
      }
    } catch (error) {
      console.error("Error loading personas:", error);
    } finally {
      setLoading(false);
    }
  };

  const handlePersonaSelect = (persona: Persona | null) => {
    setSelectedPersona(persona);
    onPersonaSelect?.(persona);
    onClose();
  };

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    loadPersonas(); // Refresh the list
  };

  const readyPersonas = personas.filter((p) => p.status === "ready");
  const trainingPersonas = personas.filter(
    (p) => p.status === "training" || p.status === "pending"
  );

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-lg xl:max-w-3xl max-h-[90vh] flex flex-col p-0">
          {/* Fixed Header */}
          <div className="flex-shrink-0 px-6 py-4 border-b bg-background">
            <DialogHeader>
              <DialogTitle>Select Persona</DialogTitle>
            </DialogHeader>
          </div>

          {/* Scrollable Content */}
          <div className="flex-1 overflow-y-auto px-6 py-4 space-y-6">
            {/* No Persona Option */}
            <Card
              className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                !selectedPersona ? "ring-2 ring-primary" : ""
              }`}
              onClick={() => handlePersonaSelect(null)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-muted-foreground" />
                    </div>
                    <div>
                      <h3 className="font-medium">No Persona</h3>
                      <p className="text-sm text-muted-foreground">
                        Generate without a specific person
                      </p>
                    </div>
                  </div>
                  {!selectedPersona && (
                    <Check className="w-5 h-5 text-primary" />
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Ready Personas */}
            {readyPersonas.length > 0 && (
              <div>
                <h3 className="font-medium mb-3 text-green-600">
                  Ready Personas ({readyPersonas.length})
                </h3>
                <div className="grid gap-3">
                  {readyPersonas.map((persona) => (
                    <Card
                      key={persona.id}
                      className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                        selectedPersona?.id === persona.id
                          ? "ring-2 ring-primary"
                          : ""
                      }`}
                      onClick={() => handlePersonaSelect(persona)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-muted rounded-full overflow-hidden">
                              {persona.generatedImageUrl ? (
                                <img
                                  src={persona.generatedImageUrl}
                                  alt={persona.name}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <User className="w-6 h-6 text-muted-foreground" />
                                </div>
                              )}
                            </div>
                            <div>
                              <h3 className="font-medium">{persona.name}</h3>
                              <div className="flex items-center gap-2">
                                <Badge variant="default" className="text-xs">
                                  Ready
                                </Badge>
                                <span className="text-xs text-muted-foreground">
                                  {persona.trainingImagesCount} images
                                </span>
                              </div>
                            </div>
                          </div>
                          {selectedPersona?.id === persona.id && (
                            <Check className="w-5 h-5 text-primary" />
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Training Personas */}
            {trainingPersonas.length > 0 && (
              <div>
                <h3 className="font-medium mb-3 text-blue-600 flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Training Personas ({trainingPersonas.length})
                </h3>
                <div className="grid gap-3">
                  {trainingPersonas.map((persona) => {
                    const statusDisplay = getPersonaStatusDisplay(
                      persona.status
                    );
                    return (
                      <Card
                        key={persona.id}
                        className="border-blue-200 bg-blue-50/50"
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                              <Loader2 className="w-6 h-6 text-blue-600 animate-spin" />
                            </div>
                            <div className="flex-1">
                              <h3 className="font-medium">{persona.name}</h3>
                              <div className="flex items-center gap-2 mb-2">
                                <Badge
                                  variant="secondary"
                                  className="text-xs bg-blue-100 text-blue-800"
                                >
                                  {statusDisplay.text}
                                </Badge>
                                <span className="text-xs text-blue-600 font-medium">
                                  Training in progress...
                                </span>
                              </div>
                              <div className="text-xs text-muted-foreground">
                                This usually takes 30-45 minutes. You can close
                                this modal and training will continue in the
                                background.
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Empty State */}
            {!isLoading && personas.length === 0 && (
              <div className="text-center py-8">
                <User className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No personas yet</h3>
                <p className="text-muted-foreground mb-6">
                  Create your first persona to get started with personalized
                  thumbnails.
                </p>
              </div>
            )}

            {/* Loading State */}
            {isLoading && (
              <div className="text-center py-8">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                <p className="text-muted-foreground">Loading personas...</p>
              </div>
            )}
          </div>

          {/* Fixed Footer */}
          <div className="flex-shrink-0 px-6 py-4 pb-6 border-t bg-background">
            <div className="flex gap-3">
              <Button
                onClick={() => setIsGuidelinesModalOpen(true)}
                className="flex-1 gap-2"
              >
                <Plus className="w-4 h-4" />
                Create Persona
              </Button>
              <Button variant="outline" asChild className="gap-2">
                <Link href="/my-personas">
                  <Users className="w-4 h-4" />
                  My Personas
                </Link>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Guidelines Modal */}
      <PersonaGuidelinesModal
        isOpen={isGuidelinesModalOpen}
        onClose={() => setIsGuidelinesModalOpen(false)}
        onContinue={() => {
          setIsGuidelinesModalOpen(false);
          setIsCreateModalOpen(true);
        }}
      />

      {/* Create Persona Modal */}
      <CreatePersonaModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSuccess={handleCreateSuccess}
      />
    </>
  );
}
