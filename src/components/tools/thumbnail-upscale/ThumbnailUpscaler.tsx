"use client";
import React, { useState, useCallback } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { Button } from "@/src/components/ui/button";
import { Label } from "@/src/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { Input } from "@/src/components/ui/input";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import {
  Loader2,
  Download,
  Upload,
  Image as ImageIcon,
  Trash2,
} from "lucide-react";
import { ImageUpload } from "@/src/components/ui/image-upload";
import Image from "next/image";

export const ThumbnailUpscaler = () => {
  const [originalImage, setOriginalImage] = useState<string | null>(null);
  const [upscaledImage, setUpscaledImage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [options, setOptions] = useState({
    upscale_factor: "2",
    creativity: "0.35",
  });

  const handleImageUpload = useCallback((imageUrl: string) => {
    setOriginalImage(imageUrl);
    setUpscaledImage(null);
    setError(null);
  }, []);

  const handleUpscale = async () => {
    if (!originalImage) return;
    setIsProcessing(true);
    setError(null);
    setUpscaledImage(null);

    try {
      const response = await fetch("/api/thumbnail-upscale", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          image_url: originalImage,
          upscale_factor: Number(options.upscale_factor),
          creativity: Number(options.creativity),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upscale image");
      }

      const result = await response.json();
      if (result.imageUrl) {
        setUpscaledImage(result.imageUrl);
      } else {
        throw new Error("Upscaled image URL not found in response");
      }
    } catch (err: any) {
      setError(err.message || "An unknown error occurred");
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = () => {
    if (!upscaledImage) return;
    const a = document.createElement("a");
    a.href = upscaledImage;
    a.download = "upscaled-thumbnail.png";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleReset = () => {
    setOriginalImage(null);
    setUpscaledImage(null);
    setError(null);
    setIsProcessing(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 overflow-y-auto">
      <div className="max-w-6xl mx-auto space-y-6 p-4">
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold text-foreground">
            Thumbnail Upscaler
          </h1>
          <p className="text-lg text-muted-foreground">
            Enhance your thumbnails to 4K resolution with AI
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload & Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!originalImage ? (
                <ImageUpload
                  onImageUpload={handleImageUpload}
                  className="min-h-[300px] border-2 border-dashed border-border rounded-lg flex flex-col items-center justify-center p-8 hover:border-primary transition-colors"
                >
                  <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-lg font-medium text-foreground mb-2">
                    Drop your image here or click to browse
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Supports JPG, PNG, WebP
                  </p>
                </ImageUpload>
              ) : (
                <div className="space-y-4">
                  <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                    <Image
                      src={originalImage}
                      alt="Original image"
                      fill
                      className="object-contain"
                    />
                  </div>
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    className="w-full text-base"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove Image
                  </Button>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="upscale_factor">Upscale Factor</Label>
                <Select
                  value={options.upscale_factor}
                  onValueChange={(value) =>
                    setOptions({ ...options, upscale_factor: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select upscale factor" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="2">2x</SelectItem>
                    <SelectItem value="4">4x</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="creativity">Creativity</Label>
                <Input
                  id="creativity"
                  type="number"
                  value={options.creativity}
                  onChange={(e) =>
                    setOptions({ ...options, creativity: e.target.value })
                  }
                  min="0.25"
                  max="0.35"
                  step="0.01"
                />
              </div>

              <Button
                onClick={handleUpscale}
                disabled={!originalImage || isProcessing}
                className="w-full text-base"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Upscaling...
                  </>
                ) : (
                  "Upscale Image"
                )}
              </Button>
            </CardContent>
          </Card>

          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                Result
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!upscaledImage && !isProcessing && !error && (
                <div className="min-h-[300px] border-2 border-dashed border-border rounded-lg flex flex-col items-center justify-center p-8">
                  <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Your upscaled image will appear here
                  </p>
                </div>
              )}

              {isProcessing && (
                <div className="min-h-[300px] border-2 border-dashed border-primary/50 rounded-lg flex flex-col items-center justify-center p-8">
                  <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-primary font-medium">
                    Upscaling your image...
                  </p>
                </div>
              )}

              {error && (
                <div className="min-h-[300px] flex flex-col items-center justify-center">
                  <Alert className="border-destructive/50 bg-destructive/10">
                    <AlertDescription className="text-destructive">
                      {error}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {upscaledImage && (
                <div className="space-y-4">
                  <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                    <Image
                      src={upscaledImage}
                      alt="Upscaled image"
                      fill
                      className="object-contain"
                    />
                  </div>
                  <Button
                    onClick={handleDownload}
                    className="w-full text-base"
                    size="lg"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download Upscaled Image
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
