"use client";

import { useEffect } from "react";
import { useMyEditedImagesStore } from "@/src/store/useMyEditedImagesStore";
import { ThumbnailsLayout } from "../shared/ThumbnailsLayout";
import { EmptyState } from "@/src/components/tools/shared/EmptyState";

export function MyEditedImagesInterface() {
  const {
    editedImages,
    loading,
    error,
    fetchEditedImages,
    setSortBy,
    setSortOrder,
  } = useMyEditedImagesStore();

  useEffect(() => {
    fetchEditedImages(1);
  }, [fetchEditedImages]);

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    setSortBy(sortBy as "createdAt" | "sourceType" | "prompt");
    setSortOrder(sortOrder as "asc" | "desc");
    fetchEditedImages(1);
  };

  const getDownloadFilename = (editedImage: any) => {
    return `edited-${editedImage.sourceType}-${editedImage.id}.jpg`;
  };

  // Transform edited images to match BaseThumbnail interface
  const transformedImages = editedImages.map((img) => ({
    id: img.id,
    imageUrl: img.editedImageUrl, // Show the edited version
    createdAt: img.createdAt,
    prompt: img.prompt,
  }));

  const emptyState = (
    <EmptyState
      title="No Edited Images Yet"
      description="You haven't edited any images yet. Start by editing thumbnails from your generated, recreated, or face swap collections."
      actionLabel="Browse My Thumbnails"
      actionHref="/my-thumbnails"
    />
  );

  return (
    <ThumbnailsLayout
      title="My Edited"
      description="View and manage all your edited images in one place"
      thumbnails={transformedImages}
      isLoading={loading}
      error={error}
      emptyState={emptyState}
      onSortChange={handleSortChange}
      getDownloadFilename={getDownloadFilename}
      hideEditButton={true} // Don't show edit button for already edited images
    />
  );
}
