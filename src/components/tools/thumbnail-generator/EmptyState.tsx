"use client";

import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/src/components/ui/card";
import { <PERSON><PERSON><PERSON>, Zap, RefreshCw } from "lucide-react";

interface EmptyStateProps {
  onActionClick?: (action: string) => void;
}

export function EmptyState({ onActionClick }: EmptyStateProps) {
  const quickActions = [
    {
      id: "dramatic",
      title: "Dramatic",
      description: "High-impact before/after style",
      icon: Zap,
      color: "bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400",
    },
    {
      id: "ctr",
      title: "High CTR",
      description: "Maximize click-through rate",
      icon: Sparkles,
      color: "bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400",
    },
  ];

  return (
    <div className="max-w-2xl mx-auto text-center space-y-6">
      <div className="space-y-4">
        <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
          <Sparkles className="w-8 h-8 text-primary" />
        </div>
        <div>
          <h2 className="text-2xl font-bold mb-2">🎨 AI Thumbnail Generator</h2>
          <p className="text-muted-foreground text-lg">
            Create Eye-Catching YouTube Thumbnails with AI
          </p>
          <p className="text-muted-foreground mt-2">
            Transform your ideas into professional thumbnails that drive clicks.
            Describe your vision and watch AI bring it to life with stunning
            visuals and compelling designs.
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Quick Start</h3>
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <Card
                key={action.id}
                className="cursor-pointer hover:shadow-lg transition-all duration-200 hover:scale-105 border-2 hover:border-primary/20"
                onClick={() => onActionClick?.(action.id)}
              >
                <CardHeader className="pb-2 text-center">
                  <div
                    className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mb-2 mx-auto`}
                  >
                    <Icon className="w-5 h-5" />
                  </div>
                  <CardTitle className="text-sm font-semibold">
                    {action.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0 pb-3 text-center">
                  <p className="text-xs text-muted-foreground">
                    {action.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
}
