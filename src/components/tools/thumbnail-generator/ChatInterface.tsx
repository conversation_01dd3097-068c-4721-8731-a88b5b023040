"use client";

import React, { useCallback } from "react";
import { ChatInterface as UnifiedChatInterface } from "@/src/components/tools/shared/ChatInterface";
import { MessageItem } from "./MessageItem";
import { EmptyState } from "./EmptyState";
import { PersonasButton } from "@/src/components/personas/PersonasButton";
import { useThumbnailGeneratorStore } from "@/src/store/thumbnailGeneratorStore";
import { usePersonaStore } from "@/src/store/personaStore";

export function ChatInterface() {
  const {
    prompt,
    isGenerating,
    setPrompt,
    addMessage,
    updateMessage,
    setIsGenerating,
    messages,
  } = useThumbnailGeneratorStore();

  const { selectedPersona } = usePersonaStore();

  const handleQuickActionClick = useCallback(
    (action: string) => {
      let promptText = "";

      switch (action) {
        case "dramatic":
          promptText =
            "Create a dramatic before/after thumbnail showing the transformation";
          break;
        case "ctr":
          promptText =
            "Design a high-CTR thumbnail that will maximize click-through rate";
          break;
        case "recreate":
          promptText =
            "Recreate a professional YouTube thumbnail with modern design";
          break;
        default:
          promptText = "";
      }

      if (promptText) {
        setPrompt(promptText);
      }
    },
    [setPrompt]
  );

  const handleSubmit = useCallback(async () => {
    if (prompt.trim().length === 0 || isGenerating) return;

    // Add user message
    addMessage({
      type: "user",
      content: prompt.trim(),
    });

    // Add assistant message with loading state
    const assistantMessageId = addMessage({
      type: "assistant",
      content: "",
      status: "generating",
    });

    setIsGenerating(true);
    const currentPrompt = prompt.trim();
    setPrompt(""); // Clear the input

    try {
      const res = await fetch("/api/generate-thumbnail", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: currentPrompt,
          persona: selectedPersona
            ? {
                id: selectedPersona.id,
                name: selectedPersona.name,
                generatedImageUrl: selectedPersona.generatedImageUrl,
                finetuneId: selectedPersona.finetuneId,
              }
            : undefined,
        }),
      });
      const data = (await res.json()) as {
        success: boolean;
        imageUrl?: string;
        responseId?: string;
        error?: string;
      };

      if (!data.success || !data.imageUrl) {
        throw new Error(data.error || "Unknown error generating thumbnail");
      }

      // Update assistant message with success
      updateMessage(assistantMessageId, {
        content: "Here's your generated thumbnail:",
        imageUrl: data.imageUrl,
        status: "success",
      });
    } catch (err: unknown) {
      console.error("Error generating thumbnail:", err);
      // Update assistant message with error
      updateMessage(assistantMessageId, {
        content: "Sorry, I couldn't generate the thumbnail.",
        status: "error",
        error: err instanceof Error ? err.message : "Unknown error",
      });
    } finally {
      setIsGenerating(false);
    }
  }, [
    prompt,
    addMessage,
    updateMessage,
    isGenerating,
    setIsGenerating,
    setPrompt,
    selectedPersona,
  ]);

  return (
    <UnifiedChatInterface
      messages={messages}
      prompt={prompt}
      isGenerating={isGenerating}
      onPromptChange={setPrompt}
      onSubmit={handleSubmit}
      placeholder="Describe your perfect thumbnail..."
      renderMessage={(message) => (
        <MessageItem key={message.id} message={message} />
      )}
      renderEmptyState={() => (
        <EmptyState onActionClick={handleQuickActionClick} />
      )}
      additionalContent={<PersonasButton />}
      tool="thumbnail"
      hasPersona={!!selectedPersona}
      showValidation={true}
      enableSpeechToText={true}
    />
  );
}
