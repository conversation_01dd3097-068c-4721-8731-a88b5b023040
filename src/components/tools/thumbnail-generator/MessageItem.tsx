"use client";

import { useState } from "react";
import Image from "next/image";
import { cn } from "@/src/lib/utils";
import { Button } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/src/components/ui/dialog";
import { Download, Expand } from "lucide-react";
import { MessageType } from "@/src/store/thumbnailGeneratorStore";
import { EditImageButton } from "@/src/components/image-editor";
import UserAvatar from "@/src/components/common/UserAvatar";
import AIAvatar from "@/src/components/common/AIAvatar";
import { ImageGenerationLoader } from "@/src/components/ui/ImageGenerationLoader";
interface MessageItemProps {
  message: MessageType;
}

export function MessageItem({ message }: MessageItemProps) {
  const [isImageHovered, setIsImageHovered] = useState(false);
  const isUser = message.type === "user";
  const isGenerating = message.status === "generating";
  const hasError = message.status === "error";

  const downloadImage = async () => {
    if (!message.imageUrl) return;

    try {
      const response = await fetch(message.imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `thumbnail-${Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Failed to download image:", error);
    }
  };

  return (
    <div
      className={cn(
        "flex gap-3 max-w-4xl",
        isUser ? "ml-auto flex-row-reverse" : "mr-auto"
      )}
    >
      {/* Avatar */}
      {isUser ? <UserAvatar /> : <AIAvatar />}

      {/* Message Content */}
      <div
        className={cn(
          "flex flex-col gap-2 max-w-[80%]",
          isUser ? "items-end" : "items-start"
        )}
      >
        {/* Text Content */}
        {message.content && (
          <div
            className={cn(
              "px-4 py-2 rounded-lg text-sm",
              isUser
                ? "bg-primary text-primary-foreground rounded-br-sm"
                : "bg-muted text-foreground rounded-bl-sm"
            )}
          >
            {message.content}
          </div>
        )}

        {/* Image Content */}
        {message.imageUrl && (
          <div className="space-y-2">
            <div
              className="relative rounded-lg overflow-hidden border shadow-sm group"
              onMouseEnter={() => setIsImageHovered(true)}
              onMouseLeave={() => setIsImageHovered(false)}
            >
              <Image
                src={message.imageUrl}
                alt="Generated thumbnail"
                width={600}
                height={338}
                className="object-cover transition-all duration-200"
              />

              {/* Expand Icon - Top Right */}
              {isImageHovered && (
                <Dialog>
                  <DialogTrigger asChild>
                    <button className="absolute top-2 right-2 bg-black/60 hover:bg-black/80 text-white p-2 rounded-full transition-all duration-200 cursor-pointer">
                      <Expand className="w-4 h-4" />
                    </button>
                  </DialogTrigger>
                  <DialogContent className="max-w-[90vw] md:max-w-[70vw] w-full p-0 overflow-hidden bg-background border shadow-lg">
                    <div className="relative">
                      <Image
                        src={message.imageUrl}
                        alt="Generated thumbnail"
                        width={1200}
                        height={675}
                        className="w-full h-auto"
                      />
                      <Button
                        onClick={downloadImage}
                        className="absolute bottom-4 right-4 bg-black/60 hover:bg-black/80 text-white"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Download
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex justify-center gap-2">
              <EditImageButton
                imageUrl={message.imageUrl}
                variant="outline"
                size="sm"
              />
              <Button
                onClick={downloadImage}
                variant="outline"
                size="sm"
                className="cursor-pointer hover:bg-muted/50"
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isGenerating && (
          <div
            className="relative rounded-lg overflow-hidden border shadow-sm"
            style={{ width: 600, height: 338 }}
          >
            <ImageGenerationLoader isLoading={true} toolType="thumbnail" />
          </div>
        )}

        {/* Error State */}
        {hasError && message.error && (
          <div className="px-4 py-2 bg-destructive/10 border border-destructive/20 rounded-lg">
            <p className="text-sm text-destructive">Error: {message.error}</p>
          </div>
        )}

        {/* Timestamp */}
        <div className="text-xs text-muted-foreground">
          {message.timestamp.toLocaleTimeString([], {
            hour: "2-digit",
            minute: "2-digit",
          })}
        </div>
      </div>
    </div>
  );
}
