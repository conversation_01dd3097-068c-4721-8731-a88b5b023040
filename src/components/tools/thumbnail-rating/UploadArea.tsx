import type React from "react";
import { Upload, CheckCircle, Target } from "lucide-react";
import Image from "next/image";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Badge } from "@/src/components/ui/badge";

interface UploadAreaProps {
  dragActive: boolean;
  imagePreview: string | null;
  input: {
    image: File | null;
    title: string;
  };
  isFormValid: boolean;
  isAnalyzing: boolean;
  handleDrag: (e: React.DragEvent<HTMLDivElement>) => void;
  handleDrop: (e: React.DragEvent<HTMLDivElement>) => void;
  handleFileInput: (e: React.ChangeEvent<HTMLInputElement>) => void;
  setInput: (input: { title: string }) => void;
  analyzeImage: () => void;
}

export function UploadArea({
  dragActive,
  imagePreview,
  input,
  isFormValid,
  isAnalyzing,
  handleDrag,
  handleDrop,
  handleFileInput,
  setInput,
  analyzeImage,
}: UploadAreaProps) {
  return (
    <div className="text-center max-w-4xl mx-auto">
      {/* Upload Area */}
      <div className="bg-card/50 backdrop-blur-sm border-2 border-dashed border-border/50 rounded-2xl p-8 mb-8 hover:border-primary/50 transition-all duration-300">
        <div
          className={`relative transition-all duration-300 ${
            dragActive ? "scale-[1.02] opacity-90" : ""
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <input
            type="file"
            accept="image/*"
            onChange={handleFileInput}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
          />

          {imagePreview ? (
            <div className="space-y-4">
              <div className="relative w-full max-w-md mx-auto">
                <div className="relative w-full aspect-video rounded-lg overflow-hidden shadow-md border">
                  <Image
                    src={imagePreview || "/placeholder.svg"}
                    alt="Thumbnail preview"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                </div>
                <Badge className="absolute -top-2 -right-2 bg-success text-success-foreground">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Ready
                </Badge>
              </div>
              <div className="bg-muted/50 rounded-lg p-3 border max-w-sm mx-auto">
                <p className="text-sm font-medium">{input.image?.name}</p>
                <p className="text-xs text-muted-foreground">
                  {((input.image?.size || 0) / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
              <p className="text-sm text-primary font-medium">
                Click below or drag & drop thumbnails to start
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="p-6 bg-primary/10 rounded-full w-20 h-20 mx-auto flex items-center justify-center">
                <Upload className="w-10 h-10 text-primary" />
              </div>
              <div>
                <p className="text-xl font-semibold mb-2">
                  <span className="text-primary">
                    Click below or drag & drop thumbnails to start
                  </span>
                </p>
                <p className="text-muted-foreground">
                  PNG, JPG, GIF, BMP up to 2MB • Recommended: 1280x720px
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Video Title Input */}
      <div className="mb-6">
        <Input
          type="text"
          value={input.title}
          onChange={(e) => setInput({ title: e.target.value })}
          placeholder="Enter your video title..."
          className="h-12 text-center text-lg max-w-2xl mx-auto"
          maxLength={100}
        />
        <p className="text-xs text-muted-foreground mt-2">
          {input.title.length}/100 characters
        </p>
      </div>

      {/* Analyze Button */}
      <Button
        onClick={analyzeImage}
        disabled={!isFormValid || isAnalyzing}
        className="w-full max-w-md h-14 text-lg font-semibold bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 disabled:opacity-50 shadow-lg mb-8"
        size="lg"
      >
        {isAnalyzing ? (
          <div className="flex items-center justify-center gap-3">
            <div className="w-5 h-5 border-2 border-primary-foreground border-t-transparent rounded-full animate-spin" />
            <span>Analyzing Thumbnail...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center gap-2">
            <Target className="w-5 h-5" />
            <span>Analyze My Thumbnail</span>
          </div>
        )}
      </Button>
    </div>
  );
}
