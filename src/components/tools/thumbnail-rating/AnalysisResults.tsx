import type React from "react";
import Image from "next/image";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/src/components/ui/card";
import { Progress } from "@/src/components/ui/progress";

import { AnalysisTabs } from "./AnalysisTabs";
import { <PERSON>, Sparkles } from "lucide-react";
import { Badge } from "@/src/components/ui/badge";
import { CircularProgress } from "@/src/components/customized/progress/progress-08";

interface AnalysisResultsProps {
  analyzedImage: string | null;
  analysis: {
    rating: {
      score: number;
      qualitative: string;
      message: string;
    };
    faces: {
      detected: boolean;
      count: number;
      person_details?: Array<{ name: string; confidence: number }>;
    };
    intrigue: Record<string, number>;
    objects?: Array<{ name: string; confidence: number }>;
    labels?: Array<{ name: string; confidence: number }>;
    improvements?: {
      thumbnail?: Array<{
        title: string;
        description: string;
      }>;
      title?: string[];
    };
    compliance?: {
      dimensions: { valid: boolean; width: number; height: number };
      minWidth: { valid: boolean; value: number };
      aspectRatio: { valid: boolean; ratio: string };
      fileSize: { valid: boolean; size: number };
      fileType: { valid: boolean; type: string };
    };
  };
  activeTab: string;
  setActiveTab: (tab: string) => void;
  getRatingColor: (score: number) => string;
  getCompliancePercentage: () => number;
}

export function AnalysisResults({
  analyzedImage,
  analysis,
  activeTab,
  setActiveTab,
  getRatingColor,
  getCompliancePercentage,
}: AnalysisResultsProps) {
  return (
    <div className="space-y-6">
      {/* Analyzed Thumbnail */}
      {analyzedImage && (
        <Card className="shadow-lg border-0 bg-card/50 backdrop-blur max-w-3xl mx-auto">
          <CardContent className="p-6">
            <div className="w-full max-w-2xl mx-auto">
              <div className="relative w-full aspect-video rounded-lg overflow-hidden shadow-lg border">
                <Image
                  src={analyzedImage || "/placeholder.svg"}
                  alt="Analyzed thumbnail"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Rating Card */}
      <Card className="shadow-lg border-0 bg-gradient-to-br from-card to-card/80 backdrop-blur max-w-4xl mx-auto">
        <CardContent className="p-8">
          <div className="text-center space-y-6">
            <div className="space-y-4 max-w-2xl mx-auto">
              <Progress
                value={(analysis.rating.score / 10) * 100}
                className="h-3"
              />
              <div className="space-y-2">
                <h2 className="text-2xl font-bold">
                  Rating:{" "}
                  <span
                    style={{
                      color: getRatingColor(analysis.rating.score),
                    }}
                  >
                    {analysis.rating.qualitative} ({analysis.rating.score}
                    /10)
                  </span>
                </h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  {analysis.rating.message}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Analysis Tabs */}
      <Card className="shadow-lg border-0 bg-card/50 backdrop-blur max-w-5xl mx-auto">
        <CardHeader className="pb-4">
          <CardTitle className="text-center">Detailed Analysis</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <AnalysisTabs
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            analysis={analysis}
          />
        </CardContent>
      </Card>
      {/* Suggested Thumbnail Improvements */}
      {analysis.improvements?.thumbnail &&
        analysis.improvements.thumbnail.length > 0 && (
          <Card className="shadow-lg border-0 bg-card/50 backdrop-blur max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <Sparkles className="w-5 h-5 text-primary" />
                Suggested Thumbnail Improvements
              </CardTitle>
            </CardHeader>
            <CardContent className="px-6 lg:px-8">
              <div className="space-y-3 max-w-3xl mx-auto">
                {analysis.improvements.thumbnail.map((improvement, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-4 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50"
                  >
                    <Badge className="bg-blue-500 text-white min-w-[1.5rem] h-6 flex items-center justify-center text-xs">
                      {index + 1}
                    </Badge>
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-blue-900 dark:text-blue-100 mb-1">
                        {improvement.title}
                      </h4>
                      <p className="text-sm leading-relaxed text-gray-700 dark:text-gray-300">
                        {improvement.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

      {/* Suggested Titles */}
      {analysis.improvements?.title &&
        analysis.improvements.title.length > 0 && (
          <Card className="shadow-lg border-0 bg-card/50 backdrop-blur max-w-4xl mx-auto">
            <CardHeader>
              <CardTitle className="flex items-center justify-center gap-2">
                <Star className="w-5 h-5 text-primary" />
                Suggested Titles
              </CardTitle>
            </CardHeader>
            <CardContent className="px-6 lg:px-8">
              <div className="space-y-3 max-w-3xl mx-auto">
                {analysis.improvements.title.map((title, index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-4 bg-purple-50/50 dark:bg-purple-950/20 rounded-lg border border-purple-200/50 dark:border-purple-800/50"
                  >
                    <Badge className="bg-purple-500 text-white min-w-[1.5rem] h-6 flex items-center justify-center text-xs">
                      {index + 1}
                    </Badge>
                    <p className="text-sm font-medium leading-relaxed">
                      {title}
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

      {/* Compliance Checklist */}
      {analysis.compliance && (
        <Card className="shadow-lg border-0 bg-card/50 backdrop-blur max-w-4xl mx-auto">
          <CardHeader>
            <div className="flex flex-col lg:flex-row items-center justify-between gap-4 w-[80%] mx-auto">
              <CardTitle className="text-center lg:text-left">
                YouTube Thumbnail Checklist
              </CardTitle>
              <div className="text-center">
                <div className="w-20 h-20 mx-auto">
                  <CircularProgress
                    value={getCompliancePercentage()}
                    size={80}
                    strokeWidth={6}
                    showLabel={true}
                    renderLabel={(progress) => `${progress}%`}
                    labelClassName="text-sm font-bold text-primary"
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-2">Compliance</p>
              </div>
            </div>
          </CardHeader>
          <CardContent className="px-6 lg:px-8">
            <div className="space-y-3 max-w-3xl mx-auto">
              {[
                {
                  label: `1280 x 720 pixels (Current: ${analysis.compliance.dimensions.width} x ${analysis.compliance.dimensions.height})`,
                  valid: analysis.compliance.dimensions.valid,
                },
                {
                  label: `Minimum width: 640 pixels (Current: ${analysis.compliance.minWidth.value}px)`,
                  valid: analysis.compliance.minWidth.valid,
                },
                {
                  label: `Recommended ratio: 16:9 (Current: ${analysis.compliance.aspectRatio.ratio})`,
                  valid: analysis.compliance.aspectRatio.valid,
                },
                {
                  label: `Maximum file size: 2MB (Current: ${(
                    analysis.compliance.fileSize.size /
                    (1024 * 1024)
                  ).toFixed(2)}MB)`,
                  valid: analysis.compliance.fileSize.valid,
                },
                {
                  label: `Accepted file types: JPG, GIF, BMP, PNG (Current: ${analysis.compliance.fileType.type})`,
                  valid: analysis.compliance.fileType.valid,
                },
              ].map((item, index) => (
                <div
                  key={index}
                  className={`flex items-center gap-3 p-3 rounded-lg border ${
                    item.valid
                      ? "bg-green-50/50 dark:bg-green-950/20 border-green-200/50 dark:border-green-800/50"
                      : "bg-red-50/50 dark:bg-red-950/20 border-red-200/50 dark:border-red-800/50"
                  }`}
                >
                  {item.valid ? (
                    <span className="w-5 h-5 text-green-600 dark:text-green-400">
                      ✓
                    </span>
                  ) : (
                    <span className="w-5 h-5 text-red-600 dark:text-red-400">
                      ✗
                    </span>
                  )}
                  <span className="text-sm">{item.label}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
