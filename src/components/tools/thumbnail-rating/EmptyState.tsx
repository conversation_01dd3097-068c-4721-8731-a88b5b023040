import type React from "react";
import { Card, CardContent } from "@/src/components/ui/card";
import { ImageIcon } from "lucide-react";

interface EmptyStateProps {
  // No props needed for this component
}

export function EmptyState({}: EmptyStateProps) {
  return (
    <Card className="shadow-lg border-0 bg-card/30 backdrop-blur max-w-2xl mx-auto">
      <CardContent className="p-12">
        <div className="text-center space-y-4">
          <div className="p-6 bg-muted/50 rounded-full w-20 h-20 mx-auto flex items-center justify-center">
            <ImageIcon className="w-10 h-10 text-muted-foreground" />
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-2">No Analysis Yet</h3>
            <p className="text-muted-foreground">
              Upload a thumbnail and enter a video title to get started with
              AI-powered analysis.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
