"use client";
import type React from "react";
import { useState, useRef } from "react";
import { useThumbnailRatingStore } from "@/src/store/thumbnailRatingStore";
import { fileToBase64, getImageDimensions } from "@/src/lib/image-utils";
import { HeroSection } from "./HeroSection";
import { UploadArea } from "./UploadArea";
import { AnalysisResults } from "./AnalysisResults";

import { EmptyState } from "./EmptyState";

interface ThumbnailRatingInterfaceProps {
  isAuthenticated?: boolean;
}

export function ThumbnailRatingInterface({
  isAuthenticated = false,
}: ThumbnailRatingInterfaceProps) {
  const {
    input,
    analysis,
    isAnalyzing,
    activeTab,
    setInput,
    setAnalysis,
    setIsAnalyzing,
    setActiveTab,
  } = useThumbnailRatingStore();

  const [dragActive, setDragActive] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [analyzedImage, setAnalyzedImage] = useState<string | null>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  const handleImageUpload = async (file: File) => {
    console.log("Uploading file:", {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: file.lastModified,
    });

    if (!file.type.startsWith("image/")) {
      alert(`Please upload a valid image file. Detected type: ${file.type}`);
      return;
    }

    if (file.size > 2 * 1024 * 1024) {
      alert(
        `File size must be less than 2MB. Current size: ${(
          file.size /
          1024 /
          1024
        ).toFixed(2)}MB`
      );
      return;
    }

    setInput({ image: file });

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageUpload(e.dataTransfer.files[0]);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleImageUpload(e.target.files[0]);
    }
  };

  const analyzeImage = async () => {
    if (!input.image || !input.title.trim()) {
      alert("Please upload an image and provide a video title");
      return;
    }

    setIsAnalyzing(true);
    setAnalysis(null);

    try {
      const imageBase64 = await fileToBase64(input.image);
      const dimensions = await getImageDimensions(input.image);

      // Store the analyzed image
      setAnalyzedImage(imageBase64);

      const response = await fetch("/api/analyze-thumbnail", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          image: imageBase64,
          title: input.title,
          fileName: input.image.name,
          fileSize: input.image.size,
          dimensions,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setAnalysis(data.analysis);
        // Reset form inputs but keep the image
        setInput({ title: "" });
        setImagePreview(null);

        // Auto-scroll to results after a short delay
        setTimeout(() => {
          resultsRef.current?.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }, 500);
      } else {
        console.error("Analysis failed:", data);
        alert(
          "Failed to analyze thumbnail: " +
            (data.error || "Unknown error occurred")
        );
      }
    } catch (error) {
      console.error("Error analyzing thumbnail:", error);
      alert("An error occurred while analyzing the thumbnail");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const isFormValid = input.image && input.title.trim();

  const getRatingColor = (score: number) => {
    if (score >= 8) return "hsl(var(--success))";
    if (score >= 6) return "hsl(var(--warning))";
    if (score >= 4) return "hsl(var(--destructive))";
    return "hsl(var(--destructive))";
  };

  // Remove unused variables - tabs and getCompliancePercentage are now handled in modular components

  const getCompliancePercentage = () => {
    if (!analysis) return 0;
    const checks = Object.values(analysis.compliance).slice(0, -1); // Exclude 'overall'
    const passedChecks = checks.filter((check) =>
      typeof check === "object" ? check.valid : check
    ).length;
    return Math.round((passedChecks / checks.length) * 100);
  };

  return (
    <div
      className={
        isAuthenticated
          ? "h-full bg-background"
          : "min-h-screen bg-gradient-to-br from-background to-muted/20"
      }
    >
      {/* Hero Section - only show for public version */}
      {!isAuthenticated && <HeroSection />}

      <div
        className={
          isAuthenticated ? "px-4 pb-8" : "container mx-auto px-4 pb-16"
        }
      >
        {/* Upload Area */}
        <UploadArea
          dragActive={dragActive}
          imagePreview={imagePreview}
          input={input}
          isFormValid={!!isFormValid}
          isAnalyzing={isAnalyzing}
          handleDrag={handleDrag}
          handleDrop={handleDrop}
          handleFileInput={handleFileInput}
          setInput={setInput}
          analyzeImage={analyzeImage}
        />
      </div>

      <div
        className={
          isAuthenticated
            ? "px-4 py-8 space-y-8 max-w-6xl mx-auto"
            : "container mx-auto px-4 py-8 space-y-8 max-w-6xl"
        }
      >
        {/* Results Section */}
        <div ref={resultsRef}>
          {analysis ? (
            <AnalysisResults
              analyzedImage={analyzedImage}
              analysis={analysis}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              getRatingColor={getRatingColor}
              getCompliancePercentage={getCompliancePercentage}
            />
          ) : (
            <EmptyState />
          )}
        </div>
      </div>
    </div>
  );
}
