import React from "react";



export function HeroSection() {
  return (
    <div className="container mx-auto px-4 py-12 mt-16">
      <div className="text-center max-w-4xl mx-auto">
        {/* Main Title */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight text-foreground mb-4 leading-tight">
            YouTube Thumbnail Analyzer
            <span className="block text-transparent bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text">
              Powered by AI
            </span>
          </h1>
          <p className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
            Get Instant Feedback. Boost Clicks. Grow Smarter.
          </p>
          <p className="text-base text-muted-foreground leading-relaxed max-w-4xl mx-auto mt-4">
            Every thumbnail you upload is scored and ranked using our advanced AI — trained on <strong>millions of high-performing YouTube thumbnails</strong>. This tool doesn&apos;t just guess... it uses real data to predict which design will get the <strong>highest click-through rate (CTR)</strong>.
          </p>
       
        </div>
      </div>
    </div>
  );
}