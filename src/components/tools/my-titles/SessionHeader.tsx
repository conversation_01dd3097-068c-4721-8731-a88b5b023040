"use client";

import React from "react";
import { Search, Plus } from "lucide-react";
import { useRouter } from "next/navigation";
import { useTitleGeneratorStore } from "@/src/store/titleGeneratorStore";

interface SessionHeaderProps {
  sessionCount: number;
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onRefresh: () => void;
}

export function SessionHeader({
  sessionCount,
  searchQuery,
  onSearchChange,
  onRefresh,
}: SessionHeaderProps) {
  const router = useRouter();
  const { createNewSession } = useTitleGeneratorStore();

  const handleNewChat = () => {
    // Clear the current session state before navigating
    createNewSession();
    router.push("/tools/title-generator");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-medium text-foreground">
          Your chat history
        </h1>
        <button
          onClick={handleNewChat}
          className="flex items-center gap-2 bg-background text-foreground border border-border px-4 py-2 rounded-lg hover:bg-accent transition-colors text-sm font-medium"
        >
          <Plus className="w-4 h-4" />
          New chat
        </button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
        <input
          type="text"
          placeholder="Search your chats..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="w-full bg-muted border border-border rounded-lg pl-10 pr-4 py-3 text-foreground placeholder-muted-foreground focus:outline-none focus:border-ring transition-colors"
        />
      </div>

      {/* Chat count */}
      <div>
        <p className="text-muted-foreground text-sm">
          You have{" "}
          <span className="text-foreground font-medium">{sessionCount}</span>{" "}
          previous chats.{" "}
        
        </p>
      </div>
    </div>
  );
}
