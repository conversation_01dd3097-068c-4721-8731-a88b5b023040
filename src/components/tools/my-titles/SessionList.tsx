"use client";

import React from "react";
import { SessionCard } from "./SessionCard";
import { TitleChatSession } from "@/src/actions/titleChatSessionAction";

interface SessionListProps {
  sessions: TitleChatSession[];
  onDeleteSession: (sessionId: string) => void;
  onRenameSession: (sessionId: string, newTitle: string) => void;
}

export function SessionList({
  sessions,
  onDeleteSession,
  onRenameSession,
}: SessionListProps) {
  return (
    <div className="space-y-1">
      {sessions.map((session) => (
        <SessionCard
          key={session.id}
          session={session}
          onDelete={() => onDeleteSession(session.id)}
          onRename={(newTitle) => onRenameSession(session.id, newTitle)}
        />
      ))}
    </div>
  );
}
