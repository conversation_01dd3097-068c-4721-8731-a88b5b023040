"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { MessageSquare, Search, Plus } from "lucide-react";

interface EmptyStateProps {
  hasSearch: boolean;
  onClearSearch: () => void;
}

export function EmptyState({ hasSearch, onClearSearch }: EmptyStateProps) {
  if (hasSearch) {
    return (
      <div className="text-center py-12 space-y-4">
        <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
          <Search className="w-8 h-8 text-muted-foreground" />
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">No Titles found</h3>
          <p className="text-muted-foreground max-w-md mx-auto">
            We couldn't find any title matching your search. Try
            different keywords or clear your search.
          </p>
        </div>
        <Button variant="outline" onClick={onClearSearch}>
          Clear Search
        </Button>
      </div>
    );
  }

  return (
    <div className="text-center py-12 space-y-6">
      <div className="w-20 h-20 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
        <MessageSquare className="w-10 h-10 text-primary" />
      </div>

      <div className="space-y-3">
        <h3 className="text-xl font-semibold">No title  yet</h3>
        <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
          Start creating engaging titles for your content! Each conversation is
          saved so you can revisit and continue where you left off.
        </p>
      </div>

      <div className="space-y-3">
        <Button asChild size="lg" className="gap-2">
          <Link href="/tools/title-generator">
            <Plus className="w-4 h-4" />
            Create Your First Title 
          </Link>
        </Button>

        <div className="text-sm text-muted-foreground">
          Generate viral titles for YouTube, articles, and more
        </div>
      </div>

   
    </div>
  );
}
