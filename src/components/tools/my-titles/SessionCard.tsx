"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { TitleChatSession } from "@/src/actions/titleChatSessionAction";
import { formatDistanceToNow } from "date-fns";
import { Button } from "@/src/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/src/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/src/components/ui/alert-dialog";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Edit2, Trash2, Loader2 } from "lucide-react";

interface SessionCardProps {
  session: TitleChatSession;
  onDelete: () => void;
  onRename: (newTitle: string) => void;
}

export function SessionCard({ session, onDelete, onRename }: SessionCardProps) {
  const router = useRouter();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editTitle, setEditTitle] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const [isRenaming, setIsRenaming] = useState(false);

  const handleChatClick = () => {
    router.push(`/tools/title-generator/${session.id}`);
  };

  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setEditTitle(session.title || session.firstPrompt);
    setIsEditDialogOpen(true);
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsDeleteDialogOpen(true);
  };

  const handleEditSave = async () => {
    if (editTitle.trim() && !isRenaming) {
      setIsRenaming(true);
      try {
        await onRename(editTitle.trim());
        setIsEditDialogOpen(false);
      } catch (error) {
        console.error("Failed to rename:", error);
      } finally {
        setIsRenaming(false);
      }
    }
  };

  const handleDeleteConfirm = async () => {
    if (!isDeleting) {
      setIsDeleting(true);
      try {
        await onDelete();
        setIsDeleteDialogOpen(false);
      } catch (error) {
        console.error("Failed to delete:", error);
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const displayTitle = session.title || session.firstPrompt;

  return (
    <>
      <div className="bg-muted hover:bg-accent p-4 rounded-lg transition-colors border border-transparent hover:border-border">
        <div className="flex justify-between items-start">
          <div className="flex-1 cursor-pointer" onClick={handleChatClick}>
            <h3 className="font-medium text-foreground mb-1 line-clamp-1">
              {displayTitle}
            </h3>
            <p className="text-muted-foreground text-sm mb-2 line-clamp-2">
              Last message{" "}
              {formatDistanceToNow(new Date(session.updatedAt), {
                addSuffix: true,
              })}
            </p>
          </div>
          <div className="flex items-center gap-1 ml-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEditClick}
              className="h-8 w-8 p-0 hover:bg-background/80"
            >
              <Edit2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDeleteClick}
              className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Chat Title</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="Enter new title..."
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleEditSave();
                  }
                }}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
              disabled={isRenaming}
            >
              Cancel
            </Button>
            <Button
              onClick={handleEditSave}
              disabled={!editTitle.trim() || isRenaming}
            >
              {isRenaming && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Chat</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this chat? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
