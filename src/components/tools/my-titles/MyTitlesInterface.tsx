"use client";

import React, { useEffect, useState } from "react";
import { SessionList } from "./SessionList";
import { SessionHeader } from "./SessionHeader";
import { EmptyState } from "./EmptyState";
import { LoadingState } from "./LoadingState";
import { TitleChatSession } from "@/src/actions/titleChatSessionAction";

export function MyTitlesInterface() {
  const [sessions, setSessions] = useState<TitleChatSession[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch("/api/title-sessions");
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || "Failed to load sessions");
      }

      setSessions(data.sessions);
    } catch (err) {
      console.error("Error loading sessions:", err);
      setError(err instanceof Error ? err.message : "Failed to load sessions");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSession = async (sessionId: string) => {
    const response = await fetch(`/api/title-sessions/${sessionId}`, {
      method: "DELETE",
    });

    const data = await response.json();
    if (!data.success) {
      throw new Error(data.error || "Failed to delete session");
    }

    // Remove the session from the list
    setSessions((prev) => prev.filter((session) => session.id !== sessionId));
  };

  const handleRenameSession = async (sessionId: string, newTitle: string) => {
    const response = await fetch(`/api/title-sessions/${sessionId}`, {
      method: "PATCH",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ title: newTitle }),
    });

    const data = await response.json();
    if (!data.success) {
      throw new Error(data.error || "Failed to rename session");
    }

    // Update the session in the list
    setSessions((prev) =>
      prev.map((session) =>
        session.id === sessionId
          ? { ...session, title: newTitle, updatedAt: new Date() }
          : session
      )
    );
  };

  // Filter sessions based on search query
  const filteredSessions = sessions.filter((session) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      session.title?.toLowerCase().includes(searchLower) ||
      session.firstPrompt.toLowerCase().includes(searchLower)
    );
  });

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center space-y-4">
          <div className="text-destructive text-lg font-medium">{error}</div>
          <button
            onClick={loadSessions}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <SessionHeader
        sessionCount={sessions.length}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        onRefresh={loadSessions}
      />

      {filteredSessions.length === 0 ? (
        <EmptyState
          hasSearch={searchQuery.length > 0}
          onClearSearch={() => setSearchQuery("")}
        />
      ) : (
        <SessionList
          sessions={filteredSessions}
          onDeleteSession={handleDeleteSession}
          onRenameSession={handleRenameSession}
        />
      )}
    </div>
  );
}
