"use client";

import { useEffect } from "react";
import { useMyBackgroundRemovalsStore } from "@/src/store/useMyBackgroundRemovalsStore";
import { ThumbnailsLayout } from "../shared/ThumbnailsLayout";
import { EmptyState } from "@/src/components/tools/shared/EmptyState";
import { ImageIcon } from "lucide-react";

export function MyBackgroundRemovalsInterface() {
  const {
    backgroundRemovals,
    loading,
    error,
    fetchBackgroundRemovals,
    setSortBy,
    setSortOrder,
  } = useMyBackgroundRemovalsStore();

  useEffect(() => {
    fetchBackgroundRemovals();
  }, [fetchBackgroundRemovals]);

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    setSortBy(sortBy as "createdAt" | "updatedAt");
    setSortOrder(sortOrder as "asc" | "desc");
    fetchBackgroundRemovals(1);
  };

  const getDownloadFilename = (backgroundRemoval: any) => {
    return `background-removed-${backgroundRemoval.id}.png`;
  };

  // Transform background removals to match the expected thumbnail format
  const transformedBackgroundRemovals = backgroundRemovals.map((item) => ({
    id: item.id,
    imageUrl: item.imageUrl,
    prompt: "", // Background removals don't have prompts
    title: undefined, // Background removals don't have titles
    tags: [], // Background removals don't have tags
    createdAt: item.createdAt,
    // Add a flag to indicate this is a background removal (no edit button)
    isBackgroundRemoval: true,
  }));

  const emptyState = (
    <EmptyState
      
      title="No Background Removals Yet"
      description="You haven't removed any backgrounds yet. Start creating amazing images with transparent backgrounds using our AI-powered tool."
      actionLabel="Remove Background"
      actionHref="/tools/background-remover"
    />
  );

  return (
    <ThumbnailsLayout
      title="My Background Removals"
      description="View and manage all your background-removed images in one place"
      createNewUrl="/tools/background-remover"
      createNewLabel="Remove Background"
      thumbnails={transformedBackgroundRemovals}
      isLoading={loading}
      error={error}
      emptyState={emptyState}
      onSortChange={handleSortChange}
      getDownloadFilename={getDownloadFilename}
      // Hide edit functionality for background removals
      hideEditButton={true}
    />
  );
}
