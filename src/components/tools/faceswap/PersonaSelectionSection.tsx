import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Users, Zap } from "lucide-react";
import { PersonasButton } from "@/src/components/personas/PersonasButton";

interface PersonaSelectionSectionProps {
  selectedPersona: any;
  isGenerating: boolean;
  uploadedImage: string | null;
  handleFaceSwap: () => void;
}

export function PersonaSelectionSection({
  selectedPersona,
  isGenerating,
  uploadedImage,
  handleFaceSwap,
}: PersonaSelectionSectionProps) {
  return (
    <Card className="border-0 shadow-lg bg-card/50 backdrop-blur">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Users className="w-5 h-5 text-primary" />
          <h2 className="text-xl font-semibold">Select Persona</h2>
        </div>

        <div className="space-y-4">
          <p className="text-sm text-muted-foreground">
            Choose a persona whose face will replace the faces in your
            thumbnail.
          </p>

          <PersonasButton />

          <Button
            onClick={handleFaceSwap}
            disabled={isGenerating || !uploadedImage || !selectedPersona}
            className="w-full h-auto py-3 text-base font-medium"
          >
            {isGenerating ? (
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 animate-pulse" />
                <span>Swapping Faces...</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                <span>Swap Faces</span>
              </div>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
