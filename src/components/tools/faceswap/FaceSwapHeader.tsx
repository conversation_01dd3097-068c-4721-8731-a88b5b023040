import { Users } from "lucide-react";

export function FaceSwapHeader() {
  return (
    <div className="text-center mb-8">
      <div className="flex items-center justify-center gap-2 mb-4">
        <div className="p-2 rounded-lg bg-primary/10">
          <Users className="w-6 h-6 text-primary" />
        </div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          Face Swap
        </h1>
      </div>
      <p className="text-muted-foreground max-w-2xl mx-auto">
        Transform your thumbnails by swapping faces with AI personas. Upload your thumbnail, 
        select a persona, and watch as faces are seamlessly replaced with stunning results.
      </p>
    </div>
  );
}