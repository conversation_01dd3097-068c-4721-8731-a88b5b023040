"use client";

import { create } from "zustand";

export type FaceSwapStatus = "idle" | "generating" | "error" | "success";

export interface FaceSwapMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  imageUrl?: string;
  timestamp: Date;
  status?: FaceSwapStatus;
  error?: string;
}

interface FaceSwapState {
  messages: FaceSwapMessage[];
  isGenerating: boolean;
  uploadedImage: string | null;
  generatedImage: string | null;
  selectedPersonaImage: string | null;

  addMessage: (message: Omit<FaceSwapMessage, "id" | "timestamp">) => string;
  updateMessage: (id: string, updates: Partial<FaceSwapMessage>) => void;
  setIsGenerating: (isGenerating: boolean) => void;
  clearMessages: () => void;
  setUploadedImage: (image: string | null) => void;
  setGeneratedImage: (image: string | null) => void;
  setSelectedPersonaImage: (image: string | null) => void;
  performFaceSwap: (
    uploadedImage: string,
    persona: {
      id: string;
      name: string;
      generatedImageUrl?: string;
      finetuneId?: string;
    }
  ) => Promise<string | null>;
}

export const useFaceSwapStore = create<FaceSwapState>((set, get) => ({
  messages: [],
  isGenerating: false,
  uploadedImage: null,
  generatedImage: null,
  selectedPersonaImage: null,

  addMessage: (message) => {
    const newMessage: FaceSwapMessage = {
      ...message,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
    };
    set((state) => ({ messages: [...state.messages, newMessage] }));
    return newMessage.id;
  },

  updateMessage: (id, updates) => {
    set((state) => ({
      messages: state.messages.map((msg) =>
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }));
  },

  setIsGenerating: (isGenerating) => set({ isGenerating }),

  clearMessages: () => set({ messages: [] }),

  setUploadedImage: (image) => set({ uploadedImage: image }),
  setGeneratedImage: (image) => set({ generatedImage: image }),
  setSelectedPersonaImage: (image) => set({ selectedPersonaImage: image }),

  performFaceSwap: async (
    uploadedImage: string,
    persona: {
      id: string;
      name: string;
      generatedImageUrl?: string;
      finetuneId?: string;
    }
  ) => {
    const { addMessage, updateMessage, setIsGenerating, setGeneratedImage } =
      get();

    setIsGenerating(true);
    setGeneratedImage(null);
    const messageId = addMessage({
      type: "user",
      content: `Starting face swap with ${persona.name}...`,
      imageUrl: uploadedImage,
      status: "generating",
    });

    try {
      // Send directly to server - let the backend handle face detection
      updateMessage(messageId, {
        content: "Sending to server for AI processing...",
      });

      const apiResponse = await fetch("/api/faceswap", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          imageUrl: uploadedImage,
          persona: {
            id: persona.id,
            name: persona.name,
            finetuneId: persona.finetuneId,
          },
        }),
      });

      updateMessage(messageId, {
        content: "AI is generating the new image...",
      });

      const data = await apiResponse.json();

      if (data.success) {
        setGeneratedImage(data.imageUrl);
        updateMessage(messageId, {
          status: "success",
          content: `Face swap completed!`,
        });

        return data.imageUrl;
      } else {
        throw new Error(data.error || "Face swap failed");
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Face swap failed";
      updateMessage(messageId, {
        status: "error",
        error: errorMessage,
      });
      throw error;
    } finally {
      setIsGenerating(false);
    }
  },
}));
