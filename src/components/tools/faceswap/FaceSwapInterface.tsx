"use client";

import { useCallback } from "react";
import { useFaceSwapStore } from "./store";
import { usePersonaStore } from "@/src/store/personaStore";
import { FaceSwapHeader } from "./FaceSwapHeader";
import { ImageUploadSection } from "./ImageUploadSection";
import { PersonaSelectionSection } from "./PersonaSelectionSection";
import { ResultsSection } from "./ResultsSection";

import { toast } from "sonner";

export function FaceSwapInterface() {
  const {
    uploadedImage,
    generatedImage,
    isGenerating,
    setUploadedImage,
    setGeneratedImage,
    clearMessages,
    performFaceSwap,
  } = useFaceSwapStore();

  const { selectedPersona } = usePersonaStore();

  const handleFileUpload = useCallback(
    (file: File) => {
      if (!file.type.startsWith("image/")) {
        toast.error("Please upload a valid image file");
        return;
      }

      if (file.size > 10 * 1024 * 1024) {
        toast.error("File size must be less than 10MB");
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setUploadedImage(result);
        toast.success("Image uploaded successfully!");
      };
      reader.readAsDataURL(file);
    },
    [setUploadedImage]
  );

  const handleFaceSwap = useCallback(async () => {
    if (!uploadedImage || !selectedPersona) {
      toast.error("Please upload an image and select a persona");
      return;
    }

    if (!selectedPersona.finetuneId && !selectedPersona.generatedImageUrl) {
      toast.error("Selected persona is not ready for face swap");
      return;
    }

    try {
      await performFaceSwap(uploadedImage, {
        id: selectedPersona.id,
        name: selectedPersona.name,
        generatedImageUrl: selectedPersona.generatedImageUrl || undefined,
        finetuneId: selectedPersona.finetuneId || undefined,
      });
      toast.success("Face swap completed!");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Face swap failed";
      toast.error(errorMessage);
    }
  }, [uploadedImage, selectedPersona, performFaceSwap]);

  const handleDownload = useCallback((imageUrl: string) => {
    const link = document.createElement("a");
    link.href = imageUrl;
    link.download = `faceswap-${Date.now()}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success("Image downloaded!");
  }, []);

  const handleReset = useCallback(() => {
    setUploadedImage(null);
    setGeneratedImage(null);
    clearMessages();
    toast.success("Reset complete!");
  }, [setUploadedImage, setGeneratedImage, clearMessages]);

  const handleRemoveImage = useCallback(() => {
    setUploadedImage(null);
    toast.success("Image removed!");
  }, [setUploadedImage]);

  return (
    <div className="h-screen bg-gradient-to-br from-background via-background to-muted/20 ">
      <div className="max-w-7xl mx-auto p-4 pb-20">
        <FaceSwapHeader />

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="space-y-6">
            <ImageUploadSection
              uploadedImage={uploadedImage}
              handleFileUpload={handleFileUpload}
              handleRemoveImage={handleRemoveImage}
            />

            <PersonaSelectionSection
              selectedPersona={selectedPersona}
              isGenerating={isGenerating}
              uploadedImage={uploadedImage}
              handleFaceSwap={handleFaceSwap}
            />
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            <ResultsSection
              uploadedImage={uploadedImage}
              generatedImage={generatedImage}
              isGenerating={isGenerating}
              selectedPersona={selectedPersona}
              handleDownload={handleDownload}
              handleReset={handleReset}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
