import { Card, CardContent } from "@/src/components/ui/card";
import { But<PERSON> } from "@/src/components/ui/button";
import { ArrowRight, Download, RefreshCw, Zap } from "lucide-react";
import Image from "next/image";
import { EditImageButton } from "@/src/components/image-editor";
import { ImageGenerationLoader } from "@/src/components/ui/ImageGenerationLoader";

interface ResultsSectionProps {
  uploadedImage: string | null;
  generatedImage: string | null;
  isGenerating: boolean;
  selectedPersona: any;
  handleDownload: (imageUrl: string) => void;
  handleReset: () => void;
}

export function ResultsSection({
  uploadedImage,
  generatedImage,
  isGenerating,
  selectedPersona,
  handleDownload,
  handleReset,
}: ResultsSectionProps) {
  return (
    <Card className="border-0 shadow-lg bg-card/50 backdrop-blur">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <ArrowRight className="w-5 h-5 text-primary" />
            <h2 className="text-xl font-semibold">Result</h2>
          </div>
          {generatedImage && (
            <Button
              onClick={handleReset}
              variant="outline"
              size="sm"
              className="gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Reset
            </Button>
          )}
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            {generatedImage && (
              <div className="flex gap-2">
                <EditImageButton
                  imageUrl={generatedImage}
                  variant="outline"
                  size="sm"
                />
                <Button
                  onClick={() => handleDownload(generatedImage)}
                  variant="outline"
                  size="sm"
                  className="gap-2"
                >
                  <Download className="w-4 h-4" />
                  Download
                </Button>
              </div>
            )}
          </div>
          <div className="relative rounded-xl overflow-hidden border border-border/50 bg-muted/20 aspect-video">
            {isGenerating ? (
              <ImageGenerationLoader
                isLoading={true}
                toolType="faceswap"
                usePersona={!!selectedPersona}
              />
            ) : generatedImage ? (
              <Image
                src={generatedImage}
                alt="Face swapped thumbnail"
                fill
                className="object-cover"
              />
            ) : (
              <div className="flex items-center justify-center h-full text-muted-foreground">
                <div className="text-center">
                  <Zap className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">
                    Your face swapped thumbnail will appear here
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
