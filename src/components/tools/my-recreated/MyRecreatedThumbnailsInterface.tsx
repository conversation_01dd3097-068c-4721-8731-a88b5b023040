"use client";

import { useEffect } from "react";
import { useMyRecreatedThumbnailsStore } from "@/src/store/useMyRecreatedThumbnailsStore";
import { ThumbnailsLayout } from "../shared/ThumbnailsLayout";
import { EmptyState } from "@/src/components/tools/shared/EmptyState";

export function MyRecreatedThumbnailsInterface() {
  const {
    thumbnails,
    loading,
    error,
    fetchThumbnails,
    setSortBy,
    setSortOrder,
  } = useMyRecreatedThumbnailsStore();

  useEffect(() => {
    fetchThumbnails(1);
  }, [fetchThumbnails]);

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    setSortBy(sortBy);
    setSortOrder(sortOrder);
    fetchThumbnails(1);
  };

  const getDownloadFilename = (thumbnail: any) => {
    return `recreated-thumbnail-${thumbnail.id}.jpg`;
  };

  const emptyState = (
    <EmptyState
      title="No Recreated Thumbnails Yet"
      description="You haven't recreated any thumbnails yet. Start by uploading an image or providing a YouTube URL to recreate amazing thumbnails."
      actionLabel="Recreate Thumbnail"
      actionHref="/tools/recreate"
    />
  );

  return (
    <ThumbnailsLayout
      title="My Recreated Thumbnails"
      description="View and manage all your recreated thumbnails in one place"
      createNewUrl="/tools/recreate"
      createNewLabel="Recreate New"
      thumbnails={thumbnails}
      isLoading={loading}
      error={error}
      emptyState={emptyState}
      onSortChange={handleSortChange}
      getDownloadFilename={getDownloadFilename}
      sourceImageType="recreated"
    />
  );
}
