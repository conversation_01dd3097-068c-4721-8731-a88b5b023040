"use client";

import React, { useState } from "react";
import { MessageType } from "@/src/store/titleGeneratorStore";
import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Co<PERSON>, Check, Loader2, Sparkles } from "lucide-react";
import { BrainstormModal } from "./BrainstormModal";
import UserAvatar from "@/src/components/common/UserAvatar";
import AIAvatar from "@/src/components/common/AIAvatar";

interface MessageItemProps {
  message: MessageType;
  onImproveTitle?: (originalTitle: string, newStyle: string) => Promise<void>;
}

export function MessageItem({ message, onImproveTitle }: MessageItemProps) {
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [brainstormModal, setBrainstormModal] = useState<{
    isOpen: boolean;
    title: string;
    style: string;
  }>({ isOpen: false, title: "", style: "" });

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error("Failed to copy text: ", err);
    }
  };

  if (message.type === "user") {
    return (
      <div className="flex justify-end gap-3 items-start">
        <Card className="max-w-[80%] bg-primary text-primary-foreground">
          <CardContent className="p-4">
            <p className="whitespace-pre-wrap">{message.content}</p>
          </CardContent>
        </Card>
        <UserAvatar />
      </div>
    );
  }

  return (
    <div className="flex justify-start gap-3 items-start">
      <AIAvatar />
      <div className="max-w-[80%] space-y-4">
        {message.status === "generating" ? (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Generating titles...</span>
          </div>
        ) : message.status === "error" ? (
          <div className="text-destructive">
            <p>{message.content}</p>
            {message.error && (
              <p className="text-sm mt-2 opacity-80">{message.error}</p>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-foreground">{message.content}</p>
            {message.titles && message.titles.length > 0 && (
              <div className="space-y-2">
                {message.titles.map((title) => (
                  <div
                    key={title.id}
                    className="group flex items-center justify-between gap-3 p-3 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1">
                      <p className="font-medium text-foreground mb-1">
                        {title.text}
                      </p>
                      <Badge
                        variant="secondary"
                        className="text-xs bg-muted text-muted-foreground border-0"
                      >
                        {title.style}
                      </Badge>
                    </div>
                    <div className="flex gap-1 shrink-0 opacity-100 transition-opacity">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          setBrainstormModal({
                            isOpen: true,
                            title: title.text,
                            style: title.style,
                          })
                        }
                        className="h-8 w-8 p-0 text-muted-foreground hover:text-primary"
                        title="Brainstorm improvements"
                      >
                        <Sparkles className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(title.text, title.id)}
                        className="h-8 w-8 p-0 text-muted-foreground hover:text-primary"
                        title="Copy title"
                      >
                        {copiedId === title.id ? (
                          <Check className="h-4 w-4" />
                        ) : (
                          <Copy className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      <BrainstormModal
        isOpen={brainstormModal.isOpen}
        onClose={() =>
          setBrainstormModal({ isOpen: false, title: "", style: "" })
        }
        originalTitle={brainstormModal.title}
        originalStyle={brainstormModal.style}
        onImprove={async (title, style) => {
          if (onImproveTitle) {
            await onImproveTitle(title, style);
          }
        }}
      />
    </div>
  );
}
