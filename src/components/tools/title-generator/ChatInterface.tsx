"use client";

import React, { useCallback, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { ChatInterface as UnifiedChatInterface } from "@/src/components/tools/shared/ChatInterface";
import { MessageItem } from "./MessageItem";
import { EmptyState } from "./EmptyState";
import { useTitleGeneratorStore } from "@/src/store/titleGeneratorStore";

export function ChatInterface() {
  const router = useRouter();
  const pathname = usePathname();

  const {
    prompt,
    isGenerating,
    setPrompt,
    addMessage,
    updateMessage,
    setIsGenerating,
    messages,
    currentSessionId,
    setCurrentSession,
    setCurrentSessionId,
  } = useTitleGeneratorStore();

  // Check if we're on a session page and sync the store if needed
  useEffect(() => {
    const sessionMatch = pathname.match(/\/tools\/title-generator\/([^\/]+)$/);
    if (sessionMatch) {
      const urlSessionId = sessionMatch[1];
      // If the URL has a session ID but the store doesn't, update the store
      if (urlSessionId !== currentSessionId) {
        setCurrentSessionId(urlSessionId);
      }
    }
    // REMOVED: Don't clear session when on base page - let user continue their conversation
  }, [pathname, currentSessionId, setCurrentSessionId]);

  const handleSubmit = useCallback(async () => {
    if (prompt.trim().length === 0 || isGenerating) return;

    const currentPrompt = prompt.trim();
    const isFirstMessage = messages.length === 0;
    let sessionId = currentSessionId;

    // Add user message to store
    addMessage({
      type: "user",
      content: currentPrompt,
    });

    // Add assistant message with loading state
    const assistantMessageId = addMessage({
      type: "assistant",
      content: "",
      status: "generating",
    });

    setIsGenerating(true);
    setPrompt(""); // Clear the input

    try {
      // If this is the first message and we don't have a session, create one
      if (isFirstMessage && !sessionId) {
        const sessionResponse = await fetch("/api/title-sessions", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            firstPrompt: currentPrompt,
          }),
        });

        const sessionData = await sessionResponse.json();
        if (!sessionData.success) {
          throw new Error(sessionData.error || "Failed to create session");
        }

        sessionId = sessionData.session.id;
        setCurrentSession({
          id: sessionData.session.id,
          title: sessionData.session.title,
          firstPrompt: sessionData.session.firstPrompt,
          createdAt: new Date(sessionData.session.createdAt),
          updatedAt: new Date(sessionData.session.updatedAt),
        });
        setCurrentSessionId(sessionId);

        // Don't navigate yet - wait until after AI response is complete
      }

      // Save user message to database if we have a session
      if (sessionId) {
        await fetch(`/api/title-sessions/${sessionId}/messages`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: "user",
            content: currentPrompt,
          }),
        });
      }

      // Generate titles
      const res = await fetch("/api/generate-titles", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt: currentPrompt,
        }),
      });

      const data = (await res.json()) as {
        success: boolean;
        titles?: {
          id: string;
          text: string;
          style: "clickbait" | "professional" | "creative";
        }[];
        responseId?: string;
        error?: string;
      };

      if (!data.success || !data.titles) {
        throw new Error(data.error || "Unknown error generating titles");
      }

      // Update assistant message with success
      updateMessage(assistantMessageId, {
        content: "Here are some title options for you:",
        titles: data.titles,
        status: "success",
      });

      // Save assistant message to database if we have a session
      if (sessionId) {
        await fetch(`/api/title-sessions/${sessionId}/messages`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: "assistant",
            content: "Here are some title options for you:",
            titles: data.titles,
            status: "success",
          }),
        });

        // Now navigate to the session URL after everything is complete
        // This ensures the user sees the full conversation flow
        if (isFirstMessage) {
          router.replace(`/tools/title-generator/${sessionId}`);
        }
      }
    } catch (err: unknown) {
      console.error("Error generating titles:", err);
      const errorMessage = err instanceof Error ? err.message : "Unknown error";

      // Update assistant message with error
      updateMessage(assistantMessageId, {
        content: "Sorry, I couldn't generate titles.",
        status: "error",
        error: errorMessage,
      });

      // Save error message to database if we have a session
      if (sessionId) {
        await fetch(`/api/title-sessions/${sessionId}/messages`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: "assistant",
            content: "Sorry, I couldn't generate titles.",
            status: "error",
            error: errorMessage,
          }),
        });

        // Navigate to session URL even on error for first message
        if (isFirstMessage) {
          router.replace(`/tools/title-generator/${sessionId}`);
        }
      }
    } finally {
      setIsGenerating(false);
    }
  }, [
    prompt,
    addMessage,
    updateMessage,
    isGenerating,
    setIsGenerating,
    setPrompt,
    messages.length,
    currentSessionId,
    setCurrentSession,
    setCurrentSessionId,
    router,
  ]);

  const handleImproveTitle = useCallback(
    async (originalTitle: string, newStyle: string) => {
      if (isGenerating) return;

      const userContent = `Improve this title with ${newStyle} style: "${originalTitle}"`;

      // Add user message showing the improvement request
      addMessage({
        type: "user",
        content: userContent,
      });

      // Add assistant message with loading state
      const assistantMessageId = addMessage({
        type: "assistant",
        content: "",
        status: "generating",
      });

      setIsGenerating(true);

      try {
        // Save user message to database if we have a session
        if (currentSessionId) {
          await fetch(`/api/title-sessions/${currentSessionId}/messages`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              type: "user",
              content: userContent,
            }),
          });
        }
        const res = await fetch("/api/improve-title", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            originalTitle,
            style: newStyle,
          }),
        });
        const data = (await res.json()) as {
          success: boolean;
          title?: {
            id: string;
            text: string;
            style: string;
          };
          error?: string;
        };

        if (!data.success || !data.title) {
          throw new Error(data.error || "Unknown error improving title");
        }

        const assistantContent = `Here's your improved ${newStyle} title:`;
        const improvedTitles = [
          {
            id: data.title.id,
            text: data.title.text,
            style: newStyle as
              | "seo"
              | "dramatic"
              | "curiosity"
              | "revealed"
              | "snappy"
              | "emphasize"
              | "clickbait"
              | "professional"
              | "creative",
          },
        ];

        // Update assistant message with the improved title
        updateMessage(assistantMessageId, {
          content: assistantContent,
          titles: improvedTitles,
          status: "success",
        });

        // Save assistant message to database if we have a session
        if (currentSessionId) {
          await fetch(`/api/title-sessions/${currentSessionId}/messages`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              type: "assistant",
              content: assistantContent,
              titles: improvedTitles,
              status: "success",
            }),
          });
        }
      } catch (err: unknown) {
        console.error("Error improving title:", err);
        const errorMessage =
          err instanceof Error ? err.message : "Unknown error";

        // Update assistant message with error
        updateMessage(assistantMessageId, {
          content: "Sorry, I couldn't improve the title.",
          status: "error",
          error: errorMessage,
        });

        // Save error message to database if we have a session
        if (currentSessionId) {
          await fetch(`/api/title-sessions/${currentSessionId}/messages`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              type: "assistant",
              content: "Sorry, I couldn't improve the title.",
              status: "error",
              error: errorMessage,
            }),
          });
        }
      } finally {
        setIsGenerating(false);
      }
    },
    [addMessage, updateMessage, isGenerating, setIsGenerating, currentSessionId]
  );

  return (
    <UnifiedChatInterface
      messages={messages}
      prompt={prompt}
      isGenerating={isGenerating}
      onPromptChange={setPrompt}
      onSubmit={handleSubmit}
      placeholder="Describe your video or content to generate title options..."
      renderMessage={(message) => (
        <MessageItem
          key={message.id}
          message={message}
          onImproveTitle={handleImproveTitle}
        />
      )}
      renderEmptyState={() => <EmptyState />}
      tool="title"
      enableSpeechToText={true}
    />
  );
}
