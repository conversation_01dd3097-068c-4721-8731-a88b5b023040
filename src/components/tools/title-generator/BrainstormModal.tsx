"use client";

import React from "react";
import { Dialog, DialogContent } from "@/src/components/ui/dialog";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import {
  Search,
  Zap,
  Eye,
  Clock,
  Target,
  MousePointer,
  Briefcase,
  Lightbulb,
  X,
} from "lucide-react";

interface BrainstormModalProps {
  isOpen: boolean;
  onClose: () => void;
  originalTitle: string;
  originalStyle: string;
  onImprove: (title: string, style: string) => Promise<void>;
}

const styleOptions = [
  {
    id: "seo",
    title: "SEO",
    description: "Search optimized",
    icon: Search,
    color:
      "bg-green-500/20 hover:bg-green-500/30 border-green-500/50 text-green-300",
  },
  {
    id: "dramatic",
    title: "Dramatic",
    description: "High impact",
    icon: Zap,
    color: "bg-red-500/20 hover:bg-red-500/30 border-red-500/50 text-red-300",
  },
  {
    id: "curiosity",
    title: "Curiosity",
    description: "Intriguing",
    icon: Eye,
    color:
      "bg-purple-500/20 hover:bg-purple-500/30 border-purple-500/50 text-purple-300",
  },
  {
    id: "revealed",
    title: "Revealed",
    description: "Secrets exposed",
    icon: Target,
    color:
      "bg-orange-500/20 hover:bg-orange-500/30 border-orange-500/50 text-orange-300",
  },
  {
    id: "snappy",
    title: "Snappy",
    description: "Short & punchy",
    icon: Clock,
    color:
      "bg-blue-500/20 hover:bg-blue-500/30 border-blue-500/50 text-blue-300",
  },
  {
    id: "emphasize",
    title: "Emphasize",
    description: "Bold & striking",
    icon: Target,
    color:
      "bg-yellow-500/20 hover:bg-yellow-500/30 border-yellow-500/50 text-yellow-300",
  },
  {
    id: "clickbait",
    title: "Clickbait",
    description: "High CTR",
    icon: MousePointer,
    color:
      "bg-pink-500/20 hover:bg-pink-500/30 border-pink-500/50 text-pink-300",
  },
  {
    id: "professional",
    title: "Professional",
    description: "Authoritative",
    icon: Briefcase,
    color:
      "bg-gray-500/20 hover:bg-gray-500/30 border-gray-500/50 text-gray-300",
  },
  {
    id: "creative",
    title: "Creative",
    description: "Artistic",
    icon: Lightbulb,
    color:
      "bg-indigo-500/20 hover:bg-indigo-500/30 border-indigo-500/50 text-indigo-300",
  },
];

export function BrainstormModal({
  isOpen,
  onClose,
  originalTitle,
  originalStyle,
  onImprove,
}: BrainstormModalProps) {
  const handleStyleSelect = async (styleId: string) => {
    try {
      onClose();
      await onImprove(originalTitle, styleId);
    } catch (error) {
      console.error("Error improving title:", error);
      // Still close modal even if there's an error
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg bg-gray-900/95 backdrop-blur-sm border-gray-700">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">
              Brainstorm Title Improvements
            </h2>
          </div>

          {/* Original Title */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-gray-400">
              Original Title
            </h3>
            <div className="p-3 bg-gray-800/50 rounded-lg border border-gray-700">
              <p className="font-medium text-white text-sm">
                "{originalTitle}"
              </p>
              <Badge
                variant="secondary"
                className="mt-2 bg-purple-500/20 text-purple-300 border-purple-500/50"
              >
                {originalStyle}
              </Badge>
            </div>
          </div>

          {/* Style Options */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-300">
              Choose a style to improve this title
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {styleOptions.map((style) => {
                const IconComponent = style.icon;
                return (
                  <Button
                    key={style.id}
                    variant="outline"
                    className={`h-16 p-2 flex flex-col items-center gap-1 hover:scale-105 transition-all duration-200 ${style.color} border`}
                    onClick={() => handleStyleSelect(style.id)}
                  >
                    <IconComponent className="h-4 w-4" />
                    <div className="text-center">
                      <div className="font-medium text-xs">{style.title}</div>
                      <div className="text-[10px] opacity-80">
                        {style.description}
                      </div>
                    </div>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Footer */}
          <div className="text-xs text-gray-500 flex items-center gap-1 pt-2">
            <Target className="h-3 w-3" />
            Click on any style to generate an improved version of your title
            using that approach.
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
