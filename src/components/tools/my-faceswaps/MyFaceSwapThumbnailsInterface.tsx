"use client";

import { useEffect } from "react";
import { useMyFaceSwapThumbnailsStore } from "@/src/store/useMyFaceSwapThumbnailsStore";
import { ThumbnailsLayout } from "../shared/ThumbnailsLayout";
import { EmptyState } from "@/src/components/tools/shared/EmptyState";

export function MyFaceSwapThumbnailsInterface() {
  const { thumbnails, isLoading, error, fetchThumbnails } =
    useMyFaceSwapThumbnailsStore();

  useEffect(() => {
    fetchThumbnails(1);
  }, [fetchThumbnails]);

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    // Face swap store doesn't support sorting yet
    fetchThumbnails(1);
  };

  const getDownloadFilename = (thumbnail: any) => {
    return `faceswap-${thumbnail.id}.jpg`;
  };

  const emptyState = (
    <EmptyState
      title="No Face Swaps Yet"
      description="You haven't created any face swaps yet. Start by uploading images to swap faces and create amazing results."
      actionLabel="Create Face Swap"
      actionHref="/tools/faceswap"
    />
  );

  return (
    <ThumbnailsLayout
      title="My Face Swaps"
      description="View and manage all your face swap creations in one place"
      createNewUrl="/tools/faceswap"
      createNewLabel="Create New"
      thumbnails={thumbnails}
      isLoading={isLoading}
      error={error}
      emptyState={emptyState}
      onSortChange={handleSortChange}
      getDownloadFilename={getDownloadFilename}
      sourceImageType="faceswap"
    />
  );
}
