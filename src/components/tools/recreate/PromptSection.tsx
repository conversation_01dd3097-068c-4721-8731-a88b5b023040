import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Sparkles, RefreshCw } from "lucide-react";
import { PromptInput } from "@/src/components/ui/prompt-input";
import { PersonasButton } from "@/src/components/personas/PersonasButton";
import { usePersonaStore } from "@/src/store/personaStore";

interface PromptSectionProps {
  prompt: string;
  setPrompt: (prompt: string) => void;
  isGenerating: boolean;
  sourceImage: string | null;
  handleRecreate: () => void;
}

export function PromptSection({
  prompt,
  setPrompt,
  isGenerating,
  sourceImage,
  handleRecreate,
}: PromptSectionProps) {
  const { selectedPersona } = usePersonaStore();

  return (
    <Card className="border-0 shadow-lg bg-card/50 backdrop-blur">
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-4">
          <Sparkles className="w-5 h-5 text-primary" />
          <h2 className="text-xl font-semibold">Transformation Prompt</h2>
        </div>

        <div className="mb-4">
          <PromptInput
            value={prompt}
            onChange={setPrompt}
            onSubmit={handleRecreate}
            disabled={isGenerating || !sourceImage}
            placeholder="Describe what you'd like to change, add, or remove from the thumbnail..."
            tool="recreate"
            hasPersona={!!selectedPersona}
            showValidation={true}
          />
        </div>

        <div className="flex flex-col sm:flex-row gap-3">
          {/* Temporarily disabled persona selection for recreate */}
          {/* <PersonasButton className="flex-1" /> */}
          <Button
            onClick={handleRecreate}
            disabled={isGenerating || !sourceImage || !prompt.trim()}
            className="w-full h-auto py-3 text-base font-medium"
          >
            {isGenerating ? (
              <div className="flex items-center gap-2">
                <Sparkles className="w-4 h-4 animate-pulse" />
                <span>Recreating...</span>
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <RefreshCw className="w-4 h-4" />
                <span>Recreate</span>
              </div>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
