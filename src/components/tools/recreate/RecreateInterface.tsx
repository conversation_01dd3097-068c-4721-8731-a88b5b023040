"use client";
import React, { useState, useCallback } from "react";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useDownloadThumbnailStore } from "@/src/store/useDownloadThumbnailStore";
import { usePersonaStore } from "@/src/store/personaStore";
import { RecreateHeader } from "./RecreateHeader";
import { SourceImageSection } from "./SourceImageSection";
import { PromptSection } from "./PromptSection";
import { ResultsSection } from "./ResultsSection";

export function RecreateInterface() {
  const [activeTab, setActiveTab] = useState("link");
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [prompt, setPrompt] = useState("");

  const { url, thumbnailUrl, error, setUrl, generate } =
    useDownloadThumbnailStore();
  const { selectedPersona } = usePersonaStore();

  const handleExtractThumbnail = useCallback(() => {
    generate("maxresdefault");
  }, [generate]);

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      const validTypes = ["image/png", "image/jpeg", "image/jpg", "image/webp"];
      if (!validTypes.includes(file.type)) {
        setUploadError("Please upload a PNG, JPG, JPEG, or WebP file.");
        return;
      }

      const maxSize = 4 * 1024 * 1024;
      if (file.size > maxSize) {
        setUploadError("File size must be less than 4MB.");
        return;
      }

      setUploadError(null);
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    },
    []
  );

  const handleRecreate = useCallback(async () => {
    const imageToUse = activeTab === "link" ? thumbnailUrl : uploadedImage;

    if (!imageToUse) {
      setUploadError(
        "Please select an image first (either extract from YouTube or upload one)."
      );
      return;
    }

    if (!prompt.trim()) {
      setUploadError(
        "Please enter a prompt describing what you'd like to change."
      );
      return;
    }

    setUploadError(null);
    setIsGenerating(true);
    const currentPrompt = prompt.trim();
    const sourceType = activeTab === "link" ? "youtube_url" : "upload_file";

    try {
      const res = await fetch("/api/recreate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          imageUrl: imageToUse,
          prompt: currentPrompt,
          personaId: selectedPersona?.id,
          sourceType,
        }),
      });

      const data = await res.json();

      if (!data.success || !data.imageUrl) {
        throw new Error(data.error || "Unknown error recreating thumbnail");
      }

      setGeneratedImage(data.imageUrl);
    } catch (err) {
      console.error("Error recreating thumbnail:", err);
      setUploadError(
        err instanceof Error ? err.message : "Failed to recreate thumbnail"
      );
    } finally {
      setIsGenerating(false);
    }
  }, [activeTab, thumbnailUrl, uploadedImage, prompt, selectedPersona]);

  const handleDownload = useCallback(async (imageUrl: string) => {
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `recreated-thumbnail-${Date.now()}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Download failed:", error);
    }
  }, []);

  const handleReset = () => {
    setGeneratedImage(null);
    setUploadedImage(null);
    setPrompt("");
    setUploadError(null);
    setUrl("");
  };

  const handleRemoveImage = () => {
    if (activeTab === "link") {
      setUrl("");
      // Also clear the thumbnailUrl from the store
      useDownloadThumbnailStore.getState().reset();
    } else {
      setUploadedImage(null);
    }
    setUploadError(null);
  };

  const sourceImage = activeTab === "link" ? thumbnailUrl : uploadedImage;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20 ">
      <div className="max-w-7xl mx-auto p-4">
        <RecreateHeader />

        <div className="grid lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="space-y-6">
            <SourceImageSection
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              url={url}
              setUrl={setUrl}
              thumbnailUrl={thumbnailUrl}
              uploadedImage={uploadedImage}
              handleExtractThumbnail={handleExtractThumbnail}
              handleFileUpload={handleFileUpload}
              handleRemoveImage={handleRemoveImage}
            />

            <PromptSection
              prompt={prompt}
              setPrompt={setPrompt}
              isGenerating={isGenerating}
              sourceImage={sourceImage}
              handleRecreate={handleRecreate}
            />

            {/* Errors */}
            {(error || uploadError) && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error || uploadError}</AlertDescription>
              </Alert>
            )}
          </div>

          {/* Results Section */}
          <div className="space-y-6">
            <ResultsSection
              generatedImage={generatedImage}
              isGenerating={isGenerating}
              handleDownload={handleDownload}
              handleReset={handleReset}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
