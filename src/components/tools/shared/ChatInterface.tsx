"use client";

import React, { useRef, useEffect } from "react";
import { PromptInput } from "@/src/components/ui/prompt-input";

// Generic message type that both generators can extend
export interface BaseMessage {
  id: string;
  type: "user" | "assistant";
  content: string;
  status?: "generating" | "success" | "error";
  error?: string;
}

// Props for the unified chat interface
interface ChatInterfaceProps<T extends BaseMessage> {
  messages: T[];
  prompt: string;
  isGenerating: boolean;
  onPromptChange: (value: string) => void;
  onSubmit: () => void;
  placeholder?: string;
  renderMessage: (message: T) => React.ReactNode;
  renderEmptyState?: () => React.ReactNode;
  additionalContent?: React.ReactNode;
  // Validation props
  tool?: "thumbnail" | "recreate" | "faceswap" | "title";
  hasPersona?: boolean;
  showValidation?: boolean;
  // Speech-to-text props
  enableSpeechToText?: boolean;
}

// Message list component
function MessageList<T extends BaseMessage>({
  messages,
  renderMessage,
  renderEmptyState,
}: {
  messages: T[];
  renderMessage: (message: T) => React.ReactNode;
  renderEmptyState?: () => React.ReactNode;
}) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  if (messages.length === 0 && renderEmptyState) {
    return (
      <>
        {renderEmptyState()}
        <div ref={messagesEndRef} className="h-4" />
      </>
    );
  }

  return (
    <>
      {messages.map((message) => renderMessage(message))}
      <div ref={messagesEndRef} className="h-4" />
    </>
  );
}

// Main unified chat interface
export function ChatInterface<T extends BaseMessage>({
  messages,
  prompt,
  isGenerating,
  onPromptChange,
  onSubmit,
  placeholder,
  renderMessage,
  renderEmptyState,
  additionalContent,
  tool = "thumbnail",
  hasPersona = false,
  showValidation = true,
  enableSpeechToText = false,
}: ChatInterfaceProps<T>) {
  return (
    <div className="flex flex-col h-full w-full">
      {/* Messages area */}
      <div className="flex-1 overflow-y-auto pt-12">
        <div className="max-w-4xl mx-auto px-4 py-6 space-y-6">
          <MessageList
            messages={messages}
            renderMessage={renderMessage}
            renderEmptyState={renderEmptyState}
          />
        </div>
      </div>

      {/* Input area */}
      <div>
        <div className="max-w-4xl mx-auto p-4 bg-transparent">
          {additionalContent && <div className="mb-3">{additionalContent}</div>}
          <PromptInput
            value={prompt}
            onChange={onPromptChange}
            onSubmit={onSubmit}
            disabled={isGenerating}
            placeholder={placeholder}
            tool={tool}
            hasPersona={hasPersona}
            showValidation={showValidation}
            enableSpeechToText={enableSpeechToText}
          />
        </div>
      </div>
    </div>
  );
}
