"use client";

import { ReactNode, useState } from "react";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { Download, Edit, Expand, Plus } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { EditImageButton } from "@/src/components/image-editor";
import { downloadImage } from "@/src/lib/imageUtils";

export interface BaseThumbnail {
  id: string;
  imageUrl: string;
  createdAt: string;
  prompt?: string;
}

export interface ThumbnailsLayoutProps<T extends BaseThumbnail> {
  title: string;
  description?: string;
  createNewUrl?: string;
  createNewLabel?: string;
  thumbnails: T[];
  isLoading: boolean;
  error: string | null;
  emptyState: ReactNode;
  onSortChange?: (sortBy: string, sortOrder: string) => void;
  renderThumbnailCard?: (thumbnail: T) => ReactNode;
  getDownloadFilename?: (thumbnail: T) => string;
  formatDate?: (dateString: string) => string;
  hideEditButton?: boolean;
  // Metadata for edit functionality
  sourceImageType?:
    | "generated"
    | "recreated"
    | "faceswap"
    | "background_removal";
}

export function ThumbnailsLayout<T extends BaseThumbnail>({
  title,
  description,
  createNewUrl,
  createNewLabel,
  thumbnails,
  isLoading,
  error,
  emptyState,
  onSortChange,
  renderThumbnailCard,
  getDownloadFilename,
  formatDate = (dateString: string) =>
    new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }),
  hideEditButton = false,
  sourceImageType,
}: ThumbnailsLayoutProps<T>) {
  const [selectedThumbnail, setSelectedThumbnail] = useState<T | null>(null);
  const [sortBy, setSortBy] = useState("createdAt");
  const [sortOrder, setSortOrder] = useState("desc");

  const handleSortChange = (value: string) => {
    const [newSortBy, newSortOrder] = value.split("-");
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    onSortChange?.(newSortBy, newSortOrder);
  };

  const handleDownload = async (thumbnail: T) => {
    try {
      const filename =
        getDownloadFilename?.(thumbnail) || `thumbnail-${thumbnail.id}.jpg`;
      await downloadImage(thumbnail.imageUrl, filename);
    } catch (error) {
      console.error("Failed to download image:", error);
      // Fallback to direct link method if fetch fails
      const link = document.createElement("a");
      link.href = thumbnail.imageUrl;
      link.download =
        getDownloadFilename?.(thumbnail) || `thumbnail-${thumbnail.id}.jpg`;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="text-destructive mb-4">
            <svg
              className="mx-auto h-12 w-12 mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-foreground mb-2">
            Error Loading {title}
          </h3>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">{title}</h1>
          {description && (
            <p className="text-muted-foreground mt-2">{description}</p>
          )}
        </div>
        {createNewUrl && createNewLabel && (
          <Link href={createNewUrl}>
            <Button className="gap-2">
              <Plus className="h-4 w-4" />
              {createNewLabel}
            </Button>
          </Link>
        )}
      </div>

      {/* Filters */}
      <div className="flex items-center gap-4">
        <Select
          value={`${sortBy}-${sortOrder}`}
          onValueChange={handleSortChange}
        >
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="createdAt-desc">Filter by Newest</SelectItem>
            <SelectItem value="createdAt-asc">Filter by Oldest</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Content */}
      {isLoading && thumbnails.length === 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <div className="aspect-video bg-muted animate-pulse" />
              <CardContent className="p-4 space-y-2">
                <div className="h-4 bg-muted animate-pulse rounded" />
                <div className="h-3 bg-muted animate-pulse rounded w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : thumbnails.length === 0 ? (
        emptyState
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6">
          {thumbnails.map((thumbnail) =>
            renderThumbnailCard ? (
              renderThumbnailCard(thumbnail)
            ) : (
              <Card
                key={thumbnail.id}
                className="group overflow-hidden hover:shadow-lg transition-all duration-200"
              >
                <div className="relative aspect-video bg-muted">
                  <Image
                    src={thumbnail.imageUrl}
                    alt="Thumbnail"
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
                  />

                  {/* Hover overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <Button
                      variant="secondary"
                      size="sm"
                      className="h-8 w-8 p-0 bg-background/90 hover:bg-background"
                      onClick={() => setSelectedThumbnail(thumbnail)}
                    >
                      <Expand className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <CardContent className="p-4 space-y-3">
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>{formatDate(thumbnail.createdAt)}</span>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 gap-2"
                      onClick={() => handleDownload(thumbnail)}
                    >
                      <Download className="h-4 w-4" />
                      Download
                    </Button>
                    {!hideEditButton && (
                      <EditImageButton
                        imageUrl={thumbnail.imageUrl}
                        sourceImageId={thumbnail.id}
                        sourceImageType={sourceImageType}
                        variant="outline"
                        size="sm"
                        className="gap-2"
                      />
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          )}
        </div>
      )}

      {/* Modal */}
      <Dialog
        open={!!selectedThumbnail}
        onOpenChange={() => setSelectedThumbnail(null)}
      >
        <DialogContent className="w-[90%] md:w-[70%] max-w-4xl">
          <DialogHeader>
            <DialogTitle>Thumbnail Details</DialogTitle>
          </DialogHeader>

          {selectedThumbnail && (
            <div className="space-y-6">
              {/* Large Image */}
              <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                <Image
                  src={selectedThumbnail.imageUrl}
                  alt="Thumbnail"
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 90vw, 70vw"
                />
              </div>

              {/* Details */}
              <div className="space-y-4">
                {selectedThumbnail.prompt && (
                  <div>
                    <h4 className="font-medium text-foreground mb-2">
                      Prompt Used
                    </h4>
                    <p className="text-sm text-muted-foreground bg-muted p-3 rounded-lg">
                      {selectedThumbnail.prompt}
                    </p>
                  </div>
                )}

                <div>
                  <h4 className="font-medium text-foreground mb-2">
                    Date Created
                  </h4>
                  <Badge variant="outline">
                    {formatDate(selectedThumbnail.createdAt)}
                  </Badge>
                </div>

                {/* Actions */}
                <div className="flex items-center gap-3 pt-4">
                  <Button
                    className="gap-2"
                    onClick={() => handleDownload(selectedThumbnail)}
                  >
                    <Download className="h-4 w-4" />
                    Download
                  </Button>
                  {!hideEditButton && (
                    <EditImageButton
                      imageUrl={selectedThumbnail.imageUrl}
                      sourceImageId={selectedThumbnail.id}
                      sourceImageType={sourceImageType}
                      variant="outline"
                      className="gap-2"
                    >
                      <Edit className="h-4 w-4" />
                      Edit
                    </EditImageButton>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
