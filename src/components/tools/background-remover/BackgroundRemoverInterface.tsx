"use client";

import React, { useCallback } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/src/components/ui/card";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Label } from "@/src/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/src/components/ui/select";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import { Progress } from "@/src/components/ui/progress";
import {
  Loader2,
  Download,
  Upload,
  Image as ImageIcon,
  Trash2,
  Eye,
} from "lucide-react";
import Link from "next/link";
import { useBackgroundRemovalStore } from "@/src/store/backgroundRemovalStore";
import { ImageUpload } from "@/src/components/ui/image-upload";
import Image from "next/image";

export function BackgroundRemoverInterface() {
  const {
    originalImage,
    processedImage,
    status,
    error,
    isProcessing,
    format,
    progress,
    setOriginalImage,
    setFormat,
    removeBackground,
    reset,
  } = useBackgroundRemovalStore();

  const handleImageUpload = useCallback(
    (imageUrl: string) => {
      setOriginalImage(imageUrl);
    },
    [setOriginalImage]
  );

  const handleRemoveBackground = async () => {
    if (!originalImage) return;
    await removeBackground(originalImage, format);
  };

  const handleDownload = async () => {
    if (!processedImage) return;

    try {
      const response = await fetch(processedImage);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `background-removed.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  const handleReset = () => {
    reset();
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 overflow-y-auto">
      <div className="max-w-6xl mx-auto space-y-6 p-4">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-3">
            <h1 className="text-4xl font-bold text-foreground">
              Background Remover
            </h1>
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">
              4K
            </div>
          </div>
          <p className="text-lg text-muted-foreground">
            Remove backgrounds from your images instantly with AI-powered
            precision in stunning 4K resolution
          </p>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Upload Section */}
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Image
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!originalImage ? (
                <ImageUpload
                  onImageUpload={handleImageUpload}
                  className="min-h-[300px] border-2 border-dashed border-border rounded-lg flex flex-col items-center justify-center p-8 hover:border-primary transition-colors"
                >
                  <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-lg font-medium text-foreground mb-2">
                    Drop your image here or click to browse
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Supports JPG, PNG, WebP • 4K Resolution • AI-powered
                    precision
                  </p>
                </ImageUpload>
              ) : (
                <div className="space-y-4">
                  <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                    <Image
                      src={originalImage}
                      alt="Original image"
                      fill
                      className="object-contain"
                    />
                  </div>
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    className="w-full text-base"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove Image
                  </Button>
                </div>
              )}

              {/* Format Selection */}
              <div className="space-y-2">
                <Label htmlFor="format">Choose Download Format</Label>
                <Select
                  value={format}
                  onValueChange={(value: "png" | "jpg" | "webp") =>
                    setFormat(value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select format" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="png">
                      PNG (Best quality, supports transparency)
                    </SelectItem>
                    <SelectItem value="jpg">
                      JPG (Smaller file size, no transparency)
                    </SelectItem>
                    <SelectItem value="webp">
                      WebP (Modern format, good compression)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Process Button */}
              <Button
                onClick={handleRemoveBackground}
                disabled={!originalImage || isProcessing}
                className="w-full text-base"
                size="lg"
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Removing Background...
                  </>
                ) : (
                  "Remove Background"
                )}
              </Button>
            </CardContent>
          </Card>

          {/* Result Section */}
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="h-5 w-5" />
                Result
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {status === "idle" && (
                <div className="min-h-[300px] border-2 border-dashed border-border rounded-lg flex flex-col items-center justify-center p-8">
                  <ImageIcon className="h-12 w-12 text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Your processed image will appear here
                  </p>
                </div>
              )}

              {status === "processing" && (
                <div className="min-h-[300px] border-2 border-dashed border-primary/50 rounded-lg flex flex-col items-center justify-center p-8">
                  <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                  <p className="text-primary font-medium">
                    Processing your image...
                  </p>
                  {progress ? (
                    <div className="text-center mt-4 space-y-3 w-64 mx-auto">
                      <div>
                        <p className="text-sm text-muted-foreground mb-2">
                          Upload: {Math.round(progress.upload * 100)}%
                        </p>
                        <Progress
                          value={progress.upload * 100}
                          className="h-2"
                        />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground mb-2">
                          Download: {Math.round(progress.download * 100)}%
                        </p>
                        <Progress
                          value={progress.download * 100}
                          className="h-2"
                        />
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground mt-2">
                      Preparing image for processing...
                    </p>
                  )}
                </div>
              )}

              {status === "error" && error && (
                <div className="min-h-[300px] flex flex-col items-center justify-center">
                  <Alert className="border-destructive/50 bg-destructive/10">
                    <AlertDescription className="text-destructive">
                      {error}
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {status === "success" && processedImage && (
                <div className="space-y-4">
                  <div className="relative aspect-video bg-muted rounded-lg overflow-hidden">
                    <div
                      className="absolute inset-0 bg-transparent opacity-50"
                      style={{
                        backgroundImage: `url("data:image/svg+xml,%3csvg width='100' height='100' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='a' patternUnits='userSpaceOnUse' width='20' height='20'%3e%3crect fill='%23000000' width='10' height='10' opacity='0.1'/%3e%3crect fill='%23ffffff' x='10' y='10' width='10' height='10' opacity='0.1'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23a)'/%3e%3c/svg%3e")`,
                      }}
                    />
                    <Image
                      src={processedImage}
                      alt="Background removed"
                      fill
                      className="object-contain relative z-10"
                    />
                  </div>
                  <div className="space-y-3">
                    <Button
                      onClick={handleDownload}
                      className="w-full text-base"
                      size="lg"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download Image
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
