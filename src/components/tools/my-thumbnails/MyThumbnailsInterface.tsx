"use client";

import { useEffect } from "react";
import { useMyThumbnailsStore } from "@/src/store/useMyThumbnailsStore";
import { ThumbnailsLayout } from "../shared/ThumbnailsLayout";
import { EmptyState } from "@/src/components/tools/shared/EmptyState";

export function MyThumbnailsInterface() {
  const {
    thumbnails,
    loading,
    error,
    fetchThumbnails,
    setSortBy,
    setSortOrder,
  } = useMyThumbnailsStore();

  useEffect(() => {
    fetchThumbnails();
  }, [fetchThumbnails]);

  const handleSortChange = (sortBy: string, sortOrder: string) => {
    setSortBy(sortBy as "createdAt" | "title" | "prompt");
    setSortOrder(sortOrder as "asc" | "desc");
    fetchThumbnails(1);
  };

  const getDownloadFilename = (thumbnail: any) => {
    return `thumbnail-${thumbnail.id}.jpg`;
  };

  const emptyState = (
    <EmptyState
      title="No Thumbnails Yet"
      description="You haven't generated any thumbnails yet. Start creating amazing thumbnails with our AI-powered generator."
      actionLabel="Generate Thumbnail"
      actionHref="/tools/thumbnail-generator"
    />
  );

  return (
    <ThumbnailsLayout
      title="My Thumbnails"
      description="View and manage all your generated thumbnails in one place"
      createNewUrl="/tools/thumbnail-generator"
      createNewLabel="Generate New"
      thumbnails={thumbnails}
      isLoading={loading}
      error={error}
      emptyState={emptyState}
      onSortChange={handleSortChange}
      getDownloadFilename={getDownloadFilename}
      sourceImageType="generated"
    />
  );
}
