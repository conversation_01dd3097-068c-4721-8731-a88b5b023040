"use client";

import Link from "next/link";
import { useAuthStore } from "@/src/store/authStore";
import { Button } from "@/src/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/src/components/ui/navigation-menu";
import {
  Sparkles,
  RefreshCw,
  UserCheck,
  Type,
  Eraser,
  TrendingUp,
  Eye,
  Download,
  Sun,
  Moon,
} from "lucide-react";

import { useState, useEffect } from "react";
import { useTheme } from "next-themes";

// Featured tools for navigation menu
const featuredTools = [
  {
    icon: Sparkles,
    title: "Thumbnail Generator",
    description: "Generate eye-catching thumbnails from text prompts",
    href: "/tools/thumbnail-generator",
  },
  {
    icon: Type,
    title: "Title Generator",
    description: "Generate click-worthy video titles that boost CTR",
    href: "/tools/title-generator",
  },
  {
    icon: Refresh<PERSON><PERSON>,
    title: "Thumbnail Recreate",
    description: "Recreate existing thumbnails with AI enhancements",
    href: "/tools/recreate",
  },
  {
    icon: User<PERSON><PERSON><PERSON>,
    title: "Face Swap",
    description: "Swap faces in your thumbnails easily",
    href: "/tools/faceswap",
  },
  {
    icon: Eraser,
    title: "Background Remover",
    description: "Remove backgrounds from any image instantly",
    href: "/tools/background-remover",
  },
  {
    icon: TrendingUp,
    title: "Thumbnail Rating",
    description: "Get AI-powered analysis and ratings",
    href: "/thumbnail-rating",
  },
  {
    icon: Eye,
    title: "Thumbnail Preview",
    description: "Preview thumbnails across devices",
    href: "/thumbnail-preview",
  },
];

const extraTools = [
  {
    icon: Download,
    title: "YouTube Downloader",
    description: "Extract thumbnails from YouTube",
    href: "/thumbnail-download",
  },
];

export default function Navbar() {
  const [mounted, setMounted] = useState(false);
  const user = useAuthStore((state) => state.user);
  const isLoadingAuth = useAuthStore((state) => state.isLoading); // Renamed for clarity
  const { theme, setTheme } = useTheme();

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const renderAuthButtons = () => {
    if (isLoadingAuth) {
      return (
        // Use muted background for placeholder
        <div className="h-8 w-20 bg-muted rounded animate-pulse"></div>
      );
    }
    if (user) {
      return (
        <Link href="/dashboard" passHref>
          <Button variant="ghost">Dashboard</Button>
        </Link>
      );
    }
    return (
      <Link href="/auth" passHref>
        <Button variant="default">Sign In</Button>
      </Link>
    );
  };

  return (
    // Use card or background for nav, and border for shadow/separation
    <nav className="w-full bg-background border-b border-border shadow-md fixed top-0 left-0 z-40">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="flex items-center gap-2">
              <span className="font-semibold text-xl text-foreground">
                Clickworthy.ai
              </span>
            </Link>
          </div>

          {/* Centered Navigation Menu */}
          <div className="hidden md:flex flex-1 justify-center relative overflow-visible">
            <NavigationMenu className="relative">
              <NavigationMenuList>
                <NavigationMenuItem>
                  <NavigationMenuTrigger className="bg-transparent hover:bg-accent data-[state=open]:bg-accent">
                    Tools
                  </NavigationMenuTrigger>
                  <NavigationMenuContent className="min-w-[400px] bg-popover text-popover-foreground">
                    <div className="grid gap-3 p-6 md:w-[500px] lg:w-[600px] lg:grid-cols-[.75fr_1fr]">
                      <div className="row-span-3">
                        <NavigationMenuLink asChild>
                          <Link
                            className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/50 to-muted p-6 no-underline outline-none focus:shadow-md text-foreground"
                            href="/dashboard"
                          >
                            <Sparkles className="h-6 w-6 text-foreground" />
                            <div className="mb-2 mt-4 text-lg font-medium text-foreground">
                              AI-Powered Tools
                            </div>
                            <p className="text-sm leading-tight text-muted-foreground">
                              Create viral thumbnails and titles with our suite
                              of AI tools
                            </p>
                          </Link>
                        </NavigationMenuLink>
                      </div>
                      <div className="grid gap-1">
                        {featuredTools.slice(0, 3).map((tool) => (
                          <NavigationMenuLink key={tool.title} asChild>
                            <Link
                              href={tool.href}
                              className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground text-foreground"
                            >
                              <div className="flex items-center gap-2">
                                <tool.icon className="h-4 w-4 text-foreground" />
                                <div className="text-sm font-medium leading-none text-foreground">
                                  {tool.title}
                                </div>
                              </div>
                              <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                                {tool.description}
                              </p>
                            </Link>
                          </NavigationMenuLink>
                        ))}
                      </div>
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuTrigger className="bg-transparent hover:bg-accent data-[state=open]:bg-accent">
                    More Tools
                  </NavigationMenuTrigger>
                  <NavigationMenuContent className="min-w-[400px] bg-popover text-popover-foreground">
                    <div className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                      {featuredTools.slice(3).map((tool) => (
                        <NavigationMenuLink key={tool.title} asChild>
                          <Link
                            href={tool.href}
                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground text-foreground"
                          >
                            <div className="flex items-center gap-2">
                              <tool.icon className="h-4 w-4 text-foreground" />
                              <div className="text-sm font-medium leading-none text-foreground">
                                {tool.title}
                              </div>
                            </div>
                            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {tool.description}
                            </p>
                          </Link>
                        </NavigationMenuLink>
                      ))}
                      {extraTools.map((tool) => (
                        <NavigationMenuLink key={tool.title} asChild>
                          <Link
                            href={tool.href}
                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground text-foreground"
                          >
                            <div className="flex items-center gap-2">
                              <tool.icon className="h-4 w-4 text-foreground" />
                              <div className="text-sm font-medium leading-none text-foreground">
                                {tool.title}
                              </div>
                            </div>
                            <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
                              {tool.description}
                            </p>
                          </Link>
                        </NavigationMenuLink>
                      ))}
                    </div>
                  </NavigationMenuContent>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link
                      href="/about"
                      className={navigationMenuTriggerStyle()}
                    >
                      About
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>

                <NavigationMenuItem>
                  <NavigationMenuLink asChild>
                    <Link
                      href="/contact"
                      className={navigationMenuTriggerStyle()}
                    >
                      Contact
                    </Link>
                  </NavigationMenuLink>
                </NavigationMenuItem>
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Auth Buttons and Theme Toggle */}
          <div className="flex items-center gap-2">
            {/* Theme Toggle */}
            {mounted && (
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleTheme}
                className="h-9 w-9"
              >
                {theme === "dark" ? (
                  <Sun className="h-4 w-4" />
                ) : (
                  <Moon className="h-4 w-4" />
                )}
                <span className="sr-only">Toggle theme</span>
              </Button>
            )}

            {/* Auth Buttons */}
            {!mounted ? (
              // Placeholder for auth buttons before client mount - use muted background
              <div className="h-8 w-20 bg-muted rounded animate-pulse"></div>
            ) : (
              renderAuthButtons()
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
