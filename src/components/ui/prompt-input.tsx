"use client";

import React, { useRef, useEffect } from "react";
import { cn } from "@/src/lib/utils";
import { Button } from "@/src/components/ui/button";
import { Textarea } from "@/src/components/ui/textarea";
import {
  Send,
  AlertTriangle,
  AlertCircle,
  Mi<PERSON>,
  Mic<PERSON>ff,
  Volume2,
  Loader2,
} from "lucide-react";
import {
  validatePrompt,
  getPromptValidationConfig,
  type PromptValidationResult,
} from "@/src/lib/utils";
import { useSpeechToText } from "@/src/hooks/useSpeechToText";
import { formatDuration } from "@/src/services/speechToTextService";
import { toast } from "sonner";

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  onSubmit: () => void;
  disabled?: boolean;
  placeholder?: string;
  tool?: "thumbnail" | "recreate" | "faceswap" | "title";
  hasPersona?: boolean;
  showValidation?: boolean;
  enableSpeechToText?: boolean; // Enable speech-to-text functionality
  className?: string;
}

export function PromptInput({
  value,
  onChange,
  onSubmit,
  disabled = false,
  placeholder = "Type your message...",
  tool = "thumbnail",
  hasPersona = false,
  showValidation = true,
  enableSpeechToText = false,
  className,
}: PromptInputProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Get validation config based on tool and persona
  const validationConfig = getPromptValidationConfig(tool, hasPersona);
  const validation: PromptValidationResult = showValidation
    ? validatePrompt(value, validationConfig)
    : {
        isValid: true,
        characterCount: value.length,
        characterLimit: validationConfig.characterLimit,
      };

  // Track last transcribed text to prevent duplicates
  const lastTranscribedTextRef = useRef<string>("");

  // Speech-to-text functionality
  const speechToText = useSpeechToText({
    tool: tool === "faceswap" ? "recreate" : tool, // Map faceswap to recreate for prompts
    onTranscriptionComplete: (text) => {
      // Prevent duplicate transcriptions
      if (text === lastTranscribedTextRef.current) {
        console.log("🔄 Duplicate transcription detected, skipping");
        return;
      }

      lastTranscribedTextRef.current = text;

      // Replace existing value instead of appending to prevent loops
      onChange(text);

      // Show success toast
      toast.success("Voice transcribed successfully!", {
        description: `Transcribed: "${text.substring(0, 50)}${
          text.length > 50 ? "..." : ""
        }"`,
        duration: 3000,
      });
    },
    onError: (error) => {
      console.error("Speech-to-text error:", error);

      // Show error toast with helpful message
      toast.error("Voice transcription failed", {
        description: error.includes("microphone")
          ? "Please check your microphone permissions and try again."
          : error.includes("network") || error.includes("fetch")
          ? "Network error. Please check your connection and try again."
          : "Please try speaking more clearly or check your microphone.",
        duration: 5000,
      });
    },
    autoTranscribe: true,
  });

  // Show recording start toast
  useEffect(() => {
    if (speechToText.state.isRecording) {
      toast.info("Recording started", {
        description:
          "Speak clearly into your microphone. Click the button again to stop.",
        duration: 2000,
      });
    }
  }, [speechToText.state.isRecording]);

  // Auto-resize textarea
  const autoResize = () => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  };

  useEffect(() => {
    if (value === "" && textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (!disabled && value.trim() && validation.isValid) {
        onSubmit();
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
    autoResize();
  };

  // Determine character count color based on validation state
  const getCharacterCountColor = () => {
    if (!validation.isValid) return "text-destructive";
    if (validation.warningMessage)
      return "text-yellow-600 dark:text-yellow-400";
    return "text-muted-foreground";
  };

  // Determine if submit should be disabled
  const isSubmitDisabled =
    disabled ||
    !value.trim() ||
    !validation.isValid ||
    (enableSpeechToText &&
      (speechToText.state.isRecording || speechToText.state.isProcessing));

  return (
    <div className={cn("space-y-2", className)}>
      {/* Main input container */}
      <div
        className={cn(
          "relative flex items-end w-full border rounded-lg transition-colors bg-transparent",
          validation.isValid
            ? "focus-within:border-primary"
            : "border-destructive focus-within:border-destructive"
        )}
      >
        <div className="flex-1 min-w-0">
          <Textarea
            ref={textareaRef}
            value={value}
            onChange={handleChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className={cn(
              "min-h-[52px] max-h-[120px] py-3 px-4 resize-none w-full border-0 bg-transparent focus:ring-0 focus:outline-none",
              !validation.isValid && "text-destructive"
            )}
            rows={1}
            aria-invalid={!validation.isValid}
            aria-describedby={showValidation ? "prompt-validation" : undefined}
          />
        </div>

        {/* Action buttons container */}
        <div className="flex items-center gap-1 m-2">
          {/* Speech-to-text button */}
          {enableSpeechToText &&
            (speechToText.isSupported ? (
              <div className="flex items-center">
                {speechToText.state.isRecording ? (
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={speechToText.stopRecording}
                      size="icon"
                      variant="destructive"
                      className="h-8 w-8 animate-pulse relative"
                      title={`Stop recording (${formatDuration(
                        speechToText.state.recordingDuration
                      )})`}
                    >
                      <Volume2 className="h-4 w-4" />
                      {/* Pulsing ring effect */}
                      <div className="absolute inset-0 rounded-md border-2 border-destructive animate-ping opacity-75" />
                    </Button>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-destructive rounded-full animate-pulse" />
                      <span className="text-xs text-destructive font-mono">
                        {formatDuration(speechToText.state.recordingDuration)}
                      </span>
                    </div>
                  </div>
                ) : speechToText.state.isTranscribing ? (
                  <Button
                    disabled
                    size="icon"
                    variant="outline"
                    className="h-8 w-8"
                    title="Transcribing audio..."
                  >
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </Button>
                ) : speechToText.state.isBlocked ? (
                  <Button
                    onClick={speechToText.resetCircuitBreaker}
                    size="icon"
                    variant="outline"
                    className="h-8 w-8 border-orange-500 text-orange-500 hover:bg-orange-50"
                    title={`Circuit breaker active. Click to reset and try again. (${speechToText.state.failureCount} failures)`}
                  >
                    <AlertCircle className="h-4 w-4" />
                  </Button>
                ) : (
                  <Button
                    onClick={speechToText.startRecording}
                    disabled={disabled || speechToText.state.isProcessing}
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8 hover:bg-primary/10 hover:text-primary"
                    title="Click to start voice recording"
                  >
                    <Mic className="h-4 w-4" />
                  </Button>
                )}
              </div>
            ) : (
              <Button
                disabled
                size="icon"
                variant="outline"
                className="h-8 w-8 opacity-50"
                title="Voice recording not supported in this browser"
                onClick={() => {
                  toast.error("Voice recording not supported", {
                    description:
                      "Please use a modern browser like Chrome, Firefox, or Safari.",
                    duration: 4000,
                  });
                }}
              >
                <MicOff className="h-4 w-4" />
              </Button>
            ))}

          {/* Submit button */}
          <Button
            onClick={onSubmit}
            disabled={isSubmitDisabled}
            size="icon"
            className="h-8 w-8 flex-shrink-0 rounded-md"
            variant={isSubmitDisabled ? "outline" : "default"}
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Validation feedback */}
      {showValidation && (
        <div
          id="prompt-validation"
          className="flex items-center justify-between text-sm"
        >
          {/* Error or warning message */}
          <div className="flex items-center gap-1 flex-1">
            {validation.errorMessage && (
              <>
                <AlertTriangle className="h-3 w-3 text-destructive flex-shrink-0" />
                <span className="text-destructive text-xs">
                  {validation.errorMessage}
                </span>
              </>
            )}
            {validation.warningMessage && !validation.errorMessage && (
              <>
                <AlertTriangle className="h-3 w-3 text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
                <span className="text-yellow-600 dark:text-yellow-400 text-xs">
                  {validation.warningMessage}
                </span>
              </>
            )}
          </div>

          {/* Character count */}
          <div
            className={cn(
              "text-xs font-mono flex-shrink-0 ml-2",
              getCharacterCountColor()
            )}
          >
            {validation.characterCount}/{validation.characterLimit}
          </div>
        </div>
      )}
    </div>
  );
}
