"use client";

import React, { useCallback, useState } from "react";
import { Upload, X } from "lucide-react";
import { cn } from "@/src/lib/utils";
import { toast } from "sonner";

interface ImageUploadProps {
  onImageUpload: (imageUrl: string) => void;
  className?: string;
  children?: React.ReactNode;
  accept?: string;
  maxSize?: number; // in MB
  disabled?: boolean;
}

export function ImageUpload({
  onImageUpload,
  className,
  children,
  accept = ".png,.jpg,.jpeg,.webp,image/png,image/jpeg,image/webp",
  maxSize = 10,
  disabled = false,
}: ImageUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFile = useCallback(
    (file: File) => {
      if (!file.type.startsWith("image/")) {
        toast.error("Please upload a valid image file");
        return;
      }

      if (file.size > maxSize * 1024 * 1024) {
        toast.error(`File size must be less than ${maxSize}MB`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        onImageUpload(result);
      };
      reader.readAsDataURL(file);
    },
    [onImageUpload, maxSize]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setIsDragOver(false);

      if (disabled) return;

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFile(files[0]);
      }
    },
    [handleFile, disabled]
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      if (!disabled) {
        setIsDragOver(true);
      }
    },
    [disabled]
  );

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        handleFile(file);
      }
      // Reset input value to allow uploading the same file again
      e.target.value = "";
    },
    [handleFile]
  );

  return (
    <div
      className={cn(
        "relative cursor-pointer transition-colors",
        isDragOver && "bg-blue-50 border-blue-300",
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onClick={() => {
        if (!disabled) {
          document.getElementById("file-upload")?.click();
        }
      }}
    >
      {children || (
        <div className="flex flex-col items-center justify-center p-8">
          <Upload className="h-10 w-10 text-gray-400 mb-4" />
          <p className="text-lg font-medium text-gray-600 mb-2">
            Drop your image here or click to browse
          </p>
          <p className="text-sm text-gray-500">
            Supports JPG, PNG, WebP up to {maxSize}MB
          </p>
        </div>
      )}

      <input
        id="file-upload"
        type="file"
        className="hidden"
        accept={accept}
        onChange={handleFileInput}
        disabled={disabled}
      />
    </div>
  );
}
