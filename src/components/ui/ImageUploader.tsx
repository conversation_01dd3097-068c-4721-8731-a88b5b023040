"use client";

import React, { useCallback, useState } from "react";
import { Upload, X } from "lucide-react";
import { cn } from "@/src/lib/utils";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "./button";
import Image from "next/image";

interface ImageUploaderProps {
  onImageUpload: (imageUrl: string) => void;
  className?: string;
  accept?: string;
  maxSize?: number; // in MB
  disabled?: boolean;
}

export function ImageUploader({
  onImageUpload,
  className,
  accept = ".png,.jpg,.jpeg,.webp,image/png,image/jpeg,image/webp",
  maxSize = 10,
  disabled = false,
}: ImageUploaderProps) {
  const [isDragOver, setIsDragOver] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleFile = useCallback(
    (file: File) => {
      if (!file.type.startsWith("image/")) {
        toast.error("Please upload a valid image file");
        return;
      }

      if (file.size > maxSize * 1024 * 1024) {
        toast.error(`File size must be less than ${maxSize}MB`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setImagePreview(result);
        onImageUpload(result);
      };
      reader.readAsDataURL(file);
    },
    [onImageUpload, maxSize]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setIsDragOver(false);

      if (disabled) return;

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleFile(files[0]);
      }
    },
    [handleFile, disabled]
  );

  const handleDragOver = useCallback(
    (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      if (!disabled) {
        setIsDragOver(true);
      }
    },
    [disabled]
  );

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        handleFile(file);
      }
      e.target.value = "";
    },
    [handleFile]
  );

  const handleRemoveImage = () => {
    setImagePreview(null);
    onImageUpload("");
  };

  return (
    <div
      className={cn(
        "relative border-2 border-dashed border-muted-foreground/50 rounded-lg p-8 text-center transition-colors",
        isDragOver && "bg-muted",
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <input
        type="file"
        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        onChange={handleFileInput}
        accept={accept}
        disabled={disabled}
      />
      {imagePreview ? (
        <>
          <Image
            src={imagePreview}
            alt="Image preview"
            width={200}
            height={200}
            className="mx-auto rounded-md"
          />
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2"
            onClick={handleRemoveImage}
          >
            <X className="h-4 w-4" />
          </Button>
        </>
      ) : (
        <div className="flex flex-col items-center justify-center space-y-2">
          <Upload className="h-8 w-8 text-muted-foreground" />
          <p className="text-muted-foreground">
            Drag & drop an image here, or click to select a file
          </p>
        </div>
      )}
    </div>
  );
}
