"use client";

import React from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { cn } from "@/src/lib/utils";
import { 
  Mi<PERSON>, 
  MicOff, 
  Square, 
  Loader2, 
  AlertCircle,
  Volume2 
} from "lucide-react";
import { useAudioRecording } from "@/src/hooks/useAudioRecording";
import { formatDuration } from "@/src/services/speechToTextService";

interface AudioRecorderProps {
  onRecordingComplete: (audioBlob: Blob) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "default" | "outline" | "ghost";
}

/**
 * Audio Recorder Component
 * Provides a clean interface for recording audio with visual feedback
 */
export function AudioRecorder({
  onRecordingComplete,
  onError,
  disabled = false,
  className,
  size = "md",
  variant = "outline",
}: AudioRecorderProps) {
  const {
    state,
    startRecording,
    stopRecording,
    clearRecording,
    isSupported,
  } = useAudioRecording();

  // Handle recording completion
  React.useEffect(() => {
    if (state.audioBlob && !state.isRecording && !state.isProcessing) {
      onRecordingComplete(state.audioBlob);
      clearRecording();
    }
  }, [state.audioBlob, state.isRecording, state.isProcessing, onRecordingComplete, clearRecording]);

  // Handle errors
  React.useEffect(() => {
    if (state.error) {
      onError?.(state.error);
    }
  }, [state.error, onError]);

  const handleClick = async () => {
    if (state.isRecording) {
      stopRecording();
    } else {
      await startRecording();
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      button: "h-8 w-8",
      icon: "h-3 w-3",
      text: "text-xs",
    },
    md: {
      button: "h-10 w-10",
      icon: "h-4 w-4", 
      text: "text-sm",
    },
    lg: {
      button: "h-12 w-12",
      icon: "h-5 w-5",
      text: "text-base",
    },
  };

  const config = sizeConfig[size];

  // Don't render if not supported
  if (!isSupported) {
    return (
      <Button
        disabled
        variant="outline"
        size="icon"
        className={cn(config.button, "opacity-50", className)}
        title="Audio recording not supported in this browser"
      >
        <MicOff className={config.icon} />
      </Button>
    );
  }

  // Recording state
  if (state.isRecording) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <Button
          onClick={handleClick}
          variant="destructive"
          size="icon"
          className={cn(config.button, "animate-pulse")}
          title={`Stop recording (${formatDuration(state.duration)})`}
        >
          <Square className={config.icon} />
        </Button>
        
        {/* Duration display */}
        <div className={cn("flex items-center gap-1 text-destructive", config.text)}>
          <Volume2 className={cn(config.icon, "animate-pulse")} />
          <span className="font-mono">
            {formatDuration(state.duration)}
          </span>
        </div>
      </div>
    );
  }

  // Processing state
  if (state.isProcessing) {
    return (
      <Button
        disabled
        variant={variant}
        size="icon"
        className={cn(config.button, className)}
        title="Processing recording..."
      >
        <Loader2 className={cn(config.icon, "animate-spin")} />
      </Button>
    );
  }

  // Error state
  if (state.error) {
    return (
      <Button
        onClick={clearRecording}
        variant="outline"
        size="icon"
        className={cn(config.button, "border-destructive text-destructive", className)}
        title={`Error: ${state.error}. Click to retry.`}
      >
        <AlertCircle className={config.icon} />
      </Button>
    );
  }

  // Default state (ready to record)
  return (
    <Button
      onClick={handleClick}
      disabled={disabled}
      variant={variant}
      size="icon"
      className={cn(
        config.button,
        "transition-colors hover:bg-primary/10 hover:text-primary",
        className
      )}
      title="Click to start recording"
    >
      <Mic className={config.icon} />
    </Button>
  );
}

/**
 * Compact Audio Recorder for inline use
 */
export function CompactAudioRecorder({
  onRecordingComplete,
  onError,
  disabled = false,
  className,
}: Omit<AudioRecorderProps, "size" | "variant">) {
  return (
    <AudioRecorder
      onRecordingComplete={onRecordingComplete}
      onError={onError}
      disabled={disabled}
      className={className}
      size="sm"
      variant="ghost"
    />
  );
}
