"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>rk<PERSON>, Zap, RefreshCw } from "lucide-react";
import { cn } from "@/src/lib/utils";
import { getTipSequence, ThumbnailTip } from "@/src/constants/thumbnailTips";

export interface ImageGenerationLoaderProps {
  /** Whether the loader is currently active */
  isLoading: boolean;
  /** Custom loading message */
  message?: string;
  /** Tool type for customized messaging */
  toolType?: "thumbnail" | "recreate" | "faceswap";
  /** Whether a persona is being used (affects timing estimates) */
  usePersona?: boolean;
  /** Custom class name */
  className?: string;
  /** Callback when generation completes early */
  onComplete?: () => void;
  /** Override estimated duration in seconds */
  estimatedDuration?: number;
  /** Real-time progress from API (0-100) - overrides time-based estimation */
  apiProgress?: number;
  /** Current generation stage for more detailed progress */
  stage?: "submitting" | "processing" | "generating" | "uploading" | "complete";
}

interface LoadingConfig {
  icon: React.ComponentType<{ className?: string }>;
  baseMessage: string;
  personaMessage: string;
  estimatedTime: number; // in seconds
  personaTime: number; // in seconds for persona generation
}

const TOOL_CONFIGS: Record<string, LoadingConfig> = {
  thumbnail: {
    icon: Sparkles,
    baseMessage: "Generating your thumbnail...",
    personaMessage: "Generating with persona...",
    estimatedTime: 20, // Regular Flux generation (15s) + upload (5s)
    personaTime: 30, // Fine-tuned model generation (25s) + upload (5s)
  },
  recreate: {
    icon: RefreshCw,
    baseMessage: "Recreating your thumbnail...",
    personaMessage: "Recreating with persona...",
    estimatedTime: 25, // Image-to-image takes slightly longer (20s) + upload (5s)
    personaTime: 35,
  },
  faceswap: {
    icon: Zap,
    baseMessage: "Swapping faces...",
    personaMessage: "Swapping with persona...",
    estimatedTime: 30, // Face detection (5s) + generation (20s) + upload (5s)
    personaTime: 40,
  },
};

// Stage-based progress mapping for more accurate progress indication
const STAGE_PROGRESS: Record<string, number> = {
  submitting: 5,
  processing: 15,
  generating: 80,
  uploading: 95,
  complete: 100,
};

export function ImageGenerationLoader({
  isLoading,
  message,
  toolType = "thumbnail",
  usePersona = false,
  className,
  onComplete,
  estimatedDuration,
  apiProgress,
  stage = "processing",
}: ImageGenerationLoaderProps) {
  const [progress, setProgress] = useState(0);
  const [currentTip, setCurrentTip] = useState<ThumbnailTip | null>(null);
  const [tipSequence, setTipSequence] = useState<ThumbnailTip[]>([]);
  const [tipIndex, setTipIndex] = useState(0);
  const [startTime, setStartTime] = useState<number | null>(null);
  const [isTipFading, setIsTipFading] = useState(false);

  const config = TOOL_CONFIGS[toolType];
  const Icon = config.icon;

  // Determine estimated duration
  const duration =
    estimatedDuration ||
    (usePersona ? config.personaTime : config.estimatedTime);

  // Initialize tip sequence when loading starts
  useEffect(() => {
    if (isLoading && tipSequence.length === 0) {
      const tips = getTipSequence(6); // Reduced number of tips for less frequent rotation
      setTipSequence(tips);
      setCurrentTip(tips[0]);
      setTipIndex(0);
      setStartTime(Date.now());
      setProgress(0);
    } else if (!isLoading) {
      setProgress(0);
      setStartTime(null);
      setTipSequence([]);
      setCurrentTip(null);
      setTipIndex(0);
    }
  }, [isLoading, tipSequence.length]);

  // Progress and tip rotation logic
  useEffect(() => {
    if (!isLoading || !startTime) return;

    const interval = setInterval(() => {
      const elapsed = (Date.now() - startTime) / 1000; // seconds

      // Use API progress if available, otherwise fall back to time-based estimation
      let newProgress: number;
      if (apiProgress !== undefined) {
        newProgress = apiProgress >= 100 ? 95 : 5 + apiProgress * 0.75;
      } else {
        const timeBasedProgress = Math.min((elapsed / duration) * 100, 95);
        if (stage && stage !== "processing" && STAGE_PROGRESS[stage]) {
          newProgress = Math.max(timeBasedProgress, STAGE_PROGRESS[stage]);
        } else {
          newProgress = timeBasedProgress;
        }
      }

      setProgress(newProgress);

      // Rotate tips every 10 seconds with smooth fade transition
      if (
        tipSequence.length > 0 &&
        Math.floor(elapsed) % 10 === 0 &&
        elapsed > 0 &&
        !isTipFading
      ) {
        setIsTipFading(true);
        setTimeout(() => {
          const nextIndex = (tipIndex + 1) % tipSequence.length;
          setTipIndex(nextIndex);
          setCurrentTip(tipSequence[nextIndex]);
          setTimeout(() => {
            setIsTipFading(false);
          }, 500); // Fade in duration
        }, 500); // Fade out duration
      }

      if (newProgress >= 95 && elapsed > duration && onComplete) {
        onComplete();
      }
    }, 100);

    return () => clearInterval(interval);
  }, [
    isLoading,
    startTime,
    duration,
    tipSequence,
    tipIndex,
    onComplete,
    apiProgress,
    stage,
    isTipFading,
  ]);

  // Complete progress when loading finishes
  useEffect(() => {
    if (!isLoading && progress > 0) {
      setProgress(100);
      const timeout = setTimeout(() => {
        setProgress(0);
      }, 500);
      return () => clearTimeout(timeout);
    }
  }, [isLoading, progress]);

  if (!isLoading) return null;

  const displayMessage =
    message || (usePersona ? config.personaMessage : config.baseMessage);

  return (
    <div className={cn("w-full h-full relative", className)}>
      <div className="w-full h-full bg-muted/20 rounded-xl border border-border/50 relative overflow-hidden">
        <div className="absolute left-0 top-0 w-1 h-full bg-muted/30 rounded-l-xl">
          <div
            className="w-full bg-primary transition-all duration-500 ease-out rounded-l-xl"
            style={{ height: `${progress}%` }}
          />
        </div>

        <div
          className="absolute top-0 left-0 h-full bg-primary/10 transition-all duration-500 ease-out"
          style={{ width: `${progress}%` }}
        />

        <div className="flex flex-col items-center justify-center h-full p-8 relative z-10">
          {currentTip && (
            <div className="text-center mb-6 overflow-hidden">
              <div
                className={cn(
                  "text-sm text-muted-foreground transition-opacity duration-500 ease-in-out",
                  isTipFading ? "opacity-0" : "opacity-100"
                )}
              >
                🧠 {currentTip.text}
              </div>
            </div>
          )}

          <div className="text-center">
            <div className="text-8xl md:text-9xl font-bold text-foreground/60">
              {Math.round(progress)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
