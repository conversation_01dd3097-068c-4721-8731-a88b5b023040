"use client";

import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { Button } from "./button";

export default function TopBanner() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check if banner was dismissed in this session
    const bannerDismissed = sessionStorage.getItem("topBannerDismissed");

    if (!bannerDismissed) {
      setIsVisible(true);
    }
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    // Store dismissal state in session storage
    sessionStorage.setItem("topBannerDismissed", "true");
  };

  if (!isVisible) {
    return null;
  }

  return (
    <div className="w-full h-10 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
      <div className="flex items-center justify-between px-4 py-2 h-full w-[95%] mx-auto ">
        <div className="flex-1 text-center">
          <span className="text-sm font-medium">
            🎉 $10,000 Creator Contest - Submit your best thumbnails and win
            big!
            <span className="ml-2 text-xs opacity-90">Limited time offer</span>
          </span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="ml-4 h-6 w-6 p-0 hover:bg-primary-foreground/20 text-primary-foreground"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close banner</span>
        </Button>
      </div>
    </div>
  );
}
