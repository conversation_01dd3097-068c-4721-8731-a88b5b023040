import React from "react";
import { Instagram, Twitter, Youtube, Send, Linkedin } from "lucide-react";
import { RiDiscordLine } from "react-icons/ri";
interface FooterLink {
  label: string;
  href: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

interface SocialLink {
  icon: React.ReactNode;
  href: string;
  label: string;
}

const Footer: React.FC = () => {
  const brandName = "Clickworthy.ai";
  const contactEmail = "<EMAIL>";

  const footerSections: FooterSection[] = [
    {
      title: "Product",
      links: [
        { label: "Features", href: "/features" },
        { label: "Reviews", href: "/reviews" },
        { label: "Pricing", href: "/pricing" },
        { label: "FAQ", href: "/#faq" },
      ],
    },
    {
      title: "Company",
      links: [
        { label: "About", href: "/about" },
        { label: "Contact", href: "/contact" },
        { label: "Login", href: "/auth" },
        { label: "Affiliate", href: "/affiliate" },
      ],
    },
    {
      title: "Legal",
      links: [
        { label: "Terms", href: "/terms" },
        { label: "Privacy", href: "/privacy" },
        { label: "Refund Policy", href: "/refund" },
      ],
    },
  ];

  const socialLinks: SocialLink[] = [
    {
      icon: <Instagram className="h-5 w-5" />,
      href: "https://instagram.com",
      label: "Instagram",
    },
    {
      icon: <RiDiscordLine className="h-5 w-5" />,
      href: "https://discord.com",
      label: "Discord",
    },
    {
      icon: <Twitter className="h-5 w-5" />,
      href: "https://twitter.com",
      label: "Twitter",
    },
    {
      icon: <Youtube className="h-5 w-5" />,
      href: "https://youtube.com",
      label: "YouTube",
    },
    {
      icon: <Send className="h-5 w-5" />,
      href: "https://telegram.org",
      label: "Telegram",
    },
    {
      icon: <Linkedin className="h-5 w-5" />,
      href: "https://linkedin.com",
      label: "LinkedIn",
    },
  ];

  return (
    <footer className="bg-background border-t border-border">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                <span className="text-primary-foreground font-bold text-sm">
                  C
                </span>
              </div>
              <span className="text-foreground font-semibold text-lg">
                {brandName}
              </span>
            </div>

            <p className="text-muted-foreground text-sm mb-6">
              Create HIGH CTR Viral Thumbnails in Seconds. ✨ Join 10,000+
              creators using Clickworthy to grow on YouTube
            </p>

            <div className="flex space-x-4 mb-6">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  aria-label={social.label}
                  className="w-10 h-10 bg-muted rounded-full flex items-center justify-center text-muted-foreground hover:bg-muted/80 hover:text-foreground transition-colors"
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <div key={index}>
              <h3 className="text-foreground font-medium mb-4">
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground transition-colors text-sm"
                    >
                      {link.label}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Footer Bottom */}
        <div className="border-t border-border mt-12 pt-8 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="text-muted-foreground text-sm">
            © 2025 {brandName}
            <br className="md:hidden" />
            <span className="hidden md:inline ml-2">All rights reserved</span>
            <span className="md:hidden text-xs mt-1 block">
              All rights reserved
            </span>
          </div>
          <div className="text-muted-foreground text-sm">{contactEmail}</div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
