"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { Coins, Plus, Bell, Check } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/src/components/ui/dropdown-menu";
import { Badge } from "@/src/components/ui/badge";

import { cn } from "@/src/lib/utils";
import Link from "next/link";
import { useSidebarStore } from "@/src/store/sidebarStore";

const Header: React.FC = () => {
  const { isSidebarOpen } = useSidebarStore();
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      title: "Welcome to Clickworthy! 🎉",
      message:
        "Start creating amazing thumbnails with our AI-powered tools. Your journey to viral content begins here!",
      time: "Just now",
      read: false,
    },
  ]);

  const markNotificationAsRead = (notificationId: number) => {
    setNotifications((prev) =>
      prev.map((notif) =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  return (
    <header>
      <nav className="w-full bg-background border-b border-border shadow-md fixed top-0 left-0 z-40 ">
        <div
          className={
            isSidebarOpen
              ? "w-[calc(100%-250px)] ml-[250px] px-4 sm:px-6 lg:px-8"
              : "w-[90%] mx-auto px-4 sm:px-6 lg:px-8"
          }
        >
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link href="/dashboard" className="flex items-center gap-2">
                <span className="font-semibold text-xl text-foreground">
                  Dashboard
                </span>
              </Link>
            </div>

            <div className="flex h-16 items-center  px-6 flex-wrap">
              {/* Right Section - Add Credits, Credits Display, Notifications */}
              <div className="flex items-center gap-3">
                {/* Add Credit Button */}
                <Button size="sm" variant="outline" className="h-7 w-7 p-0">
                  <Plus className="h-3 w-3" />
                </Button>

                {/* User Credits */}
                <div className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-accent/20 border">
                  <Coins className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium">379 Credits</span>
                </div>

                {/* Notifications */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="relative h-9 w-9 p-0"
                    >
                      <Bell className="h-4 w-4" />
                      {unreadCount > 0 && (
                        <Badge
                          variant="destructive"
                          className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
                        >
                          {unreadCount}
                        </Badge>
                      )}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-80 p-0">
                    <div className="p-4 border-b">
                      <h3 className="font-semibold text-sm">Notifications</h3>
                    </div>
                    <div className="max-h-96 overflow-y-auto">
                      {notifications.length === 0 ? (
                        <div className="p-4 text-center text-muted-foreground text-sm">
                          No notifications yet
                        </div>
                      ) : (
                        notifications.map((notification) => (
                          <div
                            key={notification.id}
                            className={cn(
                              "p-4 border-b last:border-b-0 cursor-pointer hover:bg-accent/50 transition-colors",
                              !notification.read && "bg-accent/20"
                            )}
                            onClick={() =>
                              markNotificationAsRead(notification.id)
                            }
                          >
                            <div className="flex items-start gap-3">
                              <div className="flex-1 space-y-1">
                                <div className="flex items-center gap-2">
                                  <p className="text-sm font-medium">
                                    {notification.title}
                                  </p>
                                  {!notification.read && (
                                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                                  )}
                                </div>
                                <p className="text-xs text-muted-foreground leading-relaxed">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {notification.time}
                                </p>
                              </div>
                              {notification.read && (
                                <Check className="w-4 h-4 text-muted-foreground mt-1" />
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </div>
      </nav>
    </header>
  );
};

export default Header;
