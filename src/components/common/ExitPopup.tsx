"use client";

import { useState } from "react";
import { <PERSON>, <PERSON>, CheckCircle, Clock } from "lucide-react";
import { But<PERSON> } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Card } from "@/src/components/ui/card";

interface ExitPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ExitPopup({ isOpen, onClose }: ExitPopupProps) {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsSubmitting(true);
    // Add your email submission logic here
    setTimeout(() => {
      setIsSubmitting(false);
      // Redirect to auth page or handle signup
      window.location.href = "/auth";
    }, 1000);
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          console.log("Backdrop clicked");
          onClose();
        }
      }}
    >
      <Card className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-2xl">
        {/* Close Button */}
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log("Close button clicked");
            onClose();
          }}
          className="absolute top-4 right-4 z-20 p-2 rounded-full bg-black/20 dark:bg-white/10 hover:bg-black/30 dark:hover:bg-white/20 transition-colors backdrop-blur-sm cursor-pointer"
        >
          <X className="w-5 h-5 text-black dark:text-white" />
        </button>

        <div className="grid md:grid-cols-2 min-h-[480px]">
          {/* Left Side - Black Background */}
          <div className="bg-gradient-to-br from-gray-900 to-black p-6 text-white relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-10 left-10 w-32 h-32 bg-primary/20 rounded-full blur-xl"></div>
              <div className="absolute bottom-10 right-10 w-24 h-24 bg-blue-500/20 rounded-full blur-xl"></div>
            </div>

            <div className="relative z-10 space-y-4">
              {/* Header */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-primary">
                  <span className="text-xl">🚀</span>
                  <span className="text-lg font-bold">
                    12M+ AI Thumbnails Generated
                  </span>
                </div>
                <p className="text-gray-300 text-sm">
                  Helping creators 2x their YouTube CTR in 24 hours.
                </p>
              </div>

              {/* Stats */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm">🎯</span>
                  <span className="text-gray-300 text-sm">
                    Used by YouTubers, Agencies & Brands in 30+ countries
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">🖼️</span>
                  <span className="text-gray-300 text-sm">
                    Viral thumbnail samples + "Before vs After" results
                  </span>
                </div>
              </div>

              {/* Recent Wins */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-primary">
                  <span className="text-lg">📈</span>
                  <span className="font-semibold">Recent Wins:</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                    <p className="text-green-400 font-medium">
                      "CTR jumped from 2.4% ➝ 6.7% in 1 week"
                    </p>
                    <p className="text-gray-400">— Abhay (320k Subscribers)</p>
                  </div>
                  <div className="bg-white/5 rounded-lg p-3 border border-white/10">
                    <p className="text-green-400 font-medium">
                      "We got 340K extra views using Clickworthy's thumbnails!"
                    </p>
                    <p className="text-gray-400">— Nitin (723k Subscribers)</p>
                  </div>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="text-lg">⭐️</span>
                  <span className="text-gray-300 font-medium">
                    Trusted by thousands
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    <span className="text-yellow-400">Google</span>
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-white font-medium">4.9</span>
                  </div>
                  <div className="text-gray-400">|</div>
                  <div className="flex items-center gap-1">
                    <span className="text-yellow-400">Trustpilot</span>
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-white font-medium">4.8</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Side - Light Background */}
          <div className="bg-gradient-to-br from-white to-orange-50 dark:from-gray-800 dark:to-gray-900 p-6 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5 dark:opacity-10">
              <div className="absolute top-10 right-10 w-32 h-32 bg-orange-500/20 rounded-full blur-xl"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 bg-primary/20 rounded-full blur-xl"></div>
            </div>

            <div className="relative z-10 space-y-4">
              {/* Header */}
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-orange-600 dark:text-orange-400">
                  <span className="text-xl">🎁</span>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    Double Your CTR.
                  </span>
                </div>
                <p className="text-xl font-bold text-orange-600 dark:text-orange-400">
                  Get 10% OFF Instantly.
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-sm">🎉</span>
                  <p className="text-gray-700 dark:text-gray-300 text-sm">
                    Generate scroll-stopping AI thumbnails that get clicked.
                  </p>
                </div>
                <p className="text-gray-600 dark:text-gray-400 font-medium text-sm">
                  No design skills. No learning curve. Just pure performance.
                </p>
              </div>

              {/* Benefits */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700 dark:text-gray-300">
                    <strong>Bonus:</strong> Your first 10 thumbnails are free.
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="text-gray-700 dark:text-gray-300">
                    Works for faceless, podcast & reaction channels too.
                  </span>
                </div>
              </div>

              {/* Email Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">📬</span>
                    <label className="text-gray-700 dark:text-gray-300 font-medium">
                      Enter your email & claim your exclusive offer:
                    </label>
                  </div>
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full border-2 border-orange-200 dark:border-orange-800 focus:border-orange-500 dark:focus:border-orange-400"
                    required
                  />
                </div>

                <Button
                  type="submit"
                  disabled={isSubmitting || !email}
                  className="w-full bg-orange-600 hover:bg-orange-700 dark:bg-orange-500 dark:hover:bg-orange-600 text-white font-semibold py-3 text-lg"
                >
                  {isSubmitting
                    ? "Processing..."
                    : "🟧 Generate My Thumbnails + Save 10%"}
                </Button>
              </form>

              {/* Limited Offer */}
              <div className="flex items-center gap-2 justify-center bg-orange-100 dark:bg-orange-900/30 rounded-lg p-3">
                <Clock className="w-5 h-5 text-orange-600 dark:text-orange-400" />
                <span className="text-orange-700 dark:text-orange-300 font-medium">
                  Limited offer: Only For Next 50 Signups!
                </span>
              </div>

              {/* Testimonial */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                <p className="text-gray-700 dark:text-gray-300 italic mb-2">
                  "Clickworthy gave my thumbnails a full glow-up. CTR doubled,
                  and it saved me hours every week!"
                </p>
                <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">
                  — Shreya Mehta, 200K Subs, Productivity Niche
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
