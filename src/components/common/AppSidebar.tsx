"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Menu } from "lucide-react";
import { useSidebarStore } from "@/src/store/sidebarStore";
import { useAuthStore } from "@/src/store/authStore";
import { usePathname } from "next/navigation";
import React, { useState, useEffect } from "react";
import { SignOutModal } from "./SignoutModal";
import { cn } from "@/src/lib/utils";
import { SidebarHeader } from "../sidebar/SidebarHeader";
import { NavButton } from "../sidebar/NavButton";
import { ContentSections } from "../sidebar/ContentSections";
import { UserProfileSection } from "../sidebar/UserProfileSection";

import {
  menuItems,
  contentSections,
  mainNavItems,
} from "@/src/constants/sidebar";
const noFocusRing =
  "focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none";

export default function AppSidebar() {
  const { isSidebarOpen, toggleSidebar } = useSidebarStore();
  const { user } = useAuthStore();
  const pathname = usePathname();
  const [showSignOutModal, setShowSignOutModal] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobileView(window.innerWidth < 768);
    };
    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);

    const setVh = () => {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty("--vh", `${vh}px`);
    };
    setVh();
    window.addEventListener("resize", setVh);

    return () => {
      window.removeEventListener("resize", checkIfMobile);
      window.removeEventListener("resize", setVh);
    };
  }, []);

  const handleMobileItemClick = () => {
    if (isMobileView) {
      toggleSidebar();
    }
  };

  const handleMenuDropdownItemClick = () => {
    setIsDropdownOpen(false);
    if (isMobileView) {
      toggleSidebar();
    }
  };

  const handleSignOutClick = () => {
    setShowSignOutModal(true);
    handleMenuDropdownItemClick();
  };

  return (
    <div>
      <div
        style={{ height: "calc(var(--vh, 1vh) * 100)" }}
        className={cn(
          "fixed left-0 top-0 bottom-0 bg-background border-r z-50",
          "w-[250px] transition-transform duration-300 flex flex-col",
          isSidebarOpen ? "translate-x-0" : "-translate-x-full",
          "shadow-lg dark:shadow-slate-800/50"
        )}
      >
        <div className="flex flex-col h-full">
          <SidebarHeader
            onToggle={toggleSidebar}
            onMobileClick={handleMobileItemClick}
            noFocusRing={noFocusRing}
          />

          <div className="flex flex-col flex-1 min-h-0">
            <div className="p-2">
              <nav className="space-y-1">
                {mainNavItems.map((item) => (
                  <NavButton
                    key={item.href}
                    {...item}
                    currentPath={pathname}
                    onClick={handleMobileItemClick}
                  />
                ))}
              </nav>
            </div>

            <ContentSections
              sections={contentSections}
              onSectionClick={handleMobileItemClick}
              currentPath={pathname}
              noFocusRing={noFocusRing}
            />

            <div className="border-t">
              <UserProfileSection
                user={user}
                menuItems={menuItems}
                isDropdownOpen={isDropdownOpen}
                onDropdownOpenChange={setIsDropdownOpen}
                onMenuItemClick={handleMenuDropdownItemClick}
                onSignOutClick={handleSignOutClick}
                onMobileClick={handleMobileItemClick}
                noFocusRing={noFocusRing}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile overlay backdrop */}
      {isSidebarOpen && isMobileView && (
        <div
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-30 lg:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* Hamburger menu button when sidebar is closed */}
      {!isSidebarOpen && (
        <div className="fixed left-2 top-2 z-50">
          <Button
            variant="outline"
            size="icon"
            onClick={toggleSidebar}
            className={cn(
              "bg-background/80 backdrop-blur-md shadow-lg hover:bg-accent h-10 w-10 rounded-full",
              noFocusRing
            )}
          >
            <Menu className="h-5 w-5" />
          </Button>
        </div>
      )}

      <SignOutModal
        open={showSignOutModal}
        onOpenChange={setShowSignOutModal}
      />
    </div>
  );
}
