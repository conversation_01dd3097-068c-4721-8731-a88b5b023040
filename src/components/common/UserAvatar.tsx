import React from "react";
import { useAuthStore } from "@/src/store/authStore";
import Image from "next/image";

const UserAvatar = () => {
  const { user , displayName} = useAuthStore();

  return (
    <div>
    {user?.user_metadata?.avatar_url ? (
        <Image
          src={user.user_metadata.avatar_url}
          alt="User avatar"
          width={36}
          height={36}
          className="rounded-full"
        />
      ) : (
          <div className="flex flex-col items-center justify-center bg-muted rounded-full h-9 w-9">
            <p className="text-sm font-bold text-muted-foreground">
              {displayName?.[0]?.toUpperCase() || "?"}
            </p>
        </div>
      )}
    </div>
  );
};

export default UserAvatar;
