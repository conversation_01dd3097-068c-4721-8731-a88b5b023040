// components/tools/ToolGrid.tsx
"use client";
import { useState } from "react";
import { Input } from "@/src/components/ui/input";
import { ToolCard } from "./ToolCard";
import { tools } from "@/src/constants/tools";

export function ToolGrid() {
  const [query, setQuery] = useState("");
  const filtered = tools.filter((t) =>
    t.title.toLowerCase().includes(query.toLowerCase())
  );

  return (
    <div className="container mx-auto px-4">
      <div className="flex items-center justify-between my-6 ">
        <h2 className="text-2xl font-semibold  flex-1">All Tools</h2>
        <Input
          placeholder="Search tools…"
          value={query}
          onChange={(e) => setQuery(e.currentTarget.value)}
          className="flex-1"
        />
      </div>
      <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 overflow-y-auto">
        {filtered.map((tool) => (
          <ToolCard key={tool.title} tool={tool} />
        ))}
      </div>
    </div>
  );
}
