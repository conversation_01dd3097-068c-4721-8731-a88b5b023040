"use client";

import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { 
  <PERSON><PERSON><PERSON>, 
  RefreshCw, 
  UserCheck, 
  Zap, 
  TrendingUp, 
  Eye,
  Clock,
  Target,
  BarChart3
} from "lucide-react";

const features = [
  {
    icon: Sparkles,
    title: "AI-Powered Generation",
    description: "Create stunning thumbnails from simple text prompts using advanced AI technology.",
    badge: "Most Popular"
  },
  {
    icon: RefreshCw,
    title: "Smart Recreate",
    description: "Recreate and improve existing thumbnails with AI-enhanced variations.",
    badge: null
  },
  {
    icon: UserCheck,
    title: "Face Swap Technology",
    description: "Seamlessly swap faces in thumbnails for personalized content creation.",
    badge: "New"
  },
  {
    icon: Zap,
    title: "Custom AI Training",
    description: "Train your own AI model on your images for consistent brand styling.",
    badge: "Pro"
  },
  {
    icon: TrendingUp,
    title: "CTR Optimization",
    description: "Generate click-worthy titles that boost your video's click-through rate.",
    badge: null
  },
  {
    icon: Eye,
    title: "Smart Analysis",
    description: "Get AI-powered ratings and improvement suggestions for your thumbnails.",
    badge: null
  }
];

const benefits = [
  {
    icon: Clock,
    title: "Save Hours",
    description: "Generate professional thumbnails in seconds, not hours"
  },
  {
    icon: Target,
    title: "Boost Performance",
    description: "Increase your CTR with data-driven optimization"
  },
  {
    icon: BarChart3,
    title: "Scale Content",
    description: "Create consistent, high-quality content at scale"
  }
];

export function FeaturesSection() {
  return (
    <section className="py-24 bg-muted/30">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-4 py-2">
            Features
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">
            Everything You Need to{" "}
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Dominate YouTube
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Powerful AI tools designed to help creators and marketing teams create 
            high-performing content that stands out and drives results.
          </p>
        </div>

        {/* Main Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-lg transition-all duration-300 border-0 bg-background/50 backdrop-blur-sm">
              <CardContent className="p-8">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                      <feature.icon className="w-6 h-6 text-primary" />
                    </div>
                    {feature.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {feature.badge}
                      </Badge>
                    )}
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold">{feature.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Benefits Section */}
        <div className="text-center space-y-12">
          <div className="space-y-4">
            <h3 className="text-2xl sm:text-3xl font-bold">
              Why Creators Choose Clickworthy.ai
            </h3>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Join thousands of creators who are already boosting their performance
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="text-center space-y-4">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                  <benefit.icon className="w-8 h-8 text-primary" />
                </div>
                <div className="space-y-2">
                  <h4 className="text-lg font-semibold">{benefit.title}</h4>
                  <p className="text-muted-foreground">{benefit.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
