"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { 
  ArrowRight, 
  Sparkles, 
  Users, 
  TrendingUp, 
  Zap,
  CheckCircle,
  Star
} from "lucide-react";
import Link from "next/link";

const stats = [
  {
    icon: Users,
    value: "10K+",
    label: "Active Creators"
  },
  {
    icon: TrendingUp,
    value: "300%",
    label: "Average CTR Boost"
  },
  {
    icon: Zap,
    value: "1M+",
    label: "Thumbnails Generated"
  },
  {
    icon: Star,
    value: "4.9/5",
    label: "User Rating"
  }
];

const benefits = [
  "Generate unlimited thumbnails",
  "Access all production-ready tools",
  "4K quality exports",
  "Priority support",
  "Early access to new features",
  "No watermarks"
];

export function CTASection() {
  return (
    <section className="py-24 bg-gradient-to-br from-primary/5 via-background to-secondary/5">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        
        {/* Stats Section */}
        <div className="text-center mb-20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="space-y-2">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                  <stat.icon className="w-6 h-6 text-primary" />
                </div>
                <div className="text-2xl sm:text-3xl font-bold">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>

        {/* Main CTA Card */}
        <Card className="max-w-4xl mx-auto border-0 bg-background/80 backdrop-blur-sm shadow-2xl">
          <CardContent className="p-12">
            <div className="text-center space-y-8">
              
              {/* Header */}
              <div className="space-y-4">
                <Badge variant="secondary" className="px-4 py-2">
                  <Sparkles className="w-4 h-4 mr-2" />
                  Early Access
                </Badge>
                <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">
                  Start Creating{" "}
                  <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    Today
                  </span>
                </h2>
                <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                  Join thousands of creators who are already boosting their YouTube performance 
                  with our AI-powered tools. Get started free and see results immediately.
                </p>
              </div>

              {/* Benefits List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto text-left">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                    <span className="text-muted-foreground">{benefit}</span>
                  </div>
                ))}
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                <Link href="/auth">
                  <Button size="lg" className="text-lg px-8 py-3 h-auto">
                    Get Started Free
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
                <Link href="#tools">
                  <Button variant="outline" size="lg" className="text-lg px-8 py-3 h-auto">
                    View All Tools
                  </Button>
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="">
                <div className="flex flex-col sm:flex-row items-center justify-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-primary" />
                    <span>No credit card required</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-primary" />
                    <span>Free forever plan</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-primary" />
                    <span>Cancel anytime</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

       
      </div>
    </section>
  );
}
