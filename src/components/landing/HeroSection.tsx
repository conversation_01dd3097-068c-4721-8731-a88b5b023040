"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Badge } from "@/src/components/ui/badge";
import { <PERSON>R<PERSON>, Spark<PERSON>, TrendingUp, Zap } from "lucide-react";
import Link from "next/link";

export function HeroSection() {
  return (
    <section className="relative overflow-hidden -mt-24 pt-24">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5" />

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center min-h-[90vh] text-center space-y-8">
          {/* Badge */}
          <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
            <Sparkles className="w-4 h-4 mr-2" />
            AI-Powered YouTube Optimization
          </Badge>

          {/* Main Headline */}
          <div className="space-y-4 max-w-4xl">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
              Create{" "}
              <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                Click-Worthy
              </span>{" "}
              Thumbnails & Titles
            </h1>
            <p className="text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Your all-in-one AI toolkit for creating, optimizing, and analyzing
              high-performing YouTube content. Boost your CTR and grow your
              audience faster.
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 pt-4">
            <Link href="/auth">
              <Button size="lg" className="text-lg px-8 py-3 h-auto">
                Start Creating Free
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
            <Link href="#tools">
              <Button
                variant="outline"
                size="lg"
                className="text-lg px-8 py-3 h-auto"
              >
                Explore Tools
              </Button>
            </Link>
          </div>

          {/* Social Proof / Stats */}
          <div className="flex flex-col sm:flex-row items-center gap-8 pt-8 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-primary" />
              <span>Boost CTR by up to 300%</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-primary" />
              <span>Generate in seconds</span>
            </div>
            <div className="flex items-center gap-2">
              <Sparkles className="w-4 h-4 text-primary" />
              <span>AI-powered precision</span>
            </div>
          </div>

          {/* Hero Image/Demo Placeholder */}
          <div className="pt-8 w-full max-w-4xl">
            <div className="relative">
              <div className="aspect-[16/9] sm:aspect-[2/1] bg-gradient-to-br from-muted to-muted/50 rounded-xl border shadow-2xl overflow-hidden">
                <div className="flex items-center justify-center h-full">
                  <div className="text-center space-y-3">
                    <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                      <Sparkles className="w-6 h-6 text-primary" />
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Interactive demo coming soon
                    </p>
                  </div>
                </div>
              </div>

              {/* Floating elements for visual interest */}
              <div className="absolute -top-2 -left-2 w-6 h-6 bg-primary/20 rounded-full blur-sm" />
              <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-secondary/20 rounded-full blur-sm" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
