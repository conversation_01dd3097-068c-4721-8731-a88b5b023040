"use client";

import { Card, CardContent } from "@/src/components/ui/card";
import { Badge } from "@/src/components/ui/badge";
import { But<PERSON> } from "@/src/components/ui/button";
import {
  Sparkles,
  RefreshCw,
  UserCheck,
  Zap,
  Type,
  Eraser,
  TrendingUp,
  Eye,
  Download,
  ArrowRight,
  CheckCircle,
} from "lucide-react";
import Link from "next/link";

const tools = [
  {
    icon: Sparkles,
    title: "Thumbnail Generator",
    description: "Generate eye-catching thumbnails from text prompts",
    status: "ready",
    href: "/tools/thumbnail-generator",
    features: ["Text-to-image AI", "Multiple styles", "High resolution"],
  },
  {
    icon: RefreshCw,
    title: "Thumbnail Recreate",
    description: "Recreate existing thumbnails with AI enhancements",
    status: "ready",
    href: "/tools/recreate",
    features: ["Upload & recreate", "Style variations", "Brand consistency"],
  },
  {
    icon: User<PERSON><PERSON><PERSON>,
    title: "Face Swap",
    description: "Swap faces in your thumbnails easily",
    status: "ready",
    href: "/tools/faceswap",
    features: ["Seamless swapping", "Natural results", "Multiple faces"],
  },
  {
    icon: Type,
    title: "Title Generator",
    description: "Generate click-worthy video titles that boost CTR",
    status: "ready",
    href: "/tools/title-generator",
    features: ["CTR optimization", "Multiple styles", "A/B testing"],
  },
  {
    icon: Eraser,
    title: "Background Remover",
    description: "Remove backgrounds from any image instantly",
    status: "ready",
    href: "/tools/background-remover",
    features: ["4K quality", "Instant processing", "Transparent output"],
  },
  {
    icon: TrendingUp,
    title: "Thumbnail Rating",
    description: "Get AI-powered analysis and ratings for your thumbnails",
    status: "ready",
    href: "/thumbnail-rating",
    features: ["Performance prediction", "Improvement tips", "Competitor analysis"],
  },
  {
    icon: Eye,
    title: "Thumbnail Preview",
    description: "Preview how your thumbnails look across different devices",
    status: "ready",
    href: "/thumbnail-preview",
    features: ["Device previews", "Platform layouts", "Real-time updates"],
  },
  {
    icon: Download,
    title: "YouTube Downloader",
    description: "Extract thumbnails from YouTube videos",
    status: "ready",
    href: "/thumbnail-download",
    features: ["Bulk download", "High quality", "Metadata extraction"],
  },
  {
    icon: Zap,
    title: "Train AI Model",
    description: "Train your own custom AI model on your images",
    status: "coming-soon",
    href: "/dashboard",
    features: ["Custom training", "Brand consistency", "Personal style"],
  },
];

const getStatusBadge = (status: string) => {
  switch (status) {
    case "ready":
      return (
        <Badge className="bg-green-500/10 text-green-600 border-green-500/20">
          Ready
        </Badge>
      );
    case "beta":
      return <Badge variant="secondary">Beta</Badge>;
    case "coming-soon":
      return <Badge variant="outline">Coming Soon</Badge>;
    default:
      return null;
  }
};

export function ToolsShowcase() {
  return (
    <section id="tools" className="py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="px-4 py-2">
            Tools
          </Badge>
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold">
            Complete{" "}
            <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              Creator Toolkit
            </span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            From generation to optimization, we've got every aspect of your
            YouTube content covered. Start with our production-ready tools and
            get early access to upcoming features.
          </p>
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {tools.map((tool, index) => (
            <Card
              key={index}
              className={`group transition-all duration-300 border-0 bg-background/50 backdrop-blur-sm ${
                tool.status === "ready"
                  ? "hover:shadow-lg hover:scale-[1.02] cursor-pointer"
                  : "opacity-75"
              }`}
            >
              <CardContent className="p-8">
                <div className="space-y-6">
                  {/* Header */}
                  <div className="flex items-start justify-between">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                      <tool.icon className="w-6 h-6 text-primary" />
                    </div>
                    {getStatusBadge(tool.status)}
                  </div>

                  {/* Content */}
                  <div className="space-y-3">
                    <h3 className="text-xl font-semibold">{tool.title}</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      {tool.description}
                    </p>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    {tool.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-center gap-2 text-sm text-muted-foreground"
                      >
                        <CheckCircle className="w-4 h-4 text-primary" />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>

                  {/* CTA */}
                  {tool.status === "ready" ? (
                    <Link href={tool.href}>
                      <Button className="w-full group-hover:bg-primary/90 transition-colors">
                        Try Now
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  ) : tool.status === "beta" ? (
                    <Link href={tool.href}>
                      <Button variant="outline" className="w-full">
                        Try Beta
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </Link>
                  ) : (
                    <Button variant="ghost" className="w-full" disabled>
                      Coming Soon
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center pt-16">
          <div className="space-y-4">
            <h3 className="text-2xl font-semibold">
              Ready to boost your YouTube performance?
            </h3>
            <p className="text-muted-foreground">
              Join early-access users and start creating better content today.
            </p>
            <Link href="/auth">
              <Button size="lg" className="text-lg px-8 py-3 h-auto">
                Get Started Free
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
