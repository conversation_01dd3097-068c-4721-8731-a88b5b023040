"use client";

import React from "react";
import { Button } from "@/src/components/ui/button";
import { Edit3 } from "lucide-react";
import { useThumbnailEditorStore } from "@/src/store/imageEditorStore";

interface EditImageButtonProps {
  imageUrl: string;
  onImageEdited?: (editedImageUrl: string) => void;
  variant?: "default" | "outline" | "ghost" | "secondary";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
  disabled?: boolean;
  personaId?: string; // Optional persona for face-consistent editing
  // Metadata for saving edited images
  sourceImageId?: string; // ID of the original image
  sourceImageType?:
    | "generated"
    | "recreated"
    | "faceswap"
    | "background_removal";
}

const EditImageButton: React.FC<EditImageButtonProps> = ({
  imageUrl,
  onImageEdited,
  variant = "outline",
  size = "sm",
  className = "",
  children,
  disabled = false,
  personaId,
  sourceImageId,
  sourceImageType,
}) => {
  const { openEditor } = useThumbnailEditorStore();

  const handleClick = () => {
    if (!imageUrl || disabled) return;

    openEditor(imageUrl, personaId, sourceImageId, sourceImageType);

    // Note: onImageEdited callback is handled by the ImageEditorModal
    // through the store's generateEditedImage method
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={disabled}
      className={className}
    >
      <Edit3 className="h-4 w-4 mr-2" />
      {children || "Edit"}
    </Button>
  );
};

export default EditImageButton;
