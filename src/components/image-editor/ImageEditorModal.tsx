"use client";

import React, { useState } from "react";
import { useThumbnailEditorStore } from "@/src/store/imageEditorStore";
import { usePersonaStore } from "@/src/store/personaStore";
import { Button } from "@/src/components/ui/button";
import { Textarea } from "@/src/components/ui/textarea";
import { Badge } from "@/src/components/ui/badge";
import { X, User, Download, Wand2, Loader2 } from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";
import { PersonaSelectionModal } from "@/src/components/personas/PersonaSelectionModal";
import { ImageGenerationLoader } from "@/src/components/ui/ImageGenerationLoader";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/src/components/ui/dialog";

interface ImageEditorModalProps {
  onImageEdited?: (editedImageUrl: string) => void;
}

const ImageEditorModal: React.FC<ImageEditorModalProps> = ({
  onImageEdited,
}) => {
  const {
    isOpen,
    originalImageUrl,
    editedImageUrl,
    prompt,
    personaId,
    isProcessing,
    isDownloading,
    error,
    closeEditor,
    setPrompt,
    setPersonaId,
    generateEditedImage,
    downloadEditedImage,
    startReEdit,
  } = useThumbnailEditorStore();

  const { personas } = usePersonaStore();
  const [showPersonaModal, setShowPersonaModal] = useState(false);

  // Get selected persona details
  const selectedPersona = personas.find((p) => p.id === personaId);

  const handlePersonaSelect = (persona: any) => {
    setPersonaId(persona.id);
    setShowPersonaModal(false);
    toast.success(`Selected persona: ${persona.name}`);
  };

  const handlePersonaRemove = () => {
    setPersonaId(null);
    toast.success("Persona removed");
  };

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast.error(
        "Please enter a description of how you want to edit the thumbnail"
      );
      return;
    }

    await generateEditedImage((editedUrl) => {
      onImageEdited?.(editedUrl);
    });
  };

  const handleStartOver = () => {
    startReEdit();
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full Screen Modal */}
      <div className={`fixed inset-0 z-50 ${isOpen ? "block" : "hidden"}`}>
        {/* Backdrop */}
        <div className="fixed inset-0 bg-black/80" onClick={closeEditor} />

        {/* Modal Content */}
        <div className="fixed inset-0 flex items-center justify-center p-2 md:p-4">
          <div className="relative bg-background rounded-lg w-full max-w-6xl h-full max-h-[95vh] md:max-h-[90vh] flex flex-col overflow-hidden border shadow-2xl">
            {/* Header */}
            <div className="flex items-center justify-between p-4 md:p-6 border-b">
              <h2 className="text-lg md:text-xl font-semibold">
                Edit Thumbnail
              </h2>
              <Button
                variant="ghost"
                size="icon"
                onClick={closeEditor}
                disabled={isProcessing || isDownloading}
                className="h-8 w-8 md:h-10 md:w-10"
              >
                <X className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </div>

            {/* Main Content - Responsive Layout */}
            <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
              {/* Image Section - Mobile: Top, Desktop: Left */}
              <div className="flex-1 flex items-center justify-center p-4 md:p-8 bg-muted/20 min-h-[300px] md:min-h-0">
                <div className="relative w-full max-w-2xl">
                  {isProcessing ? (
                    // Show loading state during processing
                    <div className="aspect-video w-full rounded-lg overflow-hidden">
                      <ImageGenerationLoader
                        isLoading={true}
                        toolType="thumbnail"
                        usePersona={!!personaId}
                        message="Editing your thumbnail..."
                      />
                    </div>
                  ) : editedImageUrl ? (
                    // Show edited result
                    <div className="relative">
                      <Image
                        src={editedImageUrl}
                        alt="Edited thumbnail"
                        width={800}
                        height={450}
                        className="w-full h-auto rounded-lg shadow-lg"
                        priority
                      />
                      <div className="absolute top-2 left-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                        Edited
                      </div>
                    </div>
                  ) : originalImageUrl ? (
                    // Show original image
                    <div className="relative">
                      <Image
                        src={originalImageUrl}
                        alt="Original thumbnail"
                        width={800}
                        height={450}
                        className="w-full h-auto rounded-lg shadow-lg"
                        priority
                      />
                      <div className="absolute top-2 left-2 bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">
                        Original
                      </div>
                    </div>
                  ) : (
                    // Fallback skeleton
                    <div className="aspect-video w-full rounded-lg bg-muted animate-pulse flex items-center justify-center">
                      <div className="text-muted-foreground">
                        Loading image...
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Controls Section - Mobile: Bottom, Desktop: Right */}
              <div className="w-full md:w-96 border-t md:border-t-0 md:border-l bg-background flex flex-col">
                {/* Prompt Section */}
                <div className="flex-1 p-4 md:p-6 space-y-4 md:space-y-6">
                  {/* Persona Selection */}
                  {selectedPersona ? (
                    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          {selectedPersona.name}
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePersonaRemove}
                        disabled={isProcessing || isDownloading}
                        className="h-8 px-3 text-xs"
                      >
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <Button
                      variant="outline"
                      onClick={() => setShowPersonaModal(true)}
                      disabled={isProcessing || isDownloading}
                      className="w-full justify-start gap-2 h-10 md:h-12"
                    >
                      <User className="h-4 w-4" />
                      Select Persona (Optional)
                    </Button>
                  )}

                  {/* Prompt Input */}
                  <div className="space-y-3">
                    <Textarea
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      placeholder="Describe how you want to edit the thumbnail..."
                      className="min-h-[100px] md:min-h-[120px] resize-none text-base"
                      disabled={isProcessing || isDownloading}
                    />
                  </div>

                  {/* Error Display */}
                  {error && (
                    <div className="p-3 rounded-lg bg-destructive/10 border border-destructive/20">
                      <p className="text-sm text-destructive">{error}</p>
                    </div>
                  )}
                </div>

                {/* Bottom Actions */}
                <div className="p-4 md:p-6 border-t space-y-3">
                  {!editedImageUrl ? (
                    <Button
                      onClick={handleGenerate}
                      disabled={isProcessing || isDownloading || !prompt.trim()}
                      className="w-full h-12 md:h-14 text-base md:text-lg"
                      size="lg"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Editing...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-5 w-5 mr-2" />
                          Edit Thumbnail
                        </>
                      )}
                    </Button>
                  ) : (
                    <div className="space-y-2 md:space-y-3">
                      <Button
                        onClick={downloadEditedImage}
                        disabled={isProcessing || isDownloading}
                        className="w-full h-12 md:h-14 text-base md:text-lg"
                        size="lg"
                      >
                        {isDownloading ? (
                          <>
                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                            Downloading...
                          </>
                        ) : (
                          <>
                            <Download className="h-5 w-5 mr-2" />
                            Download
                          </>
                        )}
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleStartOver}
                        disabled={isProcessing || isDownloading}
                        className="w-full h-10 md:h-12"
                      >
                        Edit Again
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Persona Selection Modal */}
      <PersonaSelectionModal
        isOpen={showPersonaModal}
        onClose={() => setShowPersonaModal(false)}
        onPersonaSelect={handlePersonaSelect}
      />
    </>
  );
};

export default ImageEditorModal;
