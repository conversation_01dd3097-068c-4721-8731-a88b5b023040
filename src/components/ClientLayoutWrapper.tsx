"use client";

import { useEffect, useState, memo } from "react";
import { useAuthStore } from "@/src/store/authStore";
import { useTitleGeneratorStore } from "@/src/store/titleGeneratorStore";
import { Toaster } from "sonner";
import { usePathname } from "next/navigation";
import { PUBLIC_ROUTES_SET, ROUTES_WITHOUT_FOOTER_SET } from "@/src/lib/routes";
import { useSidebarStore } from "@/src/store/sidebarStore";
import AppSidebar from "./common/AppSidebar";
import { ImageEditorModal } from "@/src/components/image-editor";
import Footer from "./common/Footer";
import Header from "./common/Header";
import Navbar from "./Navbar";
import { ThemeProvider } from "next-themes";
import { PersonaProvider } from "./providers/PersonaProvider";
import TopBanner from "./ui/TopBanner";

// Memoize layouts to prevent unnecessary re-renders
const PublicLayout = memo(({ children }: { children: React.ReactNode }) => (
  <div className="min-h-screen flex flex-col justify-between">
    <TopBanner />
    {children}
  </div>
));
PublicLayout.displayName = "PublicLayout";

const ProtectedLayout = memo(
  ({
    children,
    isSidebarOpen,
    showSidebar = true,
  }: {
    children: React.ReactNode;
    isSidebarOpen: boolean;
    showSidebar?: boolean;
  }) => {
    const pathname = usePathname();
    const isChat =
      pathname.startsWith("/tools/title-generator") ||
      pathname === "/tools/thumbnail-generator";

    return (
      <div className="flex h-[100dvh] w-full">
        {showSidebar && <AppSidebar />}
        <main
          className={`flex-1 transition-all duration-300 h-full relative ${
            showSidebar && isSidebarOpen ? "md:ml-[250px]" : "ml-0"
          } ${isChat ? "overflow-hidden" : "overflow-y-auto"}`}
        >
          {!isChat && <TopBanner />}
          <div className="w-[95%] mx-auto pt-10">{children}</div>
        </main>
        <ImageEditorModal />
      </div>
    );
  }
);
ProtectedLayout.displayName = "ProtectedLayout";

export default function ClientLayoutWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const initializeAuth = useAuthStore((state) => state.initializeAuthListener);
  const {
    clearAllState: clearTitleGeneratorState,
    currentSessionId,
    messages,
  } = useTitleGeneratorStore();
  const pathname = usePathname();
  const { isSidebarOpen } = useSidebarStore();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    const unsubscribe = initializeAuth();
    setMounted(true);
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [initializeAuth]);

  // Clear title generator state when navigating away from title generator section
  useEffect(() => {
    const isOnTitleGenerator = pathname?.startsWith("/tools/title-generator");

    // If we're not on a title generator page and have state to clear, clear it
    if (
      mounted &&
      pathname &&
      !isOnTitleGenerator &&
      (currentSessionId || messages.length > 0)
    ) {
      clearTitleGeneratorState();
    }
  }, [
    pathname,
    mounted,
    clearTitleGeneratorState,
    currentSessionId,
    messages.length,
  ]);

  // Handle initial mounting
  if (!mounted) {
    return null;
  }

  // Check if current route is public
  const isPublicRoute = PUBLIC_ROUTES_SET.has(pathname || "");
  // Special case for thumbnail-preview: do NOT show sidebar in protected layout
  const isThumbnailPreview =
    pathname === "/thumbnail-preview" ||
    pathname === "/tools/thumbnail-preview";

  return (
    <ThemeProvider attribute="class" defaultTheme="dark">
      <PersonaProvider>
        {isPublicRoute ? (
          <PublicLayout>
            <Navbar />
            {children}
            {!ROUTES_WITHOUT_FOOTER_SET.has(pathname || "") && <Footer />}
          </PublicLayout>
        ) : (
          <>
            {/* For /thumbnail-preview and /tools/thumbnail-preview, do NOT show sidebar, but show header */}
            {isThumbnailPreview ? (
              <ProtectedLayout isSidebarOpen={false} showSidebar={false}>
                <Header />
                {children}
              </ProtectedLayout>
            ) : (
              <ProtectedLayout isSidebarOpen={isSidebarOpen} showSidebar={true}>
                <Header />
                {children}
              </ProtectedLayout>
            )}
          </>
        )}
        <Toaster richColors position="top-center" />
      </PersonaProvider>
    </ThemeProvider>
  );
}
