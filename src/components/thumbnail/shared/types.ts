export interface ThumbnailGenerateOptions {
  prompt?: string;
  persona?: string;
  style?: string;
}

export interface RecreateGenerateOptions extends ThumbnailGenerateOptions {
  sourceUrl?: string;
  edits?: string;
}

export interface FaceSwapGenerateOptions {
  thumbnailFile?: File;
  persona?: string;
}

export interface TitleGenerateOptions {
  prompt?: string;
  enhancePrompt?: boolean;
}

export type TabType = "thumbnail" | "recreate" | "faceswap" | "title";

export interface TabState {
  isLoading: boolean;
  error?: string | null;
}
