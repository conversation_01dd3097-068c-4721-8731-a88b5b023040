"use client";

import React, { useState, useEffect } from "react";

import { YouTubeHeader } from "./YouTubeHeader";
import { Sidebar } from "./Sidebar";
import { CategoryTabs } from "./CategoryTabs";
import { ThumbnailGrid } from "./ThumbnailGrid";
import type {
  DeviceSize,
  CustomThumbnail,
  VideoThumbnail,
} from "@/src/types/preview";

interface ThumbnailPreviewInterfaceProps {
  isAuthenticated?: boolean;
}

// Main Component
export default function ThumbnailPreviewInterface({
  isAuthenticated = false,
}: ThumbnailPreviewInterfaceProps) {
  const [deviceSize, setDeviceSize] = useState<DeviceSize>("desktop");
  const [customThumbnail, setCustomThumbnail] =
    useState<CustomThumbnail | null>(null);

  const [searchQuery, setSearchQuery] = useState("");
  const [showSidebar, setShowSidebar] = useState(true);
  const [shuffledThumbnails, setShuffledThumbnails] = useState<
    VideoThumbnail[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [originalThumbnails, setOriginalThumbnails] = useState<
    VideoThumbnail[]
  >([]);

  // Load trending videos on component mount
  useEffect(() => {
    const loadTrendingVideos = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/trending-videos");
        const data = await response.json();

        if (data.success && data.videos.length > 0) {
          setOriginalThumbnails(data.videos);
          setShuffledThumbnails(data.videos);
        } else {
          console.log("No trending videos from API");
          setOriginalThumbnails([]);
          setShuffledThumbnails([]);
        }
      } catch (error) {
        console.error("Error loading trending videos:", error);
        setOriginalThumbnails([]);
        setShuffledThumbnails([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadTrendingVideos();
  }, []);

  // Shuffle function
  const shuffleThumbnails = () => {
    const shuffled = [...originalThumbnails].sort(() => Math.random() - 0.5);
    setShuffledThumbnails(shuffled);
  };

  // Reset function
  const resetThumbnails = () => {
    setShuffledThumbnails(originalThumbnails);
    setCustomThumbnail(null);
  };

  // Auto-close sidebar on mobile
  useEffect(() => {
    const checkMobile = () => {
      const isMobile = window.innerWidth < 1024;
      setShowSidebar(!isMobile);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <div className="min-h-screen transition-colors duration-300 bg-background text-foreground overflow-y-auto">
      <div className="flex">
        <Sidebar
          showSidebar={showSidebar}
          deviceSize={deviceSize}
          setDeviceSize={setDeviceSize}
          customThumbnail={customThumbnail}
          setCustomThumbnail={setCustomThumbnail}
          shuffleThumbnails={shuffleThumbnails}
          resetThumbnails={resetThumbnails}
        />

        {/* Main Content */}
        <div
          className={`flex-1 transition-all duration-300 overflow-x-hidden w-full ${
            showSidebar ? "ml-80" : ""
          }`}
        >
          <YouTubeHeader
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            showSidebar={showSidebar}
            setShowSidebar={setShowSidebar}
          />
          <CategoryTabs />

          <ThumbnailGrid
            deviceSize={deviceSize}
            customThumbnail={customThumbnail}
            shuffledThumbnails={shuffledThumbnails}
            isLoading={isLoading}
          />
        </div>
      </div>
    </div>
  );
}
