"use client";

import React from "react";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Label } from "@/src/components/ui/label";
import { Monitor, Tablet, Smartphone, Shuffle, RotateCcw, Sun, Moon } from "lucide-react";
import { useTheme } from "next-themes";
import { CustomThumbnailUpload } from "./CustomThumbnailUpload";
import { ThumbnailMakerAd } from "./ThumbnailMakerAd";
import type { DeviceSize, CustomThumbnail } from "@/src/types/preview";

export function Sidebar({
  showSidebar,
  deviceSize,
  setDeviceSize,
  customThumbnail,
  setCustomThumbnail,
  shuffleThumbnails,
  resetThumbnails,
}: {
  showSidebar: boolean;
  deviceSize: DeviceSize;
  setDeviceSize: (size: DeviceSize) => void;
  customThumbnail: CustomThumbnail | null;
  setCustomThumbnail: (thumbnail: CustomThumbnail | null) => void;
  shuffleThumbnails: () => void;
  resetThumbnails: () => void;
}) {
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  if (!showSidebar) return null;

  return (
    <div className="w-80 flex-shrink-0 h-[calc(100vh-73px)] fixed left-0 top-[73px] z-50 overflow-y-auto transition-colors bg-background border-r border-border">
      <div className="p-6 space-y-8">
        {/* Device Preview Section */}
        <div className="space-y-4">
          <Label className="text-base font-semibold text-foreground">
            Device Preview
          </Label>
          <div className="flex items-center justify-between rounded-2xl p-2 bg-muted">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDeviceSize("desktop")}
              className={`p-3 rounded-xl transition-all ${
                deviceSize === "desktop"
                  ? "bg-primary text-primary-foreground shadow-lg"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted-foreground/10"
              }`}
            >
              <Monitor className="w-5 h-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDeviceSize("tablet")}
              className={`p-3 rounded-xl transition-all ${
                deviceSize === "tablet"
                  ? "bg-primary text-primary-foreground shadow-lg"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted-foreground/10"
              }`}
            >
              <Tablet className="w-5 h-5" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setDeviceSize("mobile")}
              className={`p-3 rounded-xl transition-all ${
                deviceSize === "mobile"
                  ? "bg-primary text-primary-foreground shadow-lg"
                  : "text-muted-foreground hover:text-foreground hover:bg-muted-foreground/10"
              }`}
            >
              <Smartphone className="w-5 h-5" />
            </Button>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <div className="flex gap-3">
            <Button
              onClick={shuffleThumbnails}
              className="flex-1 flex items-center gap-2 py-3 rounded-xl font-medium transition-all bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl"
            >
              <Shuffle className="w-4 h-4" />
              Shuffle
            </Button>
            <Button
              onClick={resetThumbnails}
              variant="outline"
              className="flex-1 flex items-center gap-2 py-3 rounded-xl font-medium transition-all border-border text-foreground hover:bg-muted hover:text-foreground"
            >
              <RotateCcw className="w-4 h-4" />
              Reset
            </Button>
          </div>
          <div className="flex gap-3">
            <Button
              onClick={toggleTheme}
              variant="outline"
              className="flex-1 flex items-center gap-2 py-3 rounded-xl font-medium transition-all border-border text-foreground hover:bg-muted hover:text-foreground"
            >
              {theme === "dark" ? (
                <Sun className="w-4 h-4" />
              ) : (
                <Moon className="w-4 h-4" />
              )}
              {theme === "dark" ? "Light" : "Dark"}
            </Button>
          </div>
        </div>

        {/* Video Details */}
        <div className="space-y-4">
          <Label className="text-base font-semibold text-foreground">
            Video Details
          </Label>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label className="text-sm text-muted-foreground">
                Video Title
              </Label>
              <Input
                value={customThumbnail?.title || "Your Amazing Video Title"}
                onChange={(e) =>
                  setCustomThumbnail(
                    customThumbnail
                      ? { ...customThumbnail, title: e.target.value }
                      : {
                          url: "",
                          title: e.target.value,
                          channelName: "Your Channel",
                        }
                  )
                }
                placeholder="Enter video title"
                className="rounded-xl border border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-sm text-muted-foreground">
                Channel Name
              </Label>
              <Input
                value={customThumbnail?.channelName || "Your Channel"}
                onChange={(e) =>
                  setCustomThumbnail(
                    customThumbnail
                      ? { ...customThumbnail, channelName: e.target.value }
                      : {
                          url: "",
                          title: "Your Amazing Video Title",
                          channelName: e.target.value,
                        }
                  )
                }
                placeholder="Enter channel name"
                className="rounded-xl border border-border bg-background text-foreground placeholder-muted-foreground focus:border-primary"
              />
            </div>
          </div>
        </div>

        {/* Custom Thumbnail Upload */}
        <div className="space-y-4">
          <Label className="text-base font-semibold text-foreground">
            Custom Thumbnail
          </Label>
          <CustomThumbnailUpload
            onThumbnailUpload={setCustomThumbnail}
            customThumbnail={customThumbnail}
          />
        </div>

        {/* Thumbnail Maker Ad */}
        <ThumbnailMakerAd />
      </div>
    </div>
  );
}
