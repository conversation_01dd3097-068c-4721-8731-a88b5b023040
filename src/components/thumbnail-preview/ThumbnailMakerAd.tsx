import React from "react";
import { But<PERSON> } from "@/src/components/ui/button";
import { useRouter } from "next/navigation";

export function ThumbnailMakerAd() {
  const router = useRouter();
  const handleCreateThumbnail = () => {
    router.push("/dashboard");
  };

  return (
    <div className="p-4 rounded-3xl bg-gradient-to-br from-purple-500 via-purple-600 to-blue-500 text-white shadow-lg">
      <div className="space-y-4">
        <h3 className="text-xl font-bold leading-tight">
          🎯 FREE YouTube AI Thumbnail Maker
        </h3>

        <div className="text-sm leading-relaxed opacity-90 space-y-2">
          <p>
            <strong>Clickworthy.ai</strong> uses advanced AI to create{" "}
            <strong>viral, high-converting thumbnails</strong> in seconds.
          </p>
          <p>✅ Boost CTR </p>
          <p>✅ Save hours</p>
          <p>✅ Look like a pro (even if you&apos;re not)</p>
        </div>

        <Button
          onClick={handleCreateThumbnail}
          className="w-full bg-transparent border-2 border-white text-white hover:bg-white hover:text-purple-600 rounded-full py-3 font-semibold transition-all duration-200"
        >
          👉 Start Free
        </Button>
      </div>
    </div>
  );
}
