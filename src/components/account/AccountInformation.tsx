"use client";

import { Card } from "@/src/components/ui/card";
import { useAuthStore } from "@/src/store/authStore";

export function AccountInformation() {
  const { user, displayName } = useAuthStore();

  return (
    <Card className="p-6 bg-card/50">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Account Information</h2>
      </div>
      <div className="space-y-4">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Name</span>
          <span>{displayName}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-muted-foreground">Email</span>
          <span>{user?.email}</span>
        </div>
      </div>
    </Card>
  );
}
