"use client";

import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card } from "@/src/components/ui/card";

export function CurrentPlan() {
  return (
    <Card className="p-6 bg-card/50">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Current Plan</h2>
        <Button variant="outline" className="bg-muted/50">
          Manage
        </Button>
      </div>
      <div className="flex items-center gap-4">
        <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
          <span className="text-2xl font-bold text-primary">P</span>
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <span className="text-primary font-medium">Premium</span>
            <span>Monthly</span>
          </div>
          <p className="text-sm text-muted-foreground">Tier 0 | Since 2025</p>
        </div>
        <Button variant="outline" size="sm" className="bg-muted/50">
          Upgrade
        </Button>
      </div>
    </Card>
  );
}
