"use client";
import { useEffect, useRef } from "react";
import { useAuthStore } from "@/src/store/authStore";

export function AuthSyncEffect() {
  const { user } = useAuthStore();
  const lastSyncRef = useRef<string | null>(null);

  useEffect(() => {
    if (!user) return;

    // Only sync if user ID has changed (prevents redundant calls)
    if (lastSyncRef.current === user.id) return;

    // Sync user profile in the background with caching
    fetch("/api/user", {
      method: "GET",
      headers: {
        "Cache-Control": "max-age=300", // 5 minutes cache
      },
    });

    lastSyncRef.current = user.id;
  }, [user]);

  return null;
}
