"use client";

import { useEffect } from "react";
import { usePersonaBackgroundSync } from "@/src/hooks/usePersonaBackgroundSync";
import { usePersonaStore } from "@/src/store/personaStore";

interface PersonaProviderProps {
  children: React.ReactNode;
}

/**
 * Provider component that handles background persona sync
 * This ensures personas continue training even when modals are closed
 */
export function PersonaProvider({ children }: PersonaProviderProps) {
  // Initialize background sync
  usePersonaBackgroundSync();

  return <>{children}</>;
}
