import { Button } from "@/src/components/ui/button";
import Link from "next/link";
import React from "react";
import { cn } from "@/src/lib/utils";

interface ContentSection {
  id: number;
  title: string;
  url: string;
  icon: React.ElementType;
}

interface ContentSectionsProps {
  sections: ContentSection[];
  onSectionClick: () => void;
  currentPath: string;
  noFocusRing: string;
}

export function ContentSections({
  sections,
  onSectionClick,
  currentPath,
  noFocusRing,
}: ContentSectionsProps) {
  return (
    <div className="flex-1 overflow-y-auto overflow-x-hidden">
      <div className="px-2 py-1">
        <div className="mb-4">
          <div className="px-3 mb-2">
            <span className="text-xs text-muted-foreground font-medium">
              MY CONTENT
            </span>
          </div>
          <div className="space-y-1">
            {sections.map((section) => {
              const isActive = currentPath === section.url;
              const Icon = section.icon;

              return (
                <Button
                  key={section.id}
                  variant="ghost"
                  className={cn(
                    "w-full justify-start text-sm font-normal h-auto py-2.5 px-3",
                    "hover:bg-muted/60 dark:hover:bg-muted/50",
                    noFocusRing,
                    isActive
                      ? "bg-primary/10 dark:bg-primary/20 text-primary font-medium"
                      : "text-foreground hover:text-foreground"
                  )}
                  onClick={onSectionClick}
                  asChild
                >
                  <Link href={section.url} className="flex items-center gap-3">
                    <Icon className="h-4 w-4 shrink-0" />
                    <span className="truncate">{section.title}</span>
                  </Link>
                </Button>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
