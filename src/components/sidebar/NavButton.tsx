import { But<PERSON> } from "@/src/components/ui/button";
import { cn } from "@/src/lib/utils";
import Link from "next/link";
import React from "react";

const noFocusRing =
  "focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none";

interface NavButtonProps {
  href: string;
  icon: React.ElementType;
  label: string;
  currentPath: string;
  onClick: () => void;
  isActiveCustom?: (path: string) => boolean;
}

export function NavButton({
  href,
  icon: Icon,
  label,
  currentPath,
  onClick,
  isActiveCustom,
}: NavButtonProps) {
  const isActive = isActiveCustom ? isActiveCustom(href) : currentPath === href;

  const activeClasses =
    "bg-primary/10 dark:bg-primary/20 text-primary font-semibold relative before:content-[''] before:absolute before:left-0 before:top-1/2 before:-translate-y-1/2 before:h-5 before:w-1 before:bg-primary before:rounded-r-md";

  const hoverClasses = "hover:bg-muted/60 dark:hover:bg-muted/50";

  return (
    <Button
      variant="ghost"
      className={cn(
        "justify-start gap-3 w-full h-12 text-base",
        noFocusRing,
        isActive ? activeClasses : hoverClasses,
        !isActive && "text-foreground hover:text-foreground"
      )}
      asChild
      onClick={onClick}
    >
      <Link href={href}>
        <Icon className="h-5 w-5" />
        <span>{label}</span>
      </Link>
    </Button>
  );
}
