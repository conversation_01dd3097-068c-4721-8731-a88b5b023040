import { But<PERSON> } from "@/src/components/ui/button";
import { PanelLeftClose } from "lucide-react";
import Link from "next/link";
import { cn } from "@/src/lib/utils";

interface SidebarHeaderProps {
  onToggle: () => void;
  onMobileClick: () => void;
  noFocusRing: string;
}

export function SidebarHeader({
  onToggle,
  onMobileClick,
  noFocusRing,
}: SidebarHeaderProps) {
  return (
    <div className="flex items-center justify-between h-16 px-4 border-b">
      <Link
        href="/dashboard"
        className="flex items-center gap-2"
        onClick={onMobileClick}
      >
        <h1 className="text-xl font-semibold text-foreground">
          <span className="text-rose-500">Click</span>
          <span className="text-cyan-500">worthy</span>
        </h1>
      </Link>
      <Button
        variant="ghost"
        size="icon"
        onClick={onToggle}
        className={cn(noFocusRing)}
      >
        <PanelLeftClose size={24} />
      </Button>
    </div>
  );
}
