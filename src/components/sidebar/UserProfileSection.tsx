import { Button } from "@/src/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/src/components/ui/dropdown-menu";
import { cn } from "@/src/lib/utils";
import Link from "next/link";
import { ChevronDown, LogIn, LogOut, Sun, Moon } from "lucide-react";
import { User } from "@supabase/supabase-js";
import { useAuthStore } from "@/src/store/authStore";
import UserAvatar from "../common/UserAvatar";
import { useTheme } from "next-themes";

interface MenuItem {
  href: string;
  icon: React.ElementType;
  label: string;
  isExternal?: boolean;
}

interface UserProfileSectionProps {
  user: User | null;
  menuItems: MenuItem[];
  isDropdownOpen: boolean;
  onDropdownOpenChange: (open: boolean) => void;
  onMenuItemClick: () => void;
  onSignOutClick: () => void;
  onMobileClick: () => void;
  noFocusRing: string;
}

export function UserProfileSection({
  user,
  menuItems,
  isDropdownOpen,
  onDropdownOpenChange,
  onMenuItemClick,
  onSignOutClick,
  onMobileClick,
  noFocusRing,
}: UserProfileSectionProps) {
  const displayName = useAuthStore((s) => s.displayName);
  const { theme, setTheme } = useTheme();

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };
  if (!user) {
    return (
      <div className="p-2">
        <Button
          variant="outline"
          className={cn(
            "w-full justify-center gap-2 h-11 text-base",
            noFocusRing,
            "hover:bg-primary/5 dark:hover:bg-primary/10 hover:border-primary"
          )}
          asChild
          onClick={onMobileClick}
        >
          <Link href="/auth">
            <LogIn className="h-5 w-5" />
            <span>Sign In</span>
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <DropdownMenu open={isDropdownOpen} onOpenChange={onDropdownOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "flex items-center justify-between w-full gap-2 px-3 py-3 h-auto rounded-md",
            "hover:bg-muted/70 dark:hover:bg-muted/50",
            noFocusRing
          )}
        >
          <div className="flex items-center gap-3 truncate">
            <UserAvatar />
            <div className="flex flex-col items-start truncate">
              <span className="text-sm font-medium text-foreground truncate">
                {displayName}
              </span>
              <span className="text-xs text-muted-foreground truncate">
                {user.email}
              </span>
            </div>
          </div>
          <ChevronDown className="h-4 w-4 text-muted-foreground shrink-0" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        side="top"
        align="start"
        className="w-[250px] bg-popover text-popover-foreground border shadow-xl"
        onCloseAutoFocus={(e) => e.preventDefault()}
      >
        {menuItems.map((item) => (
          <DropdownMenuItem
            key={item.label}
            className="py-2.5 px-3 hover:bg-muted focus:bg-muted rounded-md cursor-pointer text-sm"
            onClick={onMenuItemClick}
            asChild
          >
            <Link
              href={item.href}
              className="flex items-center gap-2.5 w-full"
              target={item.isExternal ? "_blank" : undefined}
              rel={item.isExternal ? "noopener noreferrer" : undefined}
            >
              <item.icon className="h-5 w-5 text-muted-foreground" />
              <span>{item.label}</span>
            </Link>
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator className="my-1" />

        <DropdownMenuItem
          className="py-2.5 px-3 hover:bg-muted focus:bg-muted rounded-md cursor-pointer text-sm"
          onClick={toggleTheme}
        >
          <div className="flex items-center gap-2.5 w-full">
            {theme === "dark" ? (
              <Sun className="h-5 w-5 text-muted-foreground" />
            ) : (
              <Moon className="h-5 w-5 text-muted-foreground" />
            )}
            <span>Light Mode</span>
          </div>
        </DropdownMenuItem>

        <DropdownMenuSeparator className="my-1" />

        <DropdownMenuItem
          className="py-2.5 px-3 hover:bg-destructive/10 focus:bg-destructive/10 rounded-md cursor-pointer text-sm group"
          onClick={onSignOutClick}
        >
          <div className="flex items-center gap-2.5 w-full text-destructive group-hover:text-destructive-foreground">
            <LogOut className="h-5 w-5" />
            <span>Log out</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
