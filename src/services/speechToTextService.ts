/**
 * Speech-to-Text Service
 * Handles audio transcription using OpenAI's gpt-4o-transcribe model
 */

export interface TranscriptionOptions {
  prompt?: string; // Context prompt to improve accuracy
  language?: string; // Language code (optional)
  temperature?: number; // Randomness (0-1, default 0)
}

export interface TranscriptionResult {
  text: string;
  success: boolean;
  error?: string;
}

export interface TranscriptionProgress {
  stage: "uploading" | "transcribing" | "complete" | "error";
  message: string;
}

/**
 * Convert audio blob to text using OpenAI's speech-to-text API
 */
export async function transcribeAudio(
  audioBlob: Blob,
  options: TranscriptionOptions = {},
  onProgress?: (progress: TranscriptionProgress) => void
): Promise<TranscriptionResult> {
  try {
    onProgress?.({ stage: "uploading", message: "Preparing audio..." });

    // Convert blob to File object for FormData
    const audioFile = new File([audioBlob], "recording.webm", {
      type: audioBlob.type,
    });

    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append("audio", audioFile);

    if (options.prompt) {
      formData.append("prompt", options.prompt);
    }

    if (options.language) {
      formData.append("language", options.language);
    }

    if (options.temperature !== undefined) {
      formData.append("temperature", options.temperature.toString());
    }

    onProgress?.({
      stage: "transcribing",
      message: "Converting speech to text...",
    });

    // Call our API endpoint
    const response = await fetch("/api/speech-to-text", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error || `HTTP ${response.status}: ${response.statusText}`
      );
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || "Transcription failed");
    }

    onProgress?.({ stage: "complete", message: "Transcription complete!" });

    return {
      text: result.text,
      success: true,
    };
  } catch (error) {
    console.error("Transcription error:", error);

    const errorMessage =
      error instanceof Error ? error.message : "Failed to transcribe audio";

    onProgress?.({ stage: "error", message: errorMessage });

    return {
      text: "",
      success: false,
      error: errorMessage,
    };
  }
}

/**
 * Get context-aware prompts for different tools to improve transcription accuracy
 */
export function getToolPrompt(
  tool: "thumbnail" | "title" | "recreate" | "faceswap"
): string {
  switch (tool) {
    case "thumbnail":
      return "The user is describing a YouTube thumbnail they want to create. Common terms include: thumbnail, YouTube, clickbait, bright colors, text overlay, arrows, faces, expressions, backgrounds, graphics, CTR, views, viral.";

    case "title":
      return "The user is describing YouTube video titles or asking for title suggestions. Common terms include: YouTube, title, clickbait, views, viral, trending, SEO, keywords, engagement, CTR.";

    case "recreate":
      return "The user is describing changes they want to make to an existing image or thumbnail. Common terms include: change, modify, replace, remove, add, edit, transform, recreate, improve, enhance.";

    case "faceswap":
      return "The user is describing changes they want to make to an existing image or thumbnail. Common terms include: change, modify, replace, remove, add, edit, transform, recreate, improve, enhance.";

    default:
      return "";
  }
}

/**
 * Validate audio blob before transcription with improved quality checks
 */
export function validateAudioBlob(audioBlob: Blob): {
  valid: boolean;
  error?: string;
} {
  // Check if blob exists
  if (!audioBlob) {
    return { valid: false, error: "No audio data provided" };
  }

  // Check file size (25MB limit for OpenAI)
  const maxSize = 25 * 1024 * 1024; // 25MB in bytes
  if (audioBlob.size > maxSize) {
    return { valid: false, error: "Audio file too large (max 25MB)" };
  }

  // Improved minimum size check based on audio format and duration
  // For speech recognition, we need at least 2-3 seconds of audio
  // WebM/Opus typically produces ~8KB per second for speech
  const minSize = 16 * 1024; // 16KB minimum (roughly 2 seconds of speech)
  if (audioBlob.size < minSize) {
    return {
      valid: false,
      error: "Recording too short. Please speak for at least 2-3 seconds.",
    };
  }

  // Check for very small files that might be just silence
  const verySmallSize = 4 * 1024; // 4KB
  if (audioBlob.size < verySmallSize) {
    return {
      valid: false,
      error:
        "Recording appears to contain only silence. Please speak louder and closer to your microphone.",
    };
  }

  // Check audio type if available
  if (audioBlob.type && !audioBlob.type.includes("audio")) {
    return {
      valid: false,
      error: "Invalid audio format detected",
    };
  }

  return { valid: true };
}

/**
 * Enhanced audio validation with duration check
 */
export function validateAudioWithDuration(
  audioBlob: Blob,
  durationSeconds: number
): { valid: boolean; error?: string } {
  // First run basic validation
  const basicValidation = validateAudioBlob(audioBlob);
  if (!basicValidation.valid) {
    return basicValidation;
  }

  // Check minimum duration
  const minDuration = 1.5; // 1.5 seconds minimum
  if (durationSeconds < minDuration) {
    return {
      valid: false,
      error: `Recording too short (${durationSeconds.toFixed(
        1
      )}s). Please speak for at least ${minDuration} seconds.`,
    };
  }

  // Check maximum duration (OpenAI has processing limits)
  const maxDuration = 300; // 5 minutes
  if (durationSeconds > maxDuration) {
    return {
      valid: false,
      error: `Recording too long (${Math.round(
        durationSeconds
      )}s). Maximum duration is ${maxDuration} seconds.`,
    };
  }

  // Check if duration matches expected file size (rough quality check)
  const expectedSizePerSecond = 8 * 1024; // ~8KB per second for compressed speech
  const expectedSize = durationSeconds * expectedSizePerSecond;
  const sizeRatio = audioBlob.size / expectedSize;

  // If file is much smaller than expected, it might be mostly silence
  if (sizeRatio < 0.3) {
    return {
      valid: false,
      error:
        "Audio quality appears low or contains mostly silence. Please speak clearly and check your microphone.",
    };
  }

  return { valid: true };
}

/**
 * Format duration in seconds to MM:SS format
 */
export function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
}

/**
 * Get supported audio formats for the current browser
 */
export function getSupportedAudioFormats(): string[] {
  if (typeof window === "undefined" || !("MediaRecorder" in window)) {
    return [];
  }

  const formats = [
    "audio/webm;codecs=opus",
    "audio/webm",
    "audio/mp4",
    "audio/wav",
  ];

  return formats.filter((format) => MediaRecorder.isTypeSupported(format));
}

/**
 * Check if speech-to-text is supported in the current environment
 */
export function isSpeechToTextSupported(): boolean {
  return (
    typeof window !== "undefined" &&
    "MediaRecorder" in window &&
    "navigator" in window &&
    "mediaDevices" in navigator &&
    "getUserMedia" in navigator.mediaDevices
  );
}
