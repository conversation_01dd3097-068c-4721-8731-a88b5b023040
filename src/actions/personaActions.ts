"use server";

import { db } from "@/src/db";
import { personas } from "@/src/db/schema";
import { eq, and, desc } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export interface CreatePersonaData {
  userId: string;
  name: string;
  finetuneId?: string;
  triggerWord?: string;
  trainingImagesCount: number;
}

export interface UpdatePersonaData {
  status?: "pending" | "training" | "ready" | "error";
  finetuneId?: string;
  modelId?: string; // Fine-tuned model ID
  triggerWord?: string;
  generatedImageUrl?: string;
  error?: string;
}

/**
 * Create a new persona
 */
export async function createPersona(data: CreatePersonaData) {
  try {
    const personaId = createId();

    const [persona] = await db
      .insert(personas)
      .values({
        id: personaId,
        userId: data.userId,
        name: data.name,
        finetuneId: data.finetuneId,
        triggerWord: data.triggerWord || "TOK",
        trainingImagesCount: data.trainingImagesCount,
        status: "pending",
      })
      .returning();

    return { success: true, persona };
  } catch (error) {
    console.error("Error creating persona:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create persona",
    };
  }
}

/**
 * Update a persona
 */
export async function updatePersona(
  personaId: string,
  userId: string,
  data: UpdatePersonaData
) {
  try {
    const [persona] = await db
      .update(personas)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(and(eq(personas.id, personaId), eq(personas.userId, userId)))
      .returning();

    if (!persona) {
      return { success: false, error: "Persona not found" };
    }

    return { success: true, persona };
  } catch (error) {
    console.error("Error updating persona:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update persona",
    };
  }
}

/**
 * Get a persona by ID (user-specific)
 */
export async function getPersona(personaId: string, userId: string) {
  try {
    const [persona] = await db
      .select()
      .from(personas)
      .where(and(eq(personas.id, personaId), eq(personas.userId, userId)))
      .limit(1);

    if (!persona) {
      return { success: false, error: "Persona not found" };
    }

    return { success: true, persona };
  } catch (error) {
    console.error("Error getting persona:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get persona",
    };
  }
}

/**
 * Get all personas for a user
 */
export async function getUserPersonas(userId: string) {
  try {
    const userPersonas = await db
      .select()
      .from(personas)
      .where(eq(personas.userId, userId))
      .orderBy(desc(personas.createdAt));

    return { success: true, personas: userPersonas };
  } catch (error) {
    console.error("Error getting user personas:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get personas",
    };
  }
}

/**
 * Delete a persona
 */
export async function deletePersona(personaId: string, userId: string) {
  try {
    const [deletedPersona] = await db
      .delete(personas)
      .where(and(eq(personas.id, personaId), eq(personas.userId, userId)))
      .returning();

    if (!deletedPersona) {
      return { success: false, error: "Persona not found" };
    }

    return { success: true, persona: deletedPersona };
  } catch (error) {
    console.error("Error deleting persona:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to delete persona",
    };
  }
}

/**
 * Get ready personas for a user (for use in thumbnail generation)
 */
export async function getReadyPersonas(userId: string) {
  try {
    const readyPersonas = await db
      .select()
      .from(personas)
      .where(and(eq(personas.userId, userId), eq(personas.status, "ready")))
      .orderBy(desc(personas.createdAt));

    return { success: true, personas: readyPersonas };
  } catch (error) {
    console.error("Error getting ready personas:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get ready personas",
    };
  }
}

/**
 * Get persona by fine-tune ID
 */
export async function getPersonaByFinetuneId(
  finetuneId: string,
  userId: string
) {
  try {
    const [persona] = await db
      .select()
      .from(personas)
      .where(
        and(eq(personas.finetuneId, finetuneId), eq(personas.userId, userId))
      )
      .limit(1);

    if (!persona) {
      return { success: false, error: "Persona not found" };
    }

    return { success: true, persona };
  } catch (error) {
    console.error("Error getting persona by fine-tune ID:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get persona",
    };
  }
}
