import { eq, desc, asc, and, sql } from "drizzle-orm";
import { db } from "@/src/db";
import { recreatedThumbnails } from "@/src/db/schema";
import { users } from "@/src/db/schema";

export async function getRecreatedThumbnail(id: string) {
  const [thumbnail] = await db
    .select()
    .from(recreatedThumbnails)
    .where(eq(recreatedThumbnails.id, id));
  return thumbnail;
}

export async function getUserRecreatedThumbnails(
  userId: string,
  {
    page = 1,
    limit = 12,
    search = "",
    sortBy = "createdAt",
    sortOrder = "desc",
    sourceType,
    personaUsed,
  }: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    sourceType?: "youtube_url" | "upload_file";
    personaUsed?: boolean;
  } = {}
) {
  const offset = (page - 1) * limit;

  // Build where conditions
  const conditions = [eq(recreatedThumbnails.userId, userId)];

  if (sourceType) {
    conditions.push(eq(recreatedThumbnails.sourceType, sourceType));
  }

  if (typeof personaUsed === "boolean") {
    conditions.push(eq(recreatedThumbnails.personaUsed, personaUsed));
  }

  // Use a single query with window function to get both count and data
  const results = await db
    .select({
      id: recreatedThumbnails.id,
      userId: recreatedThumbnails.userId,
      imageUrl: recreatedThumbnails.imageUrl,
      sourceType: recreatedThumbnails.sourceType,
      prompt: recreatedThumbnails.prompt,
      personaUsed: recreatedThumbnails.personaUsed,
      createdAt: recreatedThumbnails.createdAt,
      updatedAt: recreatedThumbnails.updatedAt,
      totalCount: sql<number>`count(*) over()`,
    })
    .from(recreatedThumbnails)
    .where(and(...conditions))
    .limit(limit)
    .offset(offset)
    .orderBy(
      sortBy === "createdAt"
        ? sortOrder === "desc"
          ? desc(recreatedThumbnails.createdAt)
          : asc(recreatedThumbnails.createdAt)
        : sortOrder === "desc"
        ? desc(recreatedThumbnails.updatedAt)
        : asc(recreatedThumbnails.updatedAt)
    );

  const totalCount = results.length > 0 ? Number(results[0].totalCount) : 0;
  const totalPages = Math.ceil(totalCount / limit);

  // Remove totalCount from individual records
  const thumbnails = results.map(({ totalCount, ...thumbnail }) => thumbnail);

  return {
    thumbnails,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export async function createRecreatedThumbnail({
  userId,
  imageUrl,
  sourceType,
  prompt,
  personaUsed = false,
}: {
  userId: string;
  imageUrl: string;
  sourceType: "youtube_url" | "upload_file";
  prompt: string;
  personaUsed?: boolean;
}) {
  // Ensure user exists in users table
  const [user] = await db.select().from(users).where(eq(users.userId, userId));
  if (!user) {
    throw new Error(
      "User does not exist in users table. Cannot create recreated thumbnail without a valid user."
    );
  }
  const [thumbnail] = await db
    .insert(recreatedThumbnails)
    .values({
      userId,
      imageUrl,
      sourceType,
      prompt,
      personaUsed,
    })
    .returning();
  return thumbnail;
}

export async function deleteRecreatedThumbnail(id: string) {
  await db.delete(recreatedThumbnails).where(eq(recreatedThumbnails.id, id));
}
