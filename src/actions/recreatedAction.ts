import { eq, desc, asc, and, sql } from "drizzle-orm";
import { db } from "@/src/db";
import { recreatedThumbnails } from "@/src/db/schema";

export async function getRecreatedThumbnail(id: string) {
  const [thumbnail] = await db
    .select()
    .from(recreatedThumbnails)
    .where(eq(recreatedThumbnails.id, id));
  return thumbnail;
}

export async function getUserRecreatedThumbnails(
  userId: string,
  {
    page = 1,
    limit = 12,
    search = "",
    sortBy = "createdAt",
    sortOrder = "desc",
    sourceType,
    personaUsed,
  }: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
    sourceType?: "youtube_url" | "upload_file";
    personaUsed?: boolean;
  } = {}
) {
  const offset = (page - 1) * limit;

  // Build where conditions
  const conditions = [eq(recreatedThumbnails.userId, userId)];
  
  if (sourceType) {
    conditions.push(eq(recreatedThumbnails.sourceType, sourceType));
  }
  
  if (typeof personaUsed === "boolean") {
    conditions.push(eq(recreatedThumbnails.personaUsed, personaUsed));
  }

  // Get total count
  const totalCount = await db
    .select({ count: sql<number>`count(*)` })
    .from(recreatedThumbnails)
    .where(and(...conditions))
    .then(res => Number(res[0].count));

  // Get thumbnails with pagination
  const thumbnails = await db
    .select()
    .from(recreatedThumbnails)
    .where(and(...conditions))
    .limit(limit)
    .offset(offset)
    .orderBy(
      sortBy === "createdAt" 
        ? sortOrder === "desc" 
          ? desc(recreatedThumbnails.createdAt)
          : asc(recreatedThumbnails.createdAt)
        : sortOrder === "desc"
          ? desc(recreatedThumbnails.updatedAt)
          : asc(recreatedThumbnails.updatedAt)
    );

  const totalPages = Math.ceil(totalCount / limit);

  return {
    thumbnails,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export async function createRecreatedThumbnail({
  userId,
  imageUrl,
  sourceType,
  prompt,
  personaUsed = false,
}: {
  userId: string;
  imageUrl: string;
  sourceType: "youtube_url" | "upload_file";
  prompt: string;
  personaUsed?: boolean;
}) {
  const [thumbnail] = await db
    .insert(recreatedThumbnails)
    .values({
      userId,
      imageUrl,
      sourceType,
      prompt,
      personaUsed,
    })
    .returning();
  return thumbnail;
}

export async function deleteRecreatedThumbnail(id: string) {
  await db.delete(recreatedThumbnails).where(eq(recreatedThumbnails.id, id));
}
