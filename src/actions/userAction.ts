import { eq } from "drizzle-orm";
import { db } from "@/src/db";
import { users } from "@/src/db/schema";

export async function getUser(id: string) {
  const [user] = await db.select().from(users).where(eq(users.userId, id));
  return user;
}

export async function getUserByEmail(email: string) {
  const [user] = await db.select().from(users).where(eq(users.email, email));
  return user;
}

export async function createUser({
  userId,
  email,
  name,
  avatarUrl,
}: {
  userId: string;
  email: string;
  name?: string;
  avatarUrl?: string;
}) {
  const [user] = await db
    .insert(users)
    .values({
      userId,
      email,
      name,
      avatarUrl,
    })
    .returning();
  return user;
}

export async function updateUser(
  userId: string,
  data: { name?: string; avatarUrl?: string }
) {
  const [user] = await db
    .update(users)
    .set({
      ...data,
      updatedAt: new Date(),
    })
    .where(eq(users.userId, userId))
    .returning();
  return user;
}

export async function deleteUser(userId: string) {
  await db.delete(users).where(eq(users.userId, userId));
}
