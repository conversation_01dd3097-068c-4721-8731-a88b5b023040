import { eq, desc, asc, and, sql } from "drizzle-orm";
import { db } from "@/src/db";
import { generatedThumbnails } from "@/src/db/schema";
import { users } from "@/src/db/schema";

export async function getGeneratedThumbnail(id: string) {
  const [thumbnail] = await db
    .select()
    .from(generatedThumbnails)
    .where(eq(generatedThumbnails.id, id));
  return thumbnail;
}

export async function getUserGeneratedThumbnails(
  userId: string,
  {
    page = 1,
    limit = 12,
    search = "",
    sortBy = "createdAt",
    sortOrder = "desc",
  }: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  } = {}
) {
  const offset = (page - 1) * limit;

  // Build where clause
  const whereClause = eq(generatedThumbnails.userId, userId);

  // Use a single query with window function to get both count and data
  const results = await db
    .select({
      id: generatedThumbnails.id,
      userId: generatedThumbnails.userId,
      prompt: generatedThumbnails.prompt,
      imageUrl: generatedThumbnails.imageUrl,
      title: generatedThumbnails.title,
      tags: generatedThumbnails.tags,
      createdAt: generatedThumbnails.createdAt,
      updatedAt: generatedThumbnails.updatedAt,
      totalCount: sql<number>`count(*) over()`,
    })
    .from(generatedThumbnails)
    .where(whereClause)
    .limit(limit)
    .offset(offset)
    .orderBy(
      sortBy === "createdAt"
        ? sortOrder === "desc"
          ? desc(generatedThumbnails.createdAt)
          : asc(generatedThumbnails.createdAt)
        : sortOrder === "desc"
        ? desc(generatedThumbnails.updatedAt)
        : asc(generatedThumbnails.updatedAt)
    );

  const totalCount = results.length > 0 ? Number(results[0].totalCount) : 0;
  const totalPages = Math.ceil(totalCount / limit);

  // Remove totalCount from individual records
  const thumbnails = results.map(({ totalCount, ...thumbnail }) => thumbnail);

  return {
    thumbnails,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export async function createGeneratedThumbnail({
  userId,
  prompt,
  imageUrl,
  title,
  tags = [],
}: {
  userId: string;
  prompt: string;
  imageUrl: string;
  title?: string;
  tags?: string[];
}) {
  // Ensure user exists in users table
  const [user] = await db.select().from(users).where(eq(users.userId, userId));
  if (!user) {
    throw new Error(
      "User does not exist in users table. Cannot create thumbnail without a valid user."
    );
  }
  const [thumbnail] = await db
    .insert(generatedThumbnails)
    .values({
      userId,
      prompt,
      imageUrl,
      title,
      tags,
    })
    .returning();
  return thumbnail;
}

export async function deleteGeneratedThumbnail(id: string) {
  await db.delete(generatedThumbnails).where(eq(generatedThumbnails.id, id));
}
