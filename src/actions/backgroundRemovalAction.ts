import { eq, desc, asc, and, sql } from "drizzle-orm";
import { db } from "@/src/db";
import { backgroundRemovals } from "@/src/db/schema";

export async function getBackgroundRemoval(id: string) {
  const [backgroundRemoval] = await db
    .select()
    .from(backgroundRemovals)
    .where(eq(backgroundRemovals.id, id));
  return backgroundRemoval;
}

export async function getUserBackgroundRemovals(
  userId: string,
  {
    page = 1,
    limit = 12,
    search = "",
    sortBy = "createdAt",
    sortOrder = "desc",
  }: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: "createdAt" | "updatedAt";
    sortOrder?: "asc" | "desc";
  } = {}
) {
  const offset = (page - 1) * limit;

  // Build where clause
  const whereClause = eq(backgroundRemovals.userId, userId);

  // Determine sort column
  const sortColumn =
    sortBy === "updatedAt"
      ? backgroundRemovals.updatedAt
      : backgroundRemovals.createdAt;

  // Use a single query with window function to get both count and data
  const results = await db
    .select({
      id: backgroundRemovals.id,
      userId: backgroundRemovals.userId,
      imageUrl: backgroundRemovals.imageUrl,
      createdAt: backgroundRemovals.createdAt,
      updatedAt: backgroundRemovals.updatedAt,
      totalCount: sql<number>`count(*) over()`,
    })
    .from(backgroundRemovals)
    .where(whereClause)
    .limit(limit)
    .offset(offset)
    .orderBy(sortOrder === "desc" ? desc(sortColumn) : asc(sortColumn));

  const totalCount = results.length > 0 ? results[0].totalCount : 0;
  const totalPages = Math.ceil(totalCount / limit);

  // Remove totalCount from the actual data
  const backgroundRemovalsList = results.map(({ totalCount, ...rest }) => rest);

  return {
    backgroundRemovals: backgroundRemovalsList,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export async function createBackgroundRemoval(data: {
  userId: string;
  imageUrl: string;
}) {
  const [backgroundRemoval] = await db
    .insert(backgroundRemovals)
    .values({
      userId: data.userId,
      imageUrl: data.imageUrl,
    })
    .returning();

  return backgroundRemoval;
}

export async function deleteBackgroundRemoval(id: string, userId: string) {
  const [deletedBackgroundRemoval] = await db
    .delete(backgroundRemovals)
    .where(
      and(eq(backgroundRemovals.id, id), eq(backgroundRemovals.userId, userId))
    )
    .returning();

  return deletedBackgroundRemoval;
}

export async function getUserBackgroundRemovalCount(userId: string) {
  const [result] = await db
    .select({
      count: sql<number>`count(*)`,
    })
    .from(backgroundRemovals)
    .where(eq(backgroundRemovals.userId, userId));

  return result?.count || 0;
}
