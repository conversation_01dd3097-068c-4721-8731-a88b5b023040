import { eq, desc, and } from "drizzle-orm";
import { db } from "@/src/db";
import { titleChatSessions, titleChatMessages, users } from "@/src/db/schema";

// Types for better type safety
export type TitleChatSession = typeof titleChatSessions.$inferSelect;
export type NewTitleChatSession = typeof titleChatSessions.$inferInsert;
export type TitleChatMessage = typeof titleChatMessages.$inferSelect;
export type NewTitleChatMessage = typeof titleChatMessages.$inferInsert;

export interface TitleChatSessionWithMessages extends TitleChatSession {
  messages: TitleChatMessage[];
}

// Session CRUD operations
export async function createTitleChatSession({
  userId,
  firstPrompt,
  title,
}: {
  userId: string;
  firstPrompt: string;
  title?: string;
}): Promise<TitleChatSession> {
  // Ensure user exists in users table
  const [user] = await db.select().from(users).where(eq(users.userId, userId));
  if (!user) {
    throw new Error(
      "User does not exist in users table. Cannot create chat session without a valid user."
    );
  }

  const [session] = await db
    .insert(titleChatSessions)
    .values({
      userId,
      firstPrompt,
      title,
    })
    .returning();

  return session;
}

export async function getTitleChatSession(
  sessionId: string
): Promise<TitleChatSession | null> {
  const [session] = await db
    .select()
    .from(titleChatSessions)
    .where(eq(titleChatSessions.id, sessionId));

  return session || null;
}

export async function getTitleChatSessionWithMessages(
  sessionId: string
): Promise<TitleChatSessionWithMessages | null> {
  const session = await getTitleChatSession(sessionId);
  if (!session) return null;

  const messages = await db
    .select()
    .from(titleChatMessages)
    .where(eq(titleChatMessages.sessionId, sessionId))
    .orderBy(titleChatMessages.createdAt);

  return {
    ...session,
    messages,
  };
}

export async function getUserTitleChatSessions(
  userId: string
): Promise<TitleChatSession[]> {
  return await db
    .select()
    .from(titleChatSessions)
    .where(eq(titleChatSessions.userId, userId))
    .orderBy(desc(titleChatSessions.updatedAt));
}

export async function updateTitleChatSession(
  sessionId: string,
  updates: Partial<Pick<TitleChatSession, "title">>
): Promise<TitleChatSession | null> {
  const [updatedSession] = await db
    .update(titleChatSessions)
    .set({
      ...updates,
      updatedAt: new Date(),
    })
    .where(eq(titleChatSessions.id, sessionId))
    .returning();

  return updatedSession || null;
}

export async function deleteTitleChatSession(sessionId: string): Promise<void> {
  // Messages will be automatically deleted due to cascade
  await db
    .delete(titleChatSessions)
    .where(eq(titleChatSessions.id, sessionId));
}

// Message CRUD operations
export async function createTitleChatMessage({
  sessionId,
  type,
  content,
  titles,
  status,
  error,
}: {
  sessionId: string;
  type: "user" | "assistant";
  content: string;
  titles?: { id: string; text: string; style: string }[];
  status?: "generating" | "success" | "error";
  error?: string;
}): Promise<TitleChatMessage> {
  const [message] = await db
    .insert(titleChatMessages)
    .values({
      sessionId,
      type,
      content,
      titles,
      status,
      error,
    })
    .returning();

  // Update session's updatedAt timestamp
  await db
    .update(titleChatSessions)
    .set({ updatedAt: new Date() })
    .where(eq(titleChatSessions.id, sessionId));

  return message;
}

export async function updateTitleChatMessage(
  messageId: string,
  updates: Partial<
    Pick<TitleChatMessage, "content" | "titles" | "status" | "error">
  >
): Promise<TitleChatMessage | null> {
  const [updatedMessage] = await db
    .update(titleChatMessages)
    .set(updates)
    .where(eq(titleChatMessages.id, messageId))
    .returning();

  if (updatedMessage) {
    // Update session's updatedAt timestamp
    await db
      .update(titleChatSessions)
      .set({ updatedAt: new Date() })
      .where(eq(titleChatSessions.id, updatedMessage.sessionId));
  }

  return updatedMessage || null;
}

export async function getSessionMessages(
  sessionId: string
): Promise<TitleChatMessage[]> {
  return await db
    .select()
    .from(titleChatMessages)
    .where(eq(titleChatMessages.sessionId, sessionId))
    .orderBy(titleChatMessages.createdAt);
}

export async function deleteSessionMessages(sessionId: string): Promise<void> {
  await db
    .delete(titleChatMessages)
    .where(eq(titleChatMessages.sessionId, sessionId));
}

// Utility functions
export async function generateSessionTitle(firstPrompt: string): Promise<string> {
  // Simple title generation from first prompt
  // Take first 50 characters and add ellipsis if longer
  const title = firstPrompt.length > 50 
    ? firstPrompt.substring(0, 50).trim() + "..."
    : firstPrompt.trim();
  
  return title;
}

export async function getUserSessionCount(userId: string): Promise<number> {
  const result = await db
    .select({ count: titleChatSessions.id })
    .from(titleChatSessions)
    .where(eq(titleChatSessions.userId, userId));
  
  return result.length;
}
