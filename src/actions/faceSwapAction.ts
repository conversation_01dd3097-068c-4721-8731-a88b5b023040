import { eq, desc, asc, sql } from "drizzle-orm";
import { db } from "@/src/db";
import { faceSwapThumbnails } from "@/src/db/schema";

export async function getFaceSwapThumbnail(id: string) {
  const [thumbnail] = await db
    .select()
    .from(faceSwapThumbnails)
    .where(eq(faceSwapThumbnails.id, id));
  return thumbnail;
}

export async function getUserFaceSwapThumbnails(
  userId: string,
  {
    page = 1,
    limit = 12,
    sortBy = "createdAt",
    sortOrder = "desc",
  }: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: "asc" | "desc";
  } = {}
) {
  const offset = (page - 1) * limit;

  // Use a single query with window function to get both count and data
  const results = await db
    .select({
      id: faceSwapThumbnails.id,
      userId: faceSwapThumbnails.userId,
      imageUrl: faceSwapThumbnails.imageUrl,
      originalImageUrl: faceSwapThumbnails.originalImageUrl,
      personaImageUrl: faceSwapThumbnails.personaImageUrl,
      createdAt: faceSwapThumbnails.createdAt,
      updatedAt: faceSwapThumbnails.updatedAt,
      totalCount: sql<number>`count(*) over()`,
    })
    .from(faceSwapThumbnails)
    .where(eq(faceSwapThumbnails.userId, userId))
    .limit(limit)
    .offset(offset)
    .orderBy(
      sortBy === "createdAt"
        ? sortOrder === "desc"
          ? desc(faceSwapThumbnails.createdAt)
          : asc(faceSwapThumbnails.createdAt)
        : sortOrder === "desc"
        ? desc(faceSwapThumbnails.updatedAt)
        : asc(faceSwapThumbnails.updatedAt)
    );

  const totalCount = results.length > 0 ? Number(results[0].totalCount) : 0;
  const totalPages = Math.ceil(totalCount / limit);

  // Remove totalCount from individual records
  const thumbnails = results.map(({ totalCount, ...thumbnail }) => thumbnail);

  return {
    thumbnails,
    pagination: {
      currentPage: page,
      totalPages,
      totalCount,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
}

export async function createFaceSwapThumbnail({
  userId,
  imageUrl,
  originalImageUrl,
  personaImageUrl,
}: {
  userId: string;
  imageUrl: string;
  originalImageUrl: string;
  personaImageUrl: string;
}) {
  const [thumbnail] = await db
    .insert(faceSwapThumbnails)
    .values({
      userId,
      imageUrl,
      originalImageUrl,
      personaImageUrl,
    })
    .returning();
  return thumbnail;
}

export async function deleteFaceSwapThumbnail(id: string) {
  await db.delete(faceSwapThumbnails).where(eq(faceSwapThumbnails.id, id));
}
