import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  index,
  json,
} from "drizzle-orm/pg-core";
import { createId } from "@paralleldrive/cuid2";

// Users table
export const users = pgTable("users", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => createId()),
  userId: text("userId").notNull().unique(),
  email: text("email").notNull().unique(),
  name: text("name"),
  avatarUrl: text("avatarUrl"),
  createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

// Generated Thumbnails table
export const generatedThumbnails = pgTable(
  "generated_thumbnails",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    prompt: text("prompt").notNull(),
    imageUrl: text("imageUrl").notNull(),
    title: text("title"),
    tags: text("tags").array().notNull().default([]),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("generated_thumbnails_userId_idx").on(table.userId),
    createdAtIdx: index("generated_thumbnails_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("generated_thumbnails_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
  })
);

// Recreated Thumbnails table
export const recreatedThumbnails = pgTable(
  "recreated_thumbnails",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    imageUrl: text("imageUrl").notNull(),
    sourceType: text("sourceType").notNull(),
    prompt: text("prompt").notNull(),
    personaUsed: boolean("personaUsed").notNull().default(false),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("recreated_thumbnails_userId_idx").on(table.userId),
    createdAtIdx: index("recreated_thumbnails_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("recreated_thumbnails_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
    sourceTypeIdx: index("recreated_thumbnails_sourceType_idx").on(
      table.sourceType
    ),
    personaUsedIdx: index("recreated_thumbnails_personaUsed_idx").on(
      table.personaUsed
    ),
  })
);

// Face Swap Thumbnails table
export const faceSwapThumbnails = pgTable(
  "faceswap_thumbnails",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    imageUrl: text("imageUrl").notNull(),
    originalImageUrl: text("originalImageUrl").notNull(),
    personaImageUrl: text("personaImageUrl").notNull(),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("faceswap_thumbnails_userId_idx").on(table.userId),
    createdAtIdx: index("faceswap_thumbnails_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("faceswap_thumbnails_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
  })
);

// Title Chat Sessions table
export const titleChatSessions = pgTable(
  "title_chat_sessions",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    title: text("title"), // Auto-generated title for the session
    firstPrompt: text("firstPrompt").notNull(), // The initial prompt that started the session
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("title_chat_sessions_userId_idx").on(table.userId),
    createdAtIdx: index("title_chat_sessions_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("title_chat_sessions_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
  })
);

// Title Chat Messages table
export const titleChatMessages = pgTable(
  "title_chat_messages",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    sessionId: text("sessionId")
      .notNull()
      .references(() => titleChatSessions.id, { onDelete: "cascade" }),
    type: text("type", { enum: ["user", "assistant"] }).notNull(),
    content: text("content").notNull(),
    titles: json("titles").$type<
      {
        id: string;
        text: string;
        style: string;
      }[]
    >(), // For assistant messages containing generated titles
    status: text("status", { enum: ["generating", "success", "error"] }),
    error: text("error"),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (table) => ({
    sessionIdIdx: index("title_chat_messages_sessionId_idx").on(
      table.sessionId
    ),
    createdAtIdx: index("title_chat_messages_createdAt_idx").on(
      table.createdAt
    ),
    sessionIdCreatedAtIdx: index(
      "title_chat_messages_sessionId_createdAt_idx"
    ).on(table.sessionId, table.createdAt),
  })
);

// Personas table
export const personas = pgTable(
  "personas",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    name: text("name").notNull(),
    finetuneId: text("finetuneId"), // Flux fine-tune ID
    modelId: text("modelId"), // Fine-tuned model ID returned upon completion
    triggerWord: text("triggerWord").notNull().default("TOK"),
    status: text("status").notNull().default("pending"), // pending, training, ready, error
    generatedImageUrl: text("generatedImageUrl"), // URL to the generated persona image
    trainingImagesCount: integer("trainingImagesCount").notNull().default(0),
    error: text("error"), // Error message if training failed
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("personas_userId_idx").on(table.userId),
    statusIdx: index("personas_status_idx").on(table.status),
    createdAtIdx: index("personas_createdAt_idx").on(table.createdAt),
    finetuneIdIdx: index("personas_finetuneId_idx").on(table.finetuneId),
    userIdCreatedAtIdx: index("personas_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
  })
);

// Background Removal table
export const backgroundRemovals = pgTable(
  "background_removals",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    imageUrl: text("imageUrl").notNull(),
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("background_removals_userId_idx").on(table.userId),
    createdAtIdx: index("background_removals_createdAt_idx").on(
      table.createdAt
    ),
    userIdCreatedAtIdx: index("background_removals_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
  })
);

// Edited Images table - stores all edited versions as separate entries
export const editedImages = pgTable(
  "edited_images",
  {
    id: text("id")
      .primaryKey()
      .$defaultFn(() => createId()),
    userId: text("userId")
      .notNull()
      .references(() => users.userId, { onDelete: "cascade" }),
    originalImageUrl: text("originalImageUrl").notNull(), // URL of the original image
    editedImageUrl: text("editedImageUrl").notNull(), // URL of the edited result
    sourceType: text("sourceType").notNull(), // "generated", "recreated", "faceswap", "background_removal"
    sourceId: text("sourceId").notNull(), // ID of the original image record
    prompt: text("prompt").notNull(), // Edit prompt used
    personaUsed: boolean("personaUsed").notNull().default(false), // Whether persona was used for editing
    createdAt: timestamp("createdAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updatedAt", { precision: 6, withTimezone: true })
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdIdx: index("edited_images_userId_idx").on(table.userId),
    sourceTypeIdx: index("edited_images_sourceType_idx").on(table.sourceType),
    sourceIdIdx: index("edited_images_sourceId_idx").on(table.sourceId),
    createdAtIdx: index("edited_images_createdAt_idx").on(table.createdAt),
    userIdCreatedAtIdx: index("edited_images_userId_createdAt_idx").on(
      table.userId,
      table.createdAt
    ),
    userIdSourceIdx: index("edited_images_userId_source_idx").on(
      table.userId,
      table.sourceType,
      table.sourceId
    ),
  })
);
