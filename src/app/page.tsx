"use client";

import { useEffect, useState } from "react";
import { useAuthStore } from "@/src/store/authStore";
import { useRouter } from "next/navigation";
import {
  HeroSection,
  FeaturesSection,
  ToolsShowcase,
  CTASection,
} from "@/src/components/landing";
import ExitPopup from "@/src/components/common/ExitPopup";
import { useExitIntent } from "@/src/hooks/useExitIntent";

export default function HomePage() {
  const [mounted, setMounted] = useState(false);
  const user = useAuthStore((state) => state.user);
  const isLoadingAuth = useAuthStore((state) => state.isLoading);
  const router = useRouter();

  // Exit intent popup
  const { showExitIntent, closeExitIntent } = useExitIntent({
    enabled: true,
    delay: 500,
    sensitivity: 20,
  });

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted && !isLoadingAuth && user) {
      router.replace("/dashboard");
    }
  }, [mounted, user, isLoadingAuth, router]);

  if (!mounted || isLoadingAuth || (mounted && user)) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-8 bg-background text-foreground">
        <p>Loading...</p>
      </div>
    );
  }
  return (
    <div className="min-h-screen bg-background text-foreground">
      <main className="pt-24">
        {" "}
        {/* Account for fixed navbar */}
        <HeroSection />
        <FeaturesSection />
        <ToolsShowcase />
        <CTASection />
      </main>

      {/* Exit Intent Popup */}
      <ExitPopup isOpen={showExitIntent} onClose={closeExitIntent} />
    </div>
  );
}
