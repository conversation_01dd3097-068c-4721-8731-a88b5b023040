"use client";

import { useEffect } from "react";

import { useConditionalRouting } from "@/src/hooks/useConditionalRouting";

import ThumbnailPreviewInterface from "@/src/components/thumbnail-preview/ThumbnailPreviewInterface";

export default function ThumbnailPreviewPage() {
  const { redirectToAuthenticatedVersion } = useConditionalRouting();

  useEffect(() => {
    redirectToAuthenticatedVersion("/tools/thumbnail-preview");
  }, [redirectToAuthenticatedVersion]);

  return (
    <div className="h-full">
      <ThumbnailPreviewInterface />
    </div>
  );
}
