"use client";

import { useEffect } from "react";
import { ThumbnailRatingInterface } from "@/src/components/tools/thumbnail-rating/ThumbnailRatingInterface";
import { useConditionalRouting } from "@/src/hooks/useConditionalRouting";

export default function ThumbnailRatingPage() {
  const { redirectToAuthenticatedVersion } = useConditionalRouting();

  useEffect(() => {
    redirectToAuthenticatedVersion("/thumbnail-rating");
  }, [redirectToAuthenticatedVersion]);

  return (
    <div>
      <ThumbnailRatingInterface />
    </div>
  );
}
