"use client";

import Link from "next/link";
import { But<PERSON> } from "@/src/components/ui/button";

export default function AuthCodeErrorPage() {
  return (
    <div className="min-h-screen bg-background text-foreground flex flex-col items-center justify-center p-4 text-center">
      <div className="w-full max-w-md p-8 space-y-6 bg-card text-card-foreground rounded-lg shadow-xl">
        <h1 className="text-2xl font-semibold text-destructive">
          Authentication Error
        </h1>
        <p className="text-muted-foreground">
          Sorry, we couldn&apos;t sign you in. This might be due to an issue
          with the authentication provider or an invalid request.
        </p>
        <p className="text-muted-foreground">Please try signing in again.</p>
        <Link href="/auth" passHref>
          <Button variant="outline" className="w-full">
            Back to Sign In
          </Button>
        </Link>
      </div>
    </div>
  );
}
