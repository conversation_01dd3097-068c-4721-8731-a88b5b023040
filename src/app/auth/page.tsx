"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuthStore } from "@/src/store/authStore";
import { Button } from "@/src/components/ui/button";
import GoogleIcon from "@/src/components/ui/Google";
import { Star, Users, TrendingUp } from "lucide-react";

export default function AuthPage() {
  const router = useRouter();
  const { user, isLoading, signInWithGoogle } = useAuthStore();

  useEffect(() => {
    if (user) {
      router.replace("/dashboard");
    }
  }, [user, router]);

  const handleGoogle = async () => {
    await signInWithGoogle();
  };

  return (
    <div className="min-h-screen w-full lg:grid lg:grid-cols-2">
      {/* Left Side - Marketing Content (Visible on lg screens and up) */}
      <div className="hidden bg-muted lg:flex flex-col justify-between p-12">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold text-foreground">Clickworthy</span>
        </div>

        <div className="w-full max-w-none space-y-8">
          {/* YouTube Video */}
          <div className="relative w-full h-64 md:h-80 lg:h-96 rounded-lg overflow-hidden shadow-2xl">
            <iframe
              src="https://www.youtube.com/embed/dQw4w9WgXcQ?autoplay=1&mute=1&loop=1&playlist=dQw4w9WgXcQ&controls=0&showinfo=0&rel=0&modestbranding=1"
              title="Clickworthy Demo"
              className="w-full h-full"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>

          {/* Trust Indicators */}
          <div className="space-y-4 text-center">
            {/* User Avatars */}
            <div className="flex items-center justify-center gap-2">
              <div className="flex -space-x-2">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-400 to-blue-600 border-2 border-background flex items-center justify-center text-xs font-semibold text-white">
                  A
                </div>
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-400 to-green-600 border-2 border-background flex items-center justify-center text-xs font-semibold text-white">
                  M
                </div>
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-400 to-purple-600 border-2 border-background flex items-center justify-center text-xs font-semibold text-white">
                  S
                </div>
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-600 border-2 border-background flex items-center justify-center text-xs font-semibold text-white">
                  J
                </div>
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-pink-400 to-pink-600 border-2 border-background flex items-center justify-center text-xs font-semibold text-white">
                  +
                </div>
              </div>
            </div>

            <div className="space-y-1">
              <p className="text-sm font-semibold text-foreground">
                Trusted by 50,000+ Users
              </p>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-foreground">
                  Excellent
                </span>
                <div className="flex items-center gap-0.5">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-4 h-4 fill-green-500 text-green-500"
                    />
                  ))}
                </div>
                <span className="text-sm text-muted-foreground">
                  Trustpilot
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-sm text-muted-foreground">
          © {new Date().getFullYear()} Clickworthy Inc. All rights reserved.
        </div>
      </div>

      {/* Right Side - Authentication Form */}
      <div className="flex items-center justify-center p-6 sm:p-12 h-screen">
        <div className="mx-auto grid w-full max-w-sm gap-6">
          <div className="grid gap-2 text-center">
            <h1 className="text-3xl font-bold text-foreground">Get Started</h1>
            <p className="text-balance text-muted-foreground">
              Continue with Google to access your dashboard and create stunning
              thumbnails in seconds.
            </p>
          </div>

          <div className="grid gap-4">
            <Button
              onClick={handleGoogle}
              disabled={isLoading}
              variant="outline"
              className="w-full h-11 flex items-center justify-center gap-3"
            >
              <GoogleIcon className="h-5 w-5" />
              <span className="text-base font-medium">
                {isLoading ? "Signing in..." : "Continue with Google"}
              </span>
            </Button>
          </div>

          <p className="px-8 text-center text-xs text-muted-foreground">
            By continuing, you agree to our{" "}
            <Link
              href="/terms"
              className="underline underline-offset-4 hover:text-primary"
            >
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link
              href="/privacy"
              className="underline underline-offset-4 hover:text-primary"
            >
              Privacy Policy
            </Link>
            .
          </p>
        </div>
      </div>
    </div>
  );
}
