import { NextResponse, type NextRequest } from "next/server";
// Import your own createClient from utils/supabase/server.ts
import { createClient } from "@/src/lib/supabase/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const { searchParams, origin } = requestUrl;
  const code = searchParams.get("code");
  const next = searchParams.get("next") ?? "/dashboard"; // Default redirect after successful auth
  const error = searchParams.get("error");
  const error_description = searchParams.get("error_description");

  // Handle OAuth errors
  if (error) {
    console.error(`[Auth Callback] OAuth error: ${error}`, error_description);
    return NextResponse.redirect(
      `${origin}/auth/auth-code-error?error=${encodeURIComponent(
        error
      )}&message=${encodeURIComponent(error_description || "")}`
    );
  }

  console.log(
    `[Auth Callback] Received request. Code: ${code}, Next: ${next}, Origin: ${origin}`
  );

  if (code) {
    console.log(
      "[Auth Callback] OAuth flow: Attempting to exchange code for session."
    );
    // Use your server-side Supabase client, now awaiting it as createClient is async
    const supabase = await createClient();

    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (!error) {
      console.log(
        "[Auth Callback] OAuth flow: Code exchange successful. Redirecting to:",
        `${origin}${next}`
      );
      return NextResponse.redirect(`${origin}${next}`);
    }

    console.error(
      "[Auth Callback] OAuth flow: Error exchanging code for session:",
      error.message
    );
    return NextResponse.redirect(
      `${origin}/auth/auth-code-error?message=OAuthCodeExchangeFailed&error=${encodeURIComponent(
        error.message
      )}`
    );
  }

  // If no code is present, redirect to auth page with error
  console.log(
    "[Auth Callback] No code found. Redirecting to auth page with error."
  );
  return NextResponse.redirect(
    `${origin}/auth/auth-code-error?message=NoAuthCodeProvided`
  );
}
