"use client";

import { Badge } from "@/src/components/ui/badge";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON>eader,
  Card<PERSON>itle,
} from "@/src/components/ui/card";
import { Shield } from "lucide-react";

export default function RefundPage() {
  const lastUpdated = "20th July 2025";

  return (
    <div className="min-h-screen bg-background text-foreground">
      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-6 py-8">
              <Badge
                variant="secondary"
                className="px-4 py-2 text-sm font-medium"
              >
                <Shield className="w-4 h-4 mr-2" />
                Refund Policy
              </Badge>

              <div className="space-y-4 max-w-4xl mx-auto">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
                  Refund{" "}
                  <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    Policy
                  </span>
                </h1>
                <div className="space-y-2 text-base text-foreground">
                  <div>
                    <strong>Effective Date:</strong> {lastUpdated}
                  </div>
                </div>
                <p className="text-lg text-foreground max-w-3xl mx-auto">
                  At Clickworthy.ai, we strive to help creators generate
                  high-performing thumbnails with minimal effort. However, due
                  to the instant and digital nature of our product, we maintain
                  the following refund policy:
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Refund Policy Content */}
        <section className="py-8">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
            <div className="space-y-8">
              {/* No Refunds After Generation */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    ❌ No Refunds After Generation
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground mb-4">
                    Once your AI-generated thumbnails have been created, we are
                    unable to offer refunds, even if they have not been
                    downloaded.
                  </p>
                  <p className="text-foreground">
                    Our platform delivers a personalized, on-demand service and
                    utilizes significant AI resources per generation. This makes
                    refunds impractical once a request is processed.
                  </p>
                </CardContent>
              </Card>

              {/* Regeneration Promise */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    🔁 We&apos;ll Make It Right (Regeneration Promise)
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-foreground">
                    If you experience a technical error with your thumbnail
                    output (e.g. corrupted image, completely irrelevant result),
                    we&apos;re happy to regenerate your thumbnails at no
                    additional cost — once per order.
                  </p>
                  <div>
                    <p className="text-foreground font-semibold mb-2">
                      To request a regeneration:
                    </p>
                    <ul className="list-disc list-inside space-y-1 text-foreground ml-4">
                      <li>Email <EMAIL></li>
                      <li>
                        Include your account email and screenshots of the issue
                      </li>
                      <li>Requests must be made within 7 days of order</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>

              {/* Refund Eligibility */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    💸 Refund Eligibility — Strictly Limited
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-foreground">
                    To maintain fairness and platform sustainability, refunds
                    are issued only under the following strict conditions:
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-foreground ml-4">
                    <li>You have generated fewer than 3 thumbnails in total</li>
                    <li>
                      You have not downloaded or used more than one thumbnail
                    </li>
                    <li>
                      Your refund request is made within 7 days of purchase
                    </li>
                    <li>
                      Your request is based on technical issues, not personal
                      preferences
                    </li>
                    <li>
                      You have not already received a free regeneration for that
                      order
                    </li>
                  </ul>
                  <div className="mt-4 p-4 bg-destructive/10 rounded-lg border border-destructive/20">
                    <p className="text-foreground font-bold">
                      🔒 Important: We do not offer partial refunds. Refunds are
                      all-or-nothing and are issued only if fewer than 3
                      thumbnails were generated from your account.
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Not Covered */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    🙅 Not Covered by Refund or Regeneration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <p className="text-foreground font-semibold mb-2">
                      Subjective dissatisfaction with design, style, or quality.
                    </p>
                    <p className="text-foreground text-sm ml-4">
                      Preferences like: &quot;I didn&apos;t like the
                      style,&quot; &quot;It wasn&apos;t what I imagined,&quot;
                      etc.
                    </p>
                  </div>
                  <div>
                    <p className="text-foreground font-semibold">
                      Incorrect prompts or input errors by user
                    </p>
                  </div>
                  <div>
                    <p className="text-foreground font-semibold mb-2">
                      Discounted purchases or limited-time deals.
                    </p>
                    <p className="text-foreground text-sm ml-4">
                      Orders made using limited-time discounts, credits, or
                      bundles
                    </p>
                  </div>
                  <div>
                    <p className="text-foreground font-semibold mb-2">
                      Extensive use of platform prior to request.
                    </p>
                    <p className="text-foreground text-sm ml-4">
                      Usage of multiple thumbnails, then asking for a refund
                    </p>
                  </div>
                  <div>
                    <p className="text-foreground font-semibold">
                      Requests made after 7 days of purchase
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Abuse Prevention Notice */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    🛡️ Abuse Prevention Notice
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-foreground">
                    We reserve the right to decline refund or regeneration
                    requests if your account:
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-foreground ml-4">
                    <li>Shows excessive generation activity</li>
                    <li>Requests repeated refunds across multiple orders</li>
                    <li>Violates our fair-use policy</li>
                  </ul>
                  <p className="text-foreground">
                    This ensures we can continue serving all creators
                    sustainably.
                  </p>
                </CardContent>
              </Card>

              {/* How Long Do Refunds Take */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    ⏰ How Long Do Refunds Take
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-foreground">
                    We will review and respond to refund requests, in accordance
                    with this policy, within 1-3 business days. If your refund
                    is approved, the funds will be returned to your original
                    payment method within 5-10 business days.
                  </p>
                </CardContent>
              </Card>

              {/* Our Commitment */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    💌 Our Commitment
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-foreground">
                    While we can&apos;t offer refunds for subjective reasons,
                    we&apos;re here to support you. If you&apos;re genuinely
                    unhappy, reach out and we&apos;ll do our best to improve
                    your experience.
                  </p>
                </CardContent>
              </Card>

              {/* Understanding */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    📋 Understanding
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-foreground">
                    By using our Service or any other services provided by us,
                    you acknowledge that you have read, understood, and agree to
                    be bound by this Refund Policy.
                  </p>
                  <p className="text-foreground">
                    This Refund Policy is governed by the laws of India. Any
                    legal disputes arising out of or in connection with this
                    Refund Policy shall be subject to the exclusive jurisdiction
                    of the courts of India.
                  </p>
                  <p className="text-foreground">
                    If you do not agree with this Refund Policy or cannot comply
                    with its terms, you should not use our Service. Any
                    exceptions or modifications to this Refund Policy will be
                    made at the sole discretion of Clickworthy.
                  </p>
                </CardContent>
              </Card>

              {/* Contact Us */}
              <Card className="border-2 border-border bg-card shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    📞 Contact Us
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-foreground">
                    📧 <strong>Email:</strong> <EMAIL>
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
