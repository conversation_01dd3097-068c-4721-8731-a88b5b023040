import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";

// Cache control headers
const cacheHeaders = {
  "Cache-Control": "private, max-age=300", // Cache for 5 minutes for authenticated users
  Vary: "Authorization", // Vary cache by auth status
};

/**
 * GET /api/user
 * Returns the current authenticated user's information
 * Now optimized with request-scoped caching
 */
export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // User is automatically authenticated and synced via withAuth wrapper
    const { dbUser } = req.user;

    // Return user data with cache headers
    return NextResponse.json(
      { user: dbUser },
      {
        headers: cacheHeaders,
      }
    );
  } catch (error) {
    console.error("Error in user profile API:", error);
    return NextResponse.json(
      { error: "Failed to retrieve user profile" },
      { status: 500 }
    );
  }
});

/**
 * PATCH /api/user
 * Updates the current authenticated user's profile information
 */
export const PATCH = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const { updateUser } = await import("@/src/actions/userAction");
    const { userId } = req.user;

    // Parse the request body
    const { name } = await req.json();

    // Update user in database
    const updatedUser = await updateUser(userId, { name });

    if (!updatedUser) {
      return NextResponse.json(
        { error: "Failed to update user" },
        { status: 500 }
      );
    }

    // Return updated user data
    return NextResponse.json(
      { user: updatedUser },
      {
        headers: cacheHeaders,
      }
    );
  } catch (error) {
    console.error("Error updating user profile:", error);
    return NextResponse.json(
      { error: "Failed to update user profile" },
      { status: 500 }
    );
  }
});
