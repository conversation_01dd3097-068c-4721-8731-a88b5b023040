import { fal } from "@fal-ai/client";
import { NextRequest, NextResponse } from "next/server";
import cloudinary from "@/src/lib/cloudinary";

interface ClarityUpscalerResult {
  data: {
    image: {
      url: string;
    };
  };
}

const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export async function POST(req: NextRequest) {
  const { image_url, upscale_factor, creativity } = await req.json();

  try {
    const { request_id } = await fal.queue.submit("fal-ai/clarity-upscaler", {
      input: {
        image_url,
        prompt: "masterpiece, ultra high quality, sharp focus, detailed",
        upscale_factor: upscale_factor || 2,
        negative_prompt:
          "(low quality, blurry, noise, artifact, jpeg artifacts)",
        creativity: creativity || 0.35,
        resemblance: 0.9,
        guidance_scale: 4.5,
        num_inference_steps: 20,
        enable_safety_checker: true,
      },
    });

    let result: ClarityUpscalerResult | null = null;
    let attempts = 0;
    const maxAttempts = 30; // 60 seconds timeout

    while (attempts < maxAttempts) {
      const statusResponse = await fal.queue.status("fal-ai/clarity-upscaler", {
        requestId: request_id,
        logs: false,
      });

      if (statusResponse.status === "COMPLETED") {
        result = (await fal.queue.result("fal-ai/clarity-upscaler", {
          requestId: request_id,
        })) as unknown as ClarityUpscalerResult;
        break;
      }

      attempts++;
      await sleep(2000); // Poll every 2 seconds
    }

    if (!result) {
      throw new Error("Fal.ai job timed out after 60 seconds.");
    }

    console.log("Fal.ai API Result:", result);

    if (
      result &&
      result.data &&
      result.data.image &&
      typeof result.data.image.url === "string"
    ) {
      // Fetch the image data from the URL
      const imageResponse = await fetch(result.data.image.url);
      const imageBuffer = await imageResponse.arrayBuffer();
      const imageBase64 = Buffer.from(imageBuffer).toString("base64");
      const dataUri = `data:${imageResponse.headers.get(
        "content-type"
      )};base64,${imageBase64}`;

      const cloudinaryResponse = await cloudinary.uploader.upload(dataUri, {
        folder: "thumbnail-upscales",
        resource_type: "image",
        quality: "auto:best",
      });
      return NextResponse.json({ imageUrl: cloudinaryResponse.secure_url });
    } else {
      console.error("Invalid result from Fal.ai API:", result);
      return NextResponse.json(
        { error: "Failed to get upscaled image URL" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error(error);
    return NextResponse.json(
      { error: "Failed to upscale image" },
      { status: 500 }
    );
  }
}
