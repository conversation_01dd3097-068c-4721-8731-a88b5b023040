import { NextRequest, NextResponse } from "next/server";
import {
  getTrendingVideosServer,
  clearCacheData,
} from "@/src/lib/youtube-trending";

export async function GET(request: NextRequest) {
  try {
    const trendingVideos = await getTrendingVideosServer();

    return NextResponse.json({
      success: true,
      videos: trendingVideos,
      count: trendingVideos.length,
    });
  } catch (error) {
    console.error("Error in trending videos API:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch trending videos",
        videos: [],
      },
      { status: 500 }
    );
  }
}

// Optional: Add POST method to force refresh
export async function POST(request: NextRequest) {
  try {
    // Force refresh by bypassing cache
    const trendingVideos = await getTrendingVideosServer();

    return NextResponse.json({
      success: true,
      videos: trendingVideos,
      count: trendingVideos.length,
      refreshed: true,
    });
  } catch (error) {
    console.error("Error refreshing trending videos:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to refresh trending videos",
        videos: [],
      },
      { status: 500 }
    );
  }
}

// DELETE method to clear cache data for testing
export async function DELETE(request: NextRequest) {
  try {
    clearCacheData();

    return NextResponse.json({
      success: true,
      message: "Cache data cleared successfully",
    });
  } catch (error) {
    console.error("Error clearing cache data:", error);

    return NextResponse.json(
      {
        success: false,
        error: "Failed to clear cache data",
      },
      { status: 500 }
    );
  }
}
