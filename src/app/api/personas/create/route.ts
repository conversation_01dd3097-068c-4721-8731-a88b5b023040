import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  createFluxFinetuneAPI,
  FluxFinetuneAPIError,
} from "@/src/lib/flux-finetune";
import { createPersona, updatePersona } from "@/src/actions/personaActions";
import {
  validateImagesForFinetune,
  createTrainingZip,
  getFinetuningRecommendations,
  generatePersonaDescription,
  validatePersonaName,
  estimateFinetuningTime,
} from "@/src/lib/persona-utils";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 300;

async function handler(req: AuthenticatedRequest) {
  if (req.method !== "POST") {
    return NextResponse.json(
      { success: false, error: "Method not allowed" },
      { status: 405 }
    );
  }

  try {
    const formData = await req.formData();
    const name = formData.get("name") as string;
    const files = formData.getAll("images") as File[];

    console.log("Received persona creation request:");
    console.log("- Name:", name);
    console.log("- Individual files:", files.length);

    // Validate persona name
    const nameValidation = validatePersonaName(name);
    if (!nameValidation.valid) {
      return NextResponse.json(
        { success: false, error: nameValidation.error },
        { status: 400 }
      );
    }

    // Validate that we have individual images (ZIP upload removed)
    if (files.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: "Please provide at least 3 images for training",
        },
        { status: 400 }
      );
    }

    // Validate the uploaded images
    const imageValidation = validateImagesForFinetune(files);
    if (!imageValidation.valid) {
      return NextResponse.json(
        { success: false, error: imageValidation.error },
        { status: 400 }
      );
    }

    console.log(`Creating persona "${name}" with ${files.length} images`);

    // Create persona record in database
    const personaResult = await createPersona({
      userId: req.user.userId,
      name,
      trainingImagesCount: files.length,
    });

    if (!personaResult.success) {
      return NextResponse.json(
        { success: false, error: personaResult.error },
        { status: 500 }
      );
    }

    const persona = personaResult.persona!;

    try {
      // Create training ZIP file from individual images
      console.log("Creating training ZIP file from uploaded images...");

      // Use person's name as trigger word for better face consistency
      const triggerWord = name.toLowerCase().replace(/\s+/g, ""); // Use actual name as trigger word
      const recommendations = getFinetuningRecommendations(files.length);
      const description = generatePersonaDescription(name, files.length);

      console.log(
        `Using person's name as trigger word: "${triggerWord}" for persona: "${name}"`
      );
      console.log(`Training recommendations:`, recommendations);

      // Create ZIP from individual images (ZIP upload option removed)
      console.log("Creating ZIP from individual images");
      const zipBase64 = await createTrainingZip(files);

      // Initialize Flux Fine-tune API
      const fluxFinetuneAPI = createFluxFinetuneAPI();

      console.log("Submitting fine-tuning request to Flux...");

      // Submit fine-tuning request optimized for face accuracy
      const finetuneResponse = await fluxFinetuneAPI.submitFinetune({
        file_data: zipBase64,
        finetune_comment: description,
        trigger_word: triggerWord, // Use person's actual name for better face consistency
        mode: "character", // Use character mode for persona training
        iterations: 1000, // Increased for better face detail learning (was 300)
        learning_rate: 0.00008, // Slightly lower for more stable face feature learning
        captioning: true, // Enable auto-captioning
        priority: "quality", // Use quality priority for better results
        finetune_type: "lora", // LoRA training for efficiency
        lora_rank: 32, // Keep 32 for maximum face detail preservation
      });

      console.log("Fine-tuning response:", finetuneResponse);
      console.log(
        `Fine-tuning submitted with ID: ${finetuneResponse.finetune_id}`
      );

      // Update persona with fine-tune details
      await updatePersona(persona.id, req.user.userId, {
        finetuneId: finetuneResponse.finetune_id,
        triggerWord,
        status: "training",
      });

      return NextResponse.json({
        success: true,
        persona: {
          ...persona,
          finetuneId: finetuneResponse.finetune_id,
          triggerWord,
          status: "training",
        },
        estimatedTime: estimateFinetuningTime(
          files.length,
          recommendations.iterations
        ),
      });
    } catch (finetuneError) {
      console.error("Fine-tuning submission failed:", finetuneError);

      // Update persona status to error
      await updatePersona(persona.id, req.user.userId, {
        status: "error",
        error:
          finetuneError instanceof FluxFinetuneAPIError
            ? finetuneError.message
            : "Failed to submit fine-tuning request",
      });

      if (finetuneError instanceof FluxFinetuneAPIError) {
        // Handle specific Flux API errors
        if (finetuneError.message.includes("moderated")) {
          return NextResponse.json(
            {
              success: false,
              error:
                "Training images were rejected by content moderation. Please use appropriate images of yourself.",
            },
            { status: 400 }
          );
        }

        if (finetuneError.status === 402) {
          return NextResponse.json(
            {
              success: false,
              error:
                "Insufficient credits for fine-tuning. Please check your Flux API account.",
            },
            { status: 402 }
          );
        }

        if (finetuneError.status === 429) {
          return NextResponse.json(
            {
              success: false,
              error:
                "Too many fine-tuning requests. Please wait before creating another persona.",
            },
            { status: 429 }
          );
        }
      }

      return NextResponse.json(
        {
          success: false,
          error: "Failed to start persona training. Please try again.",
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error creating persona:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

export const POST = withAuth(handler);
