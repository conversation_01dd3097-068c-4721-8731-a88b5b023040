import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { getUserPersonas, deletePersona } from "@/src/actions/personaActions";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 300;
async function handler(req: AuthenticatedRequest) {
  if (req.method === "GET") {
    try {
      const result = await getUserPersonas(req.user.userId);

      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        personas: result.personas,
      });
    } catch (error) {
      console.error("Error fetching personas:", error);
      return NextResponse.json(
        {
          success: false,
          error:
            error instanceof Error ? error.message : "Internal server error",
        },
        { status: 500 }
      );
    }
  }

  if (req.method === "DELETE") {
    try {
      const { searchParams } = new URL(req.url);
      const personaId = searchParams.get("id");

      if (!personaId) {
        return NextResponse.json(
          { success: false, error: "Persona ID is required" },
          { status: 400 }
        );
      }

      const result = await deletePersona(personaId, req.user.userId);

      if (!result.success) {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: result.error === "Persona not found" ? 404 : 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: "Persona deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting persona:", error);
      return NextResponse.json(
        {
          success: false,
          error:
            error instanceof Error ? error.message : "Internal server error",
        },
        { status: 500 }
      );
    }
  }

  return NextResponse.json(
    { success: false, error: "Method not allowed" },
    { status: 405 }
  );
}

export const GET = withAuth(handler);
export const DELETE = withAuth(handler);
