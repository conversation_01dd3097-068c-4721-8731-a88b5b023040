import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  createFluxFinetuneAPI,
  FluxFinetuneAPIError,
} from "@/src/lib/flux-finetune";
import { getPersona, updatePersona } from "@/src/actions/personaActions";
import cloudinary from "@/src/lib/cloudinary";

/**
 * Generate a detailed portrait prompt for finetuned model
 * Based on the professional prompt template but adapted for finetuned personas
 */
function generatePortraitPrompt(
  personaName: string,
  triggerWord: string
): string {
  return `${triggerWord}, a professional portrait photographer's work. Create a highly photorealistic, full-head portrait of ${personaName} using the trained model. Precision and natural appearance are essential.

**PORTRAIT SPECIFICATIONS**:
- Subject: ${personaName} (using ${triggerWord} trigger word)
- Style: Hyper-realistic photographic quality
- Framing: Include entire head, hair, neck, and upper chest — no part of the head or hair should be cropped or cut off
- Background: Clean, solid white
- Facial Accuracy: Preserve exact identity features — bone structure, eye shape, mouth, nose, and natural skin texture — true to the trained model
- Expression: Neutral or mild, natural expression
- Hair: Ensure realistic, natural texture and volume — avoid over-smoothing or AI-artifacts
- Clothing: Light pastel green, modern crew-neck T-shirt; ensure consistency in shade and fit
- Lighting: Neutral and even lighting (avoid harsh shadows or inconsistent highlights)

**CRITICAL FRAMING REQUIREMENTS**:
- Frame from mid-chest to top of head with ample headroom
- Face should occupy approximately 30-40% of the image height (not full frame)
- Ensure complete head visibility including hair/forehead
- Leave white space above the head (minimum 15% of image height)
- Show shoulders and upper chest area naturally

**Goal**: Generate a clean, centered, professionally composed head-and-shoulders portrait that looks indistinguishable from a real photo. This will be used as the digital identity representation for ${personaName}, so likeness accuracy is critical using the ${triggerWord} trained model.`;
}

async function handler(
  req: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  if (req.method !== "GET") {
    return NextResponse.json(
      { success: false, error: "Method not allowed" },
      { status: 405 }
    );
  }

  try {
    const { id: personaId } = await params;

    // Get persona from database
    const personaResult = await getPersona(personaId, req.user.userId);
    if (!personaResult.success) {
      return NextResponse.json(
        { success: false, error: personaResult.error },
        { status: 404 }
      );
    }

    const persona = personaResult.persona!;

    // If persona is already ready, return current status
    if (persona.status === "ready" || persona.status === "error") {
      return NextResponse.json({
        success: true,
        persona,
      });
    }

    // If no fine-tune ID, the fine-tuning submission failed
    if (!persona.finetuneId) {
      await updatePersona(personaId, req.user.userId, {
        status: "error",
        error:
          "Fine-tuning submission failed. Please try creating the persona again.",
      });

      return NextResponse.json({
        success: true,
        persona: {
          ...persona,
          status: "error",
          error:
            "Fine-tuning submission failed. Please try creating the persona again.",
        },
      });
    }

    try {
      // Check fine-tuning status
      console.log(
        `Checking fine-tune status for persona ${personaId}, finetuneId: ${persona.finetuneId}`
      );
      const fluxFinetuneAPI = createFluxFinetuneAPI();
      const status = await fluxFinetuneAPI.getFinetuneStatus(
        persona.finetuneId
      );

      console.log(`Persona ${personaId} fine-tune status:`, status.status);

      switch (status.status) {
        case "Ready":
        case "succeeded":
          // Fine-tuning is complete, generate persona image
          console.log(`Generating persona image for ${persona.name}...`);
          console.log(
            `Fine-tuning completed`
          );

          try {
            // CRITICAL FIX: Use fine-tuned model instead of regular Flux
            const fluxFinetuneAPI = createFluxFinetuneAPI();

            // Generate a portrait image using the fine-tuned model with detailed prompt
            console.log(
              `Using fine-tuned model with ID: ${persona.finetuneId}`
            );
            console.log(`Using trigger word: ${persona.triggerWord}`);

            // Generate detailed portrait prompt based on the professional template
            const detailedPrompt = generatePortraitPrompt(
              persona.name,
              persona.triggerWord
            );
            console.log(`Using detailed portrait prompt for ${persona.name}`);

            const inferenceResponse =
              await fluxFinetuneAPI.generateWithFinetune({
                finetune_id: persona.finetuneId,
                trigger_word: persona.triggerWord, // Pass the trigger word explicitly
                prompt: detailedPrompt, // Use the detailed professional prompt
                finetune_strength: 1.5,
                aspect_ratio: "1:1",
                output_format: "jpeg",
                safety_tolerance: 2,
              });

            // Poll for the result
            const imageUrl = await fluxFinetuneAPI.waitForInferenceCompletion(
              inferenceResponse.id
            );

            // Download and upload to Cloudinary
            const base64Image = await fluxFinetuneAPI.downloadImageAsBase64(
              imageUrl
            );
            const dataUri = `data:image/jpeg;base64,${base64Image}`;

            const uploadResult = await cloudinary.uploader.upload(dataUri, {
              folder: "personas",
              public_id: `persona_${personaId}`,
              overwrite: true,
              transformation: [
                { width: 512, height: 512, crop: "fill", gravity: "face" },
                { quality: "auto", fetch_format: "auto" },
              ],
            });

            // Update persona with generated image (using finetuneId for future use)
            const updatedPersona = await updatePersona(
              personaId,
              req.user.userId,
              {
                status: "ready",
                generatedImageUrl: uploadResult.secure_url,
              }
            );

            return NextResponse.json({
              success: true,
              persona: updatedPersona.persona,
            });
          } catch (imageError) {
            console.error("Error generating persona image:", imageError);

            // Update status to ready but without image
            const updatedPersona = await updatePersona(
              personaId,
              req.user.userId,
              {
                status: "ready",
                error: "Failed to generate persona image",
              }
            );

            return NextResponse.json({
              success: true,
              persona: updatedPersona.persona,
            });
          }

        case "Error":
        case "error":
          await updatePersona(personaId, req.user.userId, {
            status: "error",
            error: "Fine-tuning failed",
          });

          return NextResponse.json({
            success: true,
            persona: {
              ...persona,
              status: "error",
              error: "Fine-tuning failed",
            },
          });

        case "queued":
        case "running":
        case "Pending":
          // Still training
          return NextResponse.json({
            success: true,
            persona: {
              ...persona,
              status: "training",
            },
          });

        case "Request Moderated":
        case "Content Moderated":
          await updatePersona(personaId, req.user.userId, {
            status: "error",
            error: "Training images were moderated",
          });

          return NextResponse.json({
            success: true,
            persona: {
              ...persona,
              status: "error",
              error: "Training images were moderated",
            },
          });

        case "Task not found":
          await updatePersona(personaId, req.user.userId, {
            status: "error",
            error: "Fine-tuning task not found",
          });

          return NextResponse.json({
            success: true,
            persona: {
              ...persona,
              status: "error",
              error: "Fine-tuning task not found",
            },
          });

        case "Pending":
        default:
          // Still training, return current status
          return NextResponse.json({
            success: true,
            persona: {
              ...persona,
              status: "training",
            },
          });
      }
    } catch (statusError) {
      console.error("Error checking fine-tune status:", statusError);

      if (
        statusError instanceof FluxFinetuneAPIError &&
        statusError.status === 404
      ) {
        console.log(
          `Fine-tune ID ${persona.finetuneId} not found (404). This might be normal if training just started or if there's a delay.`
        );

        // Don't mark as error immediately - 404 might be temporary
        // Return training status to allow continued polling
        return NextResponse.json({
          success: true,
          persona: {
            ...persona,
            status: "training",
          },
        });
      }

      // Don't update status for temporary errors
      return NextResponse.json({
        success: true,
        persona,
      });
    }
  } catch (error) {
    console.error("Error checking persona status:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

export const GET = withAuth(handler);
