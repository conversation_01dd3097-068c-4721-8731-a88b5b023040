import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { createFluxFinetuneAPI } from "@/src/lib/flux-finetune";
import { createFluxAPI } from "@/src/lib/flux";
import { getUserPersonas, updatePersona } from "@/src/actions/personaActions";
import cloudinary from "@/src/lib/cloudinary";

/**
 * Generate a detailed portrait prompt for finetuned model
 * Based on the professional prompt template but adapted for finetuned personas
 */
function generatePortraitPrompt(
  personaName: string,
  triggerWord: string
): string {
  return `${triggerWord}, a professional portrait photographer's work. Create a highly photorealistic, full-head portrait of ${personaName} using the trained model. Precision and natural appearance are essential.

**PORTRAIT SPECIFICATIONS**:
- Subject: ${personaName} (using ${triggerWord} trigger word)
- Style: Hyper-realistic photographic quality
- Framing: Include entire head, hair, neck, and upper chest — no part of the head or hair should be cropped or cut off
- Background: Clean, solid white
- Facial Accuracy: Preserve exact identity features — bone structure, eye shape, mouth, nose, and natural skin texture — true to the trained model
- Expression: Neutral or mild, natural expression
- Hair: Ensure realistic, natural texture and volume — avoid over-smoothing or AI-artifacts
- Clothing: Light pastel green, modern crew-neck T-shirt; ensure consistency in shade and fit
- Lighting: Neutral and even lighting (avoid harsh shadows or inconsistent highlights)

**CRITICAL FRAMING REQUIREMENTS**:
- Frame from mid-chest to top of head with ample headroom
- Face should occupy approximately 30-40% of the image height (not full frame)
- Ensure complete head visibility including hair/forehead
- Leave white space above the head (minimum 15% of image height)
- Show shoulders and upper chest area naturally

**Goal**: Generate a clean, centered, professionally composed head-and-shoulders portrait that looks indistinguishable from a real photo. This will be used as the digital identity representation for ${personaName}, so likeness accuracy is critical using the ${triggerWord} trained model.`;
}

async function handler(req: AuthenticatedRequest) {
  if (req.method !== "POST") {
    return NextResponse.json(
      { success: false, error: "Method not allowed" },
      { status: 405 }
    );
  }

  try {
    console.log("Starting persona sync for user:", req.user.userId);

    // Get all user personas
    const personasResult = await getUserPersonas(req.user.userId);
    if (!personasResult.success) {
      return NextResponse.json(
        { success: false, error: personasResult.error },
        { status: 500 }
      );
    }

    const personas = personasResult.personas!;
    const fluxFinetuneAPI = createFluxFinetuneAPI();
    const fluxAPI = createFluxAPI();

    let syncedCount = 0;

    for (const persona of personas) {
      if (!persona.finetuneId) {
        console.log(`Skipping persona ${persona.name} - no finetune ID`);
        continue;
      }

      try {
        console.log(
          `Checking status for persona ${persona.name} (${persona.finetuneId})`
        );

        // Check fine-tuning status
        const status = await fluxFinetuneAPI.getFinetuneStatus(
          persona.finetuneId
        );
        console.log(`Status for ${persona.name}:`, status.status);

        if (status.status === "Ready" && persona.status !== "ready") {
          console.log(`Updating persona ${persona.name} to ready status`);

          // Generate persona image if not exists
          let generatedImageUrl = persona.generatedImageUrl;

          if (!generatedImageUrl) {
            try {
              console.log(`Generating persona image for ${persona.name}...`);

              // CRITICAL FIX: Use fine-tuned model instead of regular Flux
              const fluxFinetuneAPI = createFluxFinetuneAPI();

              console.log(
                `Using fine-tuned model with ID: ${persona.finetuneId}`
              );
              console.log(`Using trigger word: ${persona.triggerWord}`);

              // Generate detailed portrait prompt based on the professional template
              const detailedPrompt = generatePortraitPrompt(
                persona.name,
                persona.triggerWord
              );
              console.log(`Using detailed portrait prompt for ${persona.name}`);

              const inferenceResponse =
                await fluxFinetuneAPI.generateWithFinetune({
                  finetune_id: persona.finetuneId,
                  trigger_word: persona.triggerWord, // Pass the trigger word explicitly
                  prompt: detailedPrompt, // Use the detailed professional prompt
                  finetune_strength: 1.5,
                  aspect_ratio: "1:1",
                  output_format: "jpeg",
                  safety_tolerance: 2,
                });

              // Poll for the result
              const imageUrl = await fluxFinetuneAPI.waitForInferenceCompletion(
                inferenceResponse.id
              );

              // Download and upload to Cloudinary
              const base64Image = await fluxFinetuneAPI.downloadImageAsBase64(
                imageUrl
              );
              const dataUri = `data:image/jpeg;base64,${base64Image}`;

              const uploadResult = await cloudinary.uploader.upload(dataUri, {
                folder: "personas",
                public_id: `persona_${persona.id}`,
                overwrite: true,
                transformation: [
                  { width: 512, height: 512, crop: "fill", gravity: "face" },
                  { quality: "auto", fetch_format: "auto" },
                ],
              });

              generatedImageUrl = uploadResult.secure_url;
              console.log(
                `Generated image for ${persona.name}: ${generatedImageUrl}`
              );
            } catch (imageError) {
              console.error(
                `Error generating image for ${persona.name}:`,
                imageError
              );
            }
          }

          // Update persona status
          await updatePersona(persona.id, req.user.userId, {
            status: "ready",
            generatedImageUrl: generatedImageUrl || undefined,
            error: undefined,
          });

          syncedCount++;
        } else if (status.status === "Error" && persona.status !== "error") {
          console.log(`Updating persona ${persona.name} to error status`);

          await updatePersona(persona.id, req.user.userId, {
            status: "error",
            error: "Fine-tuning failed",
          });

          syncedCount++;
        }
      } catch (statusError) {
        console.error(
          `Error checking status for persona ${persona.name}:`,
          statusError
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: `Synced ${syncedCount} personas`,
      syncedCount,
    });
  } catch (error) {
    console.error("Error syncing personas:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal server error",
      },
      { status: 500 }
    );
  }
}

export const POST = withAuth(handler);
