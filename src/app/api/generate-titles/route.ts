import { NextRequest, NextResponse } from "next/server";
import { openai } from "@/src/lib/openai";
import { getCurrentDateContext } from "@/src/utils/date";

export async function POST(request: NextRequest) {
  try {
    const { prompt } = await request.json();

    if (!prompt || typeof prompt !== "string") {
      return NextResponse.json(
        { success: false, error: "Prompt is required" },
        { status: 400 }
      );
    }

    // Get current date context for AI awareness
    const dateContext = getCurrentDateContext();

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are an expert YouTube title strategist who has helped creators achieve millions of views. Your specialty is crafting titles that maximize click-through rates (CTR) and drive viral engagement.

Your task: Generate 3 compelling YouTube video titles based on the content description provided.

CURRENT DATE CONTEXT (for reference when relevant):
- Today's Date: ${dateContext.formattedDate}
- Current Year: ${dateContext.year}
- Current Month: ${dateContext.month}
- Current Season: ${dateContext.season}
- Day of Week: ${dateContext.dayOfWeek}

Note: Only include dates/years in titles when contextually relevant to the content. Don't force date references if they don't naturally fit the topic.

TITLE STRATEGY GUIDELINES:
- Use psychological triggers: curiosity gaps, urgency, exclusivity, controversy
- Include power words: "Secret", "Shocking", "Ultimate", "Exposed", "Revealed"
- Leverage numbers and lists when relevant ("5 Ways", "Top 10", "#1 Mistake")
- Create emotional hooks that make viewers feel they MUST watch
- Keep titles under 60 characters for mobile optimization
- Use ALL CAPS sparingly for emphasis on key words
- Include trending keywords when relevant
- When appropriate, reference current year/season/time for relevance and urgency

STYLE VARIATIONS:
- "seo": Keyword-rich, search-optimized, includes trending terms
- "dramatic": High-energy, emotional impact, urgency-driven
- "curiosity": Mystery-driven, question-based, intrigue-focused
- "revealed": Exposé style, secrets uncovered, behind-the-scenes
- "snappy": Short, punchy, memorable, easy to share
- "emphasize": Bold, attention-grabbing, uses caps and symbols
- "clickbait": High-energy, curiosity-driven, uses emotional triggers
- "professional": Authoritative, educational, trustworthy tone
- "creative": Unique angles, wordplay, unexpected perspectives

RETURN FORMAT: JSON array with exactly this structure:
[
  {"id": "1", "text": "This SECRET Technique Will BLOW Your Mind!", "style": "clickbait"},
  {"id": "2", "text": "Complete Guide to [Topic]: Expert Analysis", "style": "professional"},
  {"id": "3", "text": "What Happens When [Unexpected Scenario]?", "style": "curiosity"}
]

Ensure each title is optimized for YouTube's algorithm and human psychology. Vary the styles across the 3 titles to give diverse options.`,
        },
        {
          role: "user",
          content: `Create YouTube video titles for: ${prompt}`,
        },
      ],
      temperature: 0.9,
      max_tokens: 800,
    });

    const content = completion.choices[0]?.message?.content;
    if (!content) {
      throw new Error("No content generated");
    }

    // Parse the JSON response
    let titles;
    try {
      titles = JSON.parse(content);
    } catch (parseError) {
      // If JSON parsing fails, try to extract titles from text
      const lines = content.split("\n").filter((line) => line.trim());
      titles = lines.slice(0, 3).map((line, index) => ({
        id: (index + 1).toString(),
        text: line.replace(/^\d+\.\s*/, "").replace(/^["']|["']$/g, ""),
        style:
          index % 3 === 0
            ? "dramatic"
            : index % 3 === 1
            ? "professional"
            : "curiosity",
      }));
    }

    // Ensure we have valid titles
    if (!Array.isArray(titles) || titles.length === 0) {
      throw new Error("Invalid titles format");
    }

    // Validate and clean up titles
    const validTitles = titles.slice(0, 3).map((title, index) => ({
      id: title.id || (index + 1).toString(),
      text: title.text || `Generated Title ${index + 1}`,
      style: [
        "seo",
        "dramatic",
        "curiosity",
        "revealed",
        "snappy",
        "emphasize",
        "clickbait",
        "professional",
        "creative",
      ].includes(title.style)
        ? title.style
        : ["dramatic", "professional", "curiosity"][index % 3],
    }));

    return NextResponse.json({
      success: true,
      titles: validTitles,
    });
  } catch (error: any) {
    console.error("Error generating titles:", error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to generate titles",
      },
      { status: 500 }
    );
  }
}
