import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { getUserGeneratedThumbnails } from "@/src/actions/generatedThumbnailAction";

export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // User is automatically authenticated via withAuth wrapper
    const { userId } = req.user;

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";

    // Use Drizzle action for paginated thumbnails
    const { thumbnails, pagination } = await getUserGeneratedThumbnails(
      userId,
      {
        page,
        limit,
        search,
        sortBy,
        sortOrder,
      }
    );

    return NextResponse.json(
      {
        success: true,
        data: {
          thumbnails,
          pagination,
        },
      },
      {
        headers: {
          "Cache-Control": "private, max-age=30, stale-while-revalidate=60",
          Vary: "Authorization",
        },
      }
    );
  } catch (error) {
    console.error("Error fetching thumbnails:", error);

    // Check if it's an authentication error
    if (error instanceof Error && error.message === "Unauthorized") {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to fetch thumbnails" },
      { status: 500 }
    );
  }
});
