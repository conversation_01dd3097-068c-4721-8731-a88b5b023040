import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  getGeneratedThumbnail,
  deleteGeneratedThumbnail,
} from "@/src/actions/generatedThumbnailAction";
import cloudinary from "@/src/lib/cloudinary";

export const DELETE = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      // User is automatically authenticated via withAuth wrapper
      const { userId } = req.user;
      const { id } = await params;

      // Find the thumbnail to delete
      const thumbnail = await getGeneratedThumbnail(id);
      if (!thumbnail || thumbnail.userId !== userId) {
        return NextResponse.json(
          { success: false, error: "Thumbnail not found or access denied" },
          { status: 404 }
        );
      }

      // Extract public_id from Cloudinary URL for deletion
      const urlParts = thumbnail.imageUrl.split("/");
      const fileWithExtension = urlParts[urlParts.length - 1];
      const publicId = `thumbnails/${fileWithExtension.split(".")[0]}`;

      // Delete from Cloudinary
      try {
        await cloudinary.uploader.destroy(publicId);
      } catch (cloudinaryError) {
        console.warn("Failed to delete from Cloudinary:", cloudinaryError);
        // Continue with database deletion even if Cloudinary fails
      }

      // Delete from database
      await deleteGeneratedThumbnail(id);

      return NextResponse.json({
        success: true,
        message: "Thumbnail deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting thumbnail:", error);
      // Check if it's an authentication error
      if (error instanceof Error && error.message === "Unauthorized") {
        return NextResponse.json(
          { success: false, error: "Authentication required" },
          { status: 401 }
        );
      }
      return NextResponse.json(
        { success: false, error: "Failed to delete thumbnail" },
        { status: 500 }
      );
    }
  }
);
