import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { rembg } from "@remove-background-ai/rembg.js";
import cloudinary from "@/src/lib/cloudinary";
import { createBackgroundRemoval } from "@/src/actions/backgroundRemovalAction";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 300; // 5 minutes for background removal

interface RemoveBackgroundRequest {
  imageUrl: string;
  format?: "png" | "jpg" | "webp";
  returnBase64?: boolean;
}

interface AxiosProgressEvent {
  loaded: number;
  total?: number;
  progress?: number;
  bytes?: number;
  rate?: number;
  estimated?: number;
  upload?: boolean;
  download?: boolean;
}

/**
 * POST /api/remove-background
 * Remove background from an image using REMBG API
 */
export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const {
      imageUrl,
      format = "png",
      returnBase64 = true,
    } = (await req.json()) as RemoveBackgroundRequest;

    if (!imageUrl || typeof imageUrl !== "string") {
      return NextResponse.json(
        {
          success: false,
          error: "Image URL is required",
        },
        { status: 400 }
      );
    }

    // Validate format
    if (!["png", "jpg", "webp"].includes(format)) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid format. Supported formats: png, jpg, webp",
        },
        { status: 400 }
      );
    }

    // Get API key from environment
    const apiKey = process.env.REMBG_API_KEY;
    if (!apiKey) {
      console.error("REMBG_API_KEY not found in environment variables");
      return NextResponse.json(
        {
          success: false,
          error: "Background removal service is not configured",
        },
        { status: 500 }
      );
    }

    // Convert image URL to buffer for REMBG
    let imageBuffer: Buffer;
    let contentType: string | null = null;
    try {
      const imageResponse = await fetch(imageUrl);
      if (!imageResponse.ok) {
        throw new Error(`Failed to fetch image: ${imageResponse.statusText}`);
      }

      // Get content type to validate image format
      contentType = imageResponse.headers.get("content-type");
      console.log("Detected content type:", contentType);

      // Validate that it's actually an image
      if (!contentType || !contentType.startsWith("image/")) {
        console.log("Invalid content type - not an image:", contentType);
        return NextResponse.json(
          {
            success: false,
            error:
              "The uploaded file is not a valid image. Please upload a JPG, PNG, or WebP image.",
          },
          { status: 400 }
        );
      }

      // Check for supported image formats
      const supportedFormats = [
        "image/jpeg",
        "image/jpg",
        "image/pjpeg", // Progressive JPEG
        "image/png",
        "image/webp",
      ];

      const normalizedContentType = contentType.toLowerCase();
      if (!supportedFormats.includes(normalizedContentType)) {
        console.log(`Unsupported content type detected: ${contentType}`);
        return NextResponse.json(
          {
            success: false,
            error: `Unsupported image format: ${contentType}. Please upload a JPG, PNG, or WebP image.`,
          },
          { status: 400 }
        );
      }

      const arrayBuffer = await imageResponse.arrayBuffer();
      imageBuffer = Buffer.from(arrayBuffer);

      // Additional validation: check if buffer is too small (likely not a real image)
      if (imageBuffer.length < 100) {
        return NextResponse.json(
          {
            success: false,
            error:
              "The uploaded file appears to be corrupted or too small. Please upload a valid image.",
          },
          { status: 400 }
        );
      }

      // Check if image is too large (increased limit for 4K processing)
      const maxSizeBytes = 25 * 1024 * 1024; // 25MB for 4K images
      if (imageBuffer.length > maxSizeBytes) {
        console.log(
          `Image too large: ${imageBuffer.length} bytes (max: ${maxSizeBytes})`
        );
        return NextResponse.json(
          {
            success: false,
            error:
              "The image is too large for processing. Please try with a smaller image (max 25MB for 4K processing).",
          },
          { status: 413 }
        );
      }

      console.log(
        `Image validation passed. Size: ${imageBuffer.length} bytes, Content-Type: ${contentType}`
      );
    } catch (error) {
      console.error("Error fetching image:", error);

      // Provide more specific error messages
      if (error instanceof Error) {
        if (error.message.includes("fetch")) {
          return NextResponse.json(
            {
              success: false,
              error:
                "Unable to access the image. Please check the image URL or try uploading a different image.",
            },
            { status: 400 }
          );
        }
      }

      return NextResponse.json(
        {
          success: false,
          error:
            "Failed to process the uploaded image. Please try again with a different image.",
        },
        { status: 400 }
      );
    }

    // Progress tracking variables
    let uploadProgress = 0;
    let downloadProgress = 0;

    // Progress callbacks
    const onUploadProgress = (event: AxiosProgressEvent) => {
      uploadProgress =
        event.progress || (event.total ? event.loaded / event.total : 0);
      console.log(`Upload progress: ${Math.round(uploadProgress * 100)}%`);
    };

    const onDownloadProgress = (event: AxiosProgressEvent) => {
      downloadProgress =
        event.progress || (event.total ? event.loaded / event.total : 0);
      console.log(`Download progress: ${Math.round(downloadProgress * 100)}%`);
    };

    try {
      console.log("Processing with 4K resolution (3840x2160)");

      // Call REMBG API with 4K resolution
      const result = await rembg({
        apiKey,
        inputImage: imageBuffer,
        onUploadProgress,
        onDownloadProgress,
        options: {
          returnBase64,
          w: 3840, // 4K width
          h: 2160, // 4K height
          exact_resize: false, // Maintain aspect ratio
        },
      });

      // Read the output file and convert to base64 if needed
      let processedImageData: string;

      console.log("REMBG result type:", typeof result);
      console.log("REMBG result:", result);

      // REMBG returns an object with base64Image property
      if (result && typeof result === "object" && "base64Image" in result) {
        processedImageData = result.base64Image as string;

        // Clean up the temporary file if cleanup function exists
        if ("cleanup" in result && typeof result.cleanup === "function") {
          result.cleanup();
        }
      } else {
        console.error("Unexpected REMBG result format:", result);
        throw new Error("Unexpected response format from REMBG API");
      }

      // Upload to Cloudinary
      console.log("Uploading processed image to Cloudinary...");
      const cloudinaryResponse = await cloudinary.uploader.upload(
        processedImageData,
        {
          folder: "background-removals",
          resource_type: "image",
          format: format === "jpg" ? "jpg" : format,
          quality: "auto:best",
        }
      );

      console.log(
        "Cloudinary upload successful:",
        cloudinaryResponse.secure_url
      );

      // Save to database
      console.log("Saving background removal to database...");
      const savedBackgroundRemoval = await createBackgroundRemoval({
        userId: req.user.userId,
        imageUrl: cloudinaryResponse.secure_url,
      });

      console.log("Database save successful:", savedBackgroundRemoval.id);

      return NextResponse.json({
        success: true,
        processedImage: processedImageData,
        cloudinaryUrl: cloudinaryResponse.secure_url,
        backgroundRemovalId: savedBackgroundRemoval.id,
        format,
        progress: {
          upload: uploadProgress,
          download: downloadProgress,
        },
      });
    } catch (rembgError) {
      console.error("REMBG API error:", rembgError);
      console.error("REMBG error type:", typeof rembgError);
      console.error(
        "REMBG error message:",
        rembgError instanceof Error ? rembgError.message : String(rembgError)
      );

      // Handle specific REMBG errors
      let errorMessage = "Background removal failed";
      let statusCode = 500;

      if (rembgError instanceof Error) {
        const errorMsg = rembgError.message.toLowerCase();
        console.log("Processing REMBG error message:", errorMsg);

        if (errorMsg.includes("api key") || errorMsg.includes("unauthorized")) {
          errorMessage =
            "Background removal service is not properly configured. Please try again later.";
          statusCode = 503;
        } else if (
          errorMsg.includes("quota") ||
          errorMsg.includes("limit") ||
          errorMsg.includes("rate")
        ) {
          errorMessage =
            "Background removal service is temporarily unavailable due to high demand. Please try again in a few minutes.";
          statusCode = 429;
        } else if (
          errorMsg.includes("timeout") ||
          errorMsg.includes("timed out")
        ) {
          errorMessage =
            "Background removal is taking longer than expected. Please try again with a smaller image.";
          statusCode = 408;
        } else if (
          errorMsg.includes("unsupported") ||
          errorMsg.includes("invalid") ||
          errorMsg.includes("format") ||
          errorMsg.includes("not supported") ||
          errorMsg.includes("jpeg") ||
          errorMsg.includes("jpg") ||
          errorMsg.includes("png") ||
          errorMsg.includes("webp")
        ) {
          errorMessage =
            "The image format is not supported for background removal. Please try with a JPG, PNG, or WebP image.";
          statusCode = 400;
        } else if (
          errorMsg.includes("too large") ||
          errorMsg.includes("size") ||
          errorMsg.includes("file size")
        ) {
          errorMessage =
            "The image is too large for processing. Please try with a smaller image (max 25MB for 4K processing).";
          statusCode = 413;
        } else if (
          errorMsg.includes("network") ||
          errorMsg.includes("connection")
        ) {
          errorMessage =
            "Network error occurred during background removal. Please check your connection and try again.";
          statusCode = 503;
        } else if (
          errorMsg.includes("500") ||
          errorMsg.includes("internal server error") ||
          errorMsg.includes("server error")
        ) {
          // For 500 errors, it might be an image format issue even if MIME type is correct
          errorMessage =
            "Unable to process this image. The image format may not be fully supported or the file may be corrupted. Please try with a different JPEG, PNG, or WebP image.";
          statusCode = 400;
        } else {
          // For any other error, provide a user-friendly message without exposing technical details
          console.log("Using fallback error message for:", errorMsg);
          errorMessage =
            "Unable to remove background from this image. Please try with a different image or try again later.";
        }
      } else {
        console.log("Non-Error object received:", rembgError);
        errorMessage =
          "Unable to remove background from this image. Please try with a different image or try again later.";
      }

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
        },
        { status: statusCode }
      );
    }
  } catch (error) {
    console.error("Error in remove-background API:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
});
