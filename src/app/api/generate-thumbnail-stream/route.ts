import { createFluxAPI, FluxAPIError } from "@/src/lib/flux";
import { createFluxFinetuneAPI } from "@/src/lib/flux-finetune";
import cloudinary from "@/src/lib/cloudinary";
import {
  withAuthStreaming,
  AuthenticatedRequest,
} from "@/src/lib/api-auth-wrapper";
import { NextResponse } from "next/server";
import { createGeneratedThumbnail } from "@/src/actions/generatedThumbnailAction";
import { getPersona } from "@/src/actions/personaActions";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 300;

/**
 * Server-Sent Events endpoint for real-time thumbnail generation progress
 * Provides streaming progress updates to the frontend
 */
export const POST = withAuthStreaming(async (req: AuthenticatedRequest) => {
  const { userId } = req.user;

  try {
    const { prompt, persona, title, tags } = (await req.json()) as {
      prompt: string;
      persona?: {
        id: string;
        name: string;
        generatedImageUrl?: string;
        finetuneId?: string;
      };
      title?: string;
      tags?: string[];
    };

    if (!prompt || typeof prompt !== "string") {
      return NextResponse.json(
        { success: false, error: "Invalid or missing prompt" },
        { status: 400 }
      );
    }

    // Create a readable stream for Server-Sent Events
    const stream = new ReadableStream({
      start(controller) {
        const encoder = new TextEncoder();

        const sendProgress = (
          progress: number,
          stage: string,
          message?: string
        ) => {
          const data = JSON.stringify({ progress, stage, message });
          controller.enqueue(encoder.encode(`data: ${data}\n\n`));
        };

        const sendError = (error: string) => {
          const data = JSON.stringify({ error });
          controller.enqueue(encoder.encode(`data: ${data}\n\n`));
          controller.close();
        };

        const sendComplete = (result: any) => {
          const data = JSON.stringify({ ...result, complete: true });
          controller.enqueue(encoder.encode(`data: ${data}\n\n`));
          controller.close();
        };

        // Start the generation process
        (async () => {
          try {
            sendProgress(5, "processing", "Initializing generation...");

            // Check if we should use fine-tuned model or regular generation
            let useFinetunedModel = false;
            let personaData = null;

            if (persona && persona.finetuneId) {
              try {
                const personaResult = await getPersona(persona.id, userId);
                if (
                  personaResult.success &&
                  personaResult.persona?.status === "ready"
                ) {
                  useFinetunedModel = true;
                  personaData = personaResult.persona;
                  sendProgress(
                    10,
                    "processing",
                    `Using fine-tuned model for ${persona.name}`
                  );
                }
              } catch (error) {
                console.log(
                  "Failed to get persona data, falling back to regular generation:",
                  error
                );
              }
            }

            let enhancedPrompt: string;
            let imageUrl: string;

            sendProgress(15, "generating", "Starting image generation...");

            if (useFinetunedModel && personaData) {
              // Use fine-tuned model with progress tracking
              enhancedPrompt = `${personaData.triggerWord}, a vibrant YouTube thumbnail of ${personaData.triggerWord} ${prompt} — 16:9, dramatic lighting, photorealistic`;

              const fluxFinetuneAPI = createFluxFinetuneAPI();

              // Note: Fine-tune API doesn't support progress callbacks yet
              // This is a limitation we'll document
              sendProgress(
                50,
                "generating",
                "Generating with persona model..."
              );

              imageUrl = await fluxFinetuneAPI.testGenerateWithModel(
                personaData.finetuneId!,
                `a vibrant YouTube thumbnail of ${personaData.triggerWord} ${prompt} — dramatic lighting, photorealistic`,
                personaData.triggerWord,
                {
                  aspect_ratio: "16:9",
                  width: 1280,
                  height: 720,
                }
              );
            } else {
              // Use regular Flux API with progress tracking
              enhancedPrompt = `You are an expert visual designer specializing in high-click-through-rate promotional graphics. Your mission is to generate a single, clean, photorealistic digital artwork (16:9) for use on YouTube as a thumbnail.

DESIGN BRIEF: Generate an image based on this core concept: ${prompt}`;

              if (persona && persona.generatedImageUrl) {
                enhancedPrompt += `

PERSONA INTEGRATION (Strict Preservation):
The main subject of the image must be the person from the reference image (${persona.name}). The character's identity is paramount.
- **Action:** Integrate "the person from the reference image" into the scene. Do NOT use vague terms like "her" or "the person."
- **Preservation:** You MUST preserve their distinctive appearance. This includes maintaining the **exact same facial features, hairstyle, expression, and eye color** from the reference image.
- **Modification:** Only the clothing and the context/environment should be adapted to fit the scene. Do not "transform" the person; "place" the person within the new setting.
- **Reference URL:** ${persona.generatedImageUrl}`;
              }

              enhancedPrompt += `
    
DESIGN SPECIFICATIONS & COMPOSITION:
- **Style:** Hyper-realistic, 8K resolution, cinematic quality, rendered with sharp focus, intricate details, and deep shadows. The final image should feel like a photorealistic digital painting.
- **Composition:** Use dynamic camera angles and the rule of thirds. The main subject must be the clear focal point. Ensure strong, clean separation between the subject and the background to maximize clarity at small sizes.
- **Color:** Use a bold, vibrant, and high-contrast color palette to grab attention.
- **Lighting:** Employ dramatic, professional studio-style lighting to create depth and highlight the main subject.
- **Mood:** The energy should be exciting, intriguing, and convey a strong, clear emotion.

TYPOGRAPHY & TEXT (CRITICAL):
- **Text Accuracy:** You must render any text exactly as specified in the user's prompt. Do not add, omit, misspell, or change any words.
- **Literal Interpretation:** If the prompt specifies text on an object (e.g., "text on a sign," "words on a calendar"), you MUST render the object with the specified text on it. Do not simplify or replace the concept (e.g., do not turn it into a neon sign unless explicitly asked).
- **Readability & Style:** Text must be bold, clean, and highly legible at a small scale. Use strong, modern, sans-serif fonts.
- **Visual Separation:** To make text 'pop', apply a contrasting outline (stroke) AND/OR a subtle drop shadow to all text. This is crucial for separating it from the background.
- **Strategic Placement:** Position text to enhance the composition. Place it in areas of negative space and avoid covering the main subject's face or other crucial visual elements.

VISUAL COHERENCE & STORYTELLING:
- **Integrated Elements:** All visual elements, including symbols or effects, must be physically integrated into the scene. They should appear to be part of the environment, not disconnected floating overlays or clip art. For example, an effect should emit *from* an object.
- **Consistent Scale:** Ensure all elements are appropriately scaled. A symbol like a heart should be the size of a person's hand, not a tiny icon.
- **Proportional Relationships:** Maintain proportional relationships between elements. If a person is holding an object, the object should be sized appropriately for the person's hand.

- **Environmental Detail:** Enrich the background and foreground with details that support the core story. If the theme is chaos, the environment should look chaotic. If the theme is discovery, the environment should reflect that.
- **Physical Interaction:** Ensure the subject's interaction with objects is realistic and direct. 
  
ABSOLUTE DON'TS (NON-NEGOTIABLE RULE):
- **NO UI ELEMENTS:** Under no circumstances should you include any part of a user interface. This means: NO play buttons, NO video progress bars, NO timeline scrubs, NO volume icons, NO channel logos, and NO subscription buttons.
- **CLEAN ARTWORK ONLY:** The output must be a pure digital artwork. It must NOT resemble a screenshot of a video player, website, or any application.
- **NO LOW QUALITY:** Avoid blurry, pixelated, or noisy artifacts. The image must be sharp, clean, and professional.

CREATIVE EXECUTION:
Execute this design with the precision of an expert. The final image must be visually stunning and so compelling it's impossible to scroll past without clicking.`;

              const fluxAPI = createFluxAPI();

              // Use the new progress-enabled method
              imageUrl = await fluxAPI.generateImageWithProgress(
                {
                  prompt: enhancedPrompt,
                  aspect_ratio: "16:9",
                  output_format: "jpeg",
                  safety_tolerance: 2,
                },
                (progress, stage) => {
                  sendProgress(
                    15 + progress * 0.65,
                    stage,
                    `Generation ${Math.round(progress)}% complete`
                  );
                }
              );
            }

            sendProgress(85, "uploading", "Uploading to cloud storage...");

            // Download and upload to Cloudinary
            let base64Image: string;
            if (useFinetunedModel) {
              const fluxFinetuneAPI = createFluxFinetuneAPI();
              base64Image = await fluxFinetuneAPI.downloadImageAsBase64(
                imageUrl
              );
            } else {
              const fluxAPI = createFluxAPI();
              base64Image = await fluxAPI.downloadImageAsBase64(imageUrl);
            }

            sendProgress(90, "uploading", "Processing final image...");

            const dataUri = `data:image/jpeg;base64,${base64Image}`;
            const uploadResult = await cloudinary.uploader.upload(dataUri, {
              folder: "thumbnails",
              public_id: `thumb_${Date.now()}`,
              width: 1280,
              height: 720,
              crop: "fill",
              quality: "auto:best",
              format: "jpg",
              flags: "progressive",
            });

            sendProgress(95, "uploading", "Saving to database...");

            // Save to database
            const savedThumbnail = await createGeneratedThumbnail({
              userId,
              imageUrl: uploadResult.secure_url,
              prompt,
              title: title || undefined,
              tags: tags || [],
            });

            // Send completion
            sendComplete({
              success: true,
              imageUrl: uploadResult.secure_url,
              responseId: savedThumbnail.id,
              thumbnailId: savedThumbnail.id,
            });
          } catch (error) {
            console.error("Error in streaming generation:", error);

            if (error instanceof FluxAPIError) {
              if (error.message.includes("moderated")) {
                sendError(
                  "Content was blocked by safety filters. Try rephrasing your prompt to focus on positive, exciting adventures rather than distressing situations."
                );
              } else if (error.status === 408) {
                sendError(
                  "Image generation timed out. Please try again with a simpler prompt."
                );
              } else if (error.status === 429) {
                sendError(
                  "Too many requests. Please wait a moment before trying again."
                );
              } else if (error.status === 402) {
                sendError(
                  "Insufficient credits. Please check your account balance."
                );
              } else {
                sendError(error.message);
              }
            } else {
              sendError(
                error instanceof Error ? error.message : "Internal Server Error"
              );
            }
          }
        })();
      },
    });

    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  } catch (error) {
    console.error("Error setting up streaming generation:", error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Internal Server Error",
      },
      { status: 500 }
    );
  }
});
