import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { createFluxFinetuneAPI } from "@/src/lib/flux-finetune";

async function handler(req: AuthenticatedRequest) {
  if (req.method === "GET") {
    try {
      const fluxFinetuneAPI = createFluxFinetuneAPI();

      console.log("Fetching all fine-tunes for user...");
      const result = await fluxFinetuneAPI.listFinetunes();

      console.log(`Found ${result.finetunes?.length || 0} fine-tunes`);

      return NextResponse.json({
        success: true,
        finetunes: result.finetunes || [],
      });
    } catch (error) {
      console.error("Error fetching fine-tunes:", error);
      return NextResponse.json(
        {
          success: false,
          error:
            error instanceof Error
              ? error.message
              : "Failed to fetch fine-tunes",
        },
        { status: 500 }
      );
    }
  }

  if (req.method === "POST") {
    try {
      const body = await req.json();
      const { testEndpoint, withDetails } = body;

      if (!testEndpoint) {
        return NextResponse.json(
          { success: false, error: "testEndpoint is required" },
          { status: 400 }
        );
      }

      console.log(`🔍 Testing endpoint: ${testEndpoint}`);

      const apiKey = process.env.BFL_API_KEY;
      if (!apiKey) {
        throw new Error("BFL_API_KEY not found in environment variables");
      }

      const response = await fetch(testEndpoint, {
        headers: {
          "x-key": apiKey,
        },
      });

      const data = await response.json();

      console.log(
        `📦 Response from ${testEndpoint}:`,
        JSON.stringify(data, null, 2)
      );

      return NextResponse.json({
        success: response.ok,
        status: response.status,
        endpoint: testEndpoint,
        data: response.ok ? data : { error: data },
      });
    } catch (error) {
      console.error("Error testing endpoint:", error);
      return NextResponse.json(
        {
          success: false,
          error:
            error instanceof Error ? error.message : "Failed to test endpoint",
        },
        { status: 500 }
      );
    }
  }

  return NextResponse.json(
    { success: false, error: "Method not allowed" },
    { status: 405 }
  );
}

export const GET = withAuth(handler);
export const POST = withAuth(handler);
