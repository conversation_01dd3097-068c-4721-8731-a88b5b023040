import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  createTitleChatSession,
  getUserTitleChatSessions,
  generateSessionTitle,
} from "@/src/actions/titleChatSessionAction";

export const dynamic = "force-dynamic";

/**
 * GET /api/title-sessions
 * Get all title chat sessions for the authenticated user
 */
export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const { userId } = req.user;

    const sessions = await getUserTitleChatSessions(userId);

    return NextResponse.json({
      success: true,
      sessions,
    });
  } catch (error) {
    console.error("Error fetching title sessions:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch title sessions",
      },
      { status: 500 }
    );
  }
});

/**
 * POST /api/title-sessions
 * Create a new title chat session
 */
export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const { userId } = req.user;
    const { firstPrompt, title } = await req.json();

    if (!firstPrompt || typeof firstPrompt !== "string") {
      return NextResponse.json(
        {
          success: false,
          error: "First prompt is required",
        },
        { status: 400 }
      );
    }

    // Generate title if not provided
    const sessionTitle = title || (await generateSessionTitle(firstPrompt));

    const session = await createTitleChatSession({
      userId,
      firstPrompt,
      title: sessionTitle,
    });

    return NextResponse.json({
      success: true,
      session,
    });
  } catch (error) {
    console.error("Error creating title session:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to create title session",
      },
      { status: 500 }
    );
  }
});
