import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  createTitleChatMessage,
  getTitleChatSession,
  getSessionMessages,
} from "@/src/actions/titleChatSessionAction";

export const dynamic = "force-dynamic";

/**
 * GET /api/title-sessions/[sessionId]/messages
 * Get all messages for a specific session
 */
export const GET = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ sessionId: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { sessionId } = await params;

      // Verify session exists and belongs to user
      const session = await getTitleChatSession(sessionId);
      if (!session || session.userId !== userId) {
        return NextResponse.json(
          {
            success: false,
            error: "Session not found or unauthorized",
          },
          { status: 404 }
        );
      }

      const messages = await getSessionMessages(sessionId);

      return NextResponse.json({
        success: true,
        messages,
      });
    } catch (error) {
      console.error("Error fetching session messages:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch session messages",
        },
        { status: 500 }
      );
    }
  }
);

/**
 * POST /api/title-sessions/[sessionId]/messages
 * Create a new message in a session
 */
export const POST = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ sessionId: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { sessionId } = await params;
      const { type, content, titles, status, error } = await req.json();

      // Verify session exists and belongs to user
      const session = await getTitleChatSession(sessionId);
      if (!session || session.userId !== userId) {
        return NextResponse.json(
          {
            success: false,
            error: "Session not found or unauthorized",
          },
          { status: 404 }
        );
      }

      // Validate required fields
      if (!type || !content) {
        return NextResponse.json(
          {
            success: false,
            error: "Type and content are required",
          },
          { status: 400 }
        );
      }

      if (!["user", "assistant"].includes(type)) {
        return NextResponse.json(
          {
            success: false,
            error: "Type must be 'user' or 'assistant'",
          },
          { status: 400 }
        );
      }

      const message = await createTitleChatMessage({
        sessionId,
        type,
        content,
        titles,
        status,
        error,
      });

      return NextResponse.json({
        success: true,
        message,
      });
    } catch (error) {
      console.error("Error creating session message:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create session message",
        },
        { status: 500 }
      );
    }
  }
);
