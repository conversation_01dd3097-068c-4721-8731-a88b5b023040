import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  updateTitleChatMessage,
  getTitleChatSession,
} from "@/src/actions/titleChatSessionAction";
import { eq } from "drizzle-orm";
import { db } from "@/src/db";
import { titleChatMessages } from "@/src/db/schema";

export const dynamic = "force-dynamic";

/**
 * PATCH /api/title-sessions/[sessionId]/messages/[messageId]
 * Update a specific message in a session
 */
export const PATCH = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ sessionId: string; messageId: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { sessionId, messageId } = await params;
      const { content, titles, status, error } = await req.json();

      // Verify session exists and belongs to user
      const session = await getTitleChatSession(sessionId);
      if (!session || session.userId !== userId) {
        return NextResponse.json(
          {
            success: false,
            error: "Session not found or unauthorized",
          },
          { status: 404 }
        );
      }

      // Verify message exists and belongs to the session
      const [existingMessage] = await db
        .select()
        .from(titleChatMessages)
        .where(eq(titleChatMessages.id, messageId));

      if (!existingMessage || existingMessage.sessionId !== sessionId) {
        return NextResponse.json(
          {
            success: false,
            error: "Message not found or unauthorized",
          },
          { status: 404 }
        );
      }

      const updatedMessage = await updateTitleChatMessage(messageId, {
        content,
        titles,
        status,
        error,
      });

      if (!updatedMessage) {
        return NextResponse.json(
          {
            success: false,
            error: "Failed to update message",
          },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        message: updatedMessage,
      });
    } catch (error) {
      console.error("Error updating session message:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to update session message",
        },
        { status: 500 }
      );
    }
  }
);
