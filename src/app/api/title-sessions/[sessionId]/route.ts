import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  getTitleChatSessionWithMessages,
  updateTitleChatSession,
  deleteTitleChatSession,
} from "@/src/actions/titleChatSessionAction";

export const dynamic = "force-dynamic";

/**
 * GET /api/title-sessions/[sessionId]
 * Get a specific title chat session with messages
 */
export const GET = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ sessionId: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { sessionId } = await params;

      const sessionWithMessages = await getTitleChatSessionWithMessages(
        sessionId
      );

      if (!sessionWithMessages) {
        return NextResponse.json(
          {
            success: false,
            error: "Session not found",
          },
          { status: 404 }
        );
      }

      // Verify the session belongs to the authenticated user
      if (sessionWithMessages.userId !== userId) {
        return NextResponse.json(
          {
            success: false,
            error: "Unauthorized access to session",
          },
          { status: 403 }
        );
      }

      return NextResponse.json({
        success: true,
        session: sessionWithMessages,
      });
    } catch (error) {
      console.error("Error fetching title session:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch title session",
        },
        { status: 500 }
      );
    }
  }
);

/**
 * PATCH /api/title-sessions/[sessionId]
 * Update a title chat session
 */
export const PATCH = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ sessionId: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { sessionId } = await params;
      const { title } = await req.json();

      // First verify the session exists and belongs to the user
      const existingSession = await getTitleChatSessionWithMessages(sessionId);
      if (!existingSession || existingSession.userId !== userId) {
        return NextResponse.json(
          {
            success: false,
            error: "Session not found or unauthorized",
          },
          { status: 404 }
        );
      }

      const updatedSession = await updateTitleChatSession(sessionId, { title });

      if (!updatedSession) {
        return NextResponse.json(
          {
            success: false,
            error: "Failed to update session",
          },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        session: updatedSession,
      });
    } catch (error) {
      console.error("Error updating title session:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to update title session",
        },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/title-sessions/[sessionId]
 * Delete a title chat session
 */
export const DELETE = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ sessionId: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { sessionId } = await params;

      // First verify the session exists and belongs to the user
      const existingSession = await getTitleChatSessionWithMessages(sessionId);
      if (!existingSession || existingSession.userId !== userId) {
        return NextResponse.json(
          {
            success: false,
            error: "Session not found or unauthorized",
          },
          { status: 404 }
        );
      }

      await deleteTitleChatSession(sessionId);

      return NextResponse.json({
        success: true,
        message: "Session deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting title session:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to delete title session",
        },
        { status: 500 }
      );
    }
  }
);
