import { NextRequest, NextResponse } from "next/server";
import { openai } from "@/src/lib/openai";

/**
 * POST /api/speech-to-text
 * Transcribe audio using OpenAI's gpt-4o-transcribe model
 */
export async function POST(request: NextRequest) {
  try {
    // Parse multipart form data
    const formData = await request.formData();
    const audioFile = formData.get("audio") as File;
    const prompt = formData.get("prompt") as string | null;
    const language = formData.get("language") as string | null;
    const temperatureStr = formData.get("temperature") as string | null;

    // Validate audio file
    if (!audioFile) {
      return NextResponse.json(
        { success: false, error: "Audio file is required" },
        { status: 400 }
      );
    }

    // Validate file size (25MB limit)
    const maxSize = 25 * 1024 * 1024; // 25MB
    if (audioFile.size > maxSize) {
      return NextResponse.json(
        { success: false, error: "Audio file too large (max 25MB)" },
        { status: 400 }
      );
    }

    // Validate file type
    const supportedTypes = [
      "audio/webm",
      "audio/mp4",
      "audio/mpeg",
      "audio/mpga",
      "audio/m4a",
      "audio/wav",
      "audio/mp3",
    ];

    if (
      !supportedTypes.some((type) =>
        audioFile.type.includes(type.split("/")[1])
      )
    ) {
      return NextResponse.json(
        {
          success: false,
          error: `Unsupported audio format. Supported: ${supportedTypes.join(
            ", "
          )}`,
        },
        { status: 400 }
      );
    }

    console.log(
      `🎤 Transcribing audio: ${audioFile.name} (${audioFile.size} bytes, ${audioFile.type})`
    );
    console.log(`📊 Audio file details:`, {
      name: audioFile.name,
      size: audioFile.size,
      type: audioFile.type,
      lastModified: audioFile.lastModified,
    });

    // Prepare transcription options
    const transcriptionOptions: any = {
      file: audioFile,
      model: "gpt-4o-transcribe",
      response_format: "text",
    };

    // Add optional parameters
    if (prompt) {
      transcriptionOptions.prompt = prompt;
      console.log(`📝 Using prompt: ${prompt.substring(0, 100)}...`);
    }

    if (language) {
      transcriptionOptions.language = language;
      console.log(`🌍 Language: ${language}`);
    }

    if (temperatureStr) {
      const temperature = parseFloat(temperatureStr);
      if (!isNaN(temperature) && temperature >= 0 && temperature <= 1) {
        transcriptionOptions.temperature = temperature;
        console.log(`🌡️ Temperature: ${temperature}`);
      }
    }

    // Call OpenAI transcription API
    const startTime = Date.now();
    const transcription = await openai.audio.transcriptions.create(
      transcriptionOptions
    );
    const duration = Date.now() - startTime;

    console.log(`✅ Transcription completed in ${duration}ms`);

    // Log complete response structure for debugging
    console.log(
      `🔍 Full transcription response:`,
      JSON.stringify(transcription, null, 2)
    );

    // Handle different response formats from OpenAI
    let transcriptionText = "";

    if (typeof transcription === "string") {
      // OpenAI returns plain string when response_format is "text"
      transcriptionText = transcription;
    } else if (transcription?.text) {
      // Object with text property (standard OpenAI response)
      transcriptionText = transcription.text;
    }

    if (transcriptionText) {
      console.log(`📄 Result: ${transcriptionText.substring(0, 200)}...`);
    } else {
      console.log(`⚠️ No text found in transcription response`);
      console.log(`🔍 Response type: ${typeof transcription}`);
      console.log(`🔍 Response keys: ${Object.keys(transcription || {})}`);
    }

    // Validate result with better error handling
    if (!transcriptionText || transcriptionText.trim().length === 0) {
      console.log(
        `❌ Empty transcription result - possible causes: no speech detected, audio too quiet, or unsupported audio format`
      );
      return NextResponse.json(
        {
          success: false,
          error:
            "No speech detected in audio. Please speak clearly and ensure your microphone is working.",
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      text: transcriptionText.trim(),
      metadata: {
        duration_ms: duration,
        file_size: audioFile.size,
        file_type: audioFile.type,
        model: "gpt-4o-transcribe",
      },
    });
  } catch (error) {
    console.error("❌ Speech-to-text error:", error);

    // Log detailed error information for debugging
    console.error("🔍 Error details:", {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      name: error instanceof Error ? error.name : undefined,
      cause: error instanceof Error ? error.cause : undefined,
    });

    // Handle specific OpenAI errors
    if (error && typeof error === "object" && "error" in error) {
      const openaiError = error as any;
      console.error(
        "🔍 OpenAI error details:",
        JSON.stringify(openaiError, null, 2)
      );

      if (openaiError.error?.code === "invalid_request_error") {
        return NextResponse.json(
          { success: false, error: "Invalid audio format or corrupted file" },
          { status: 400 }
        );
      }

      if (openaiError.error?.code === "rate_limit_exceeded") {
        return NextResponse.json(
          {
            success: false,
            error: "Rate limit exceeded. Please try again later.",
          },
          { status: 429 }
        );
      }
    }

    // Generic error handling
    const errorMessage =
      error instanceof Error ? error.message : "Failed to transcribe audio";

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

/**
 * GET /api/speech-to-text
 * Get speech-to-text service information
 */
export async function GET() {
  return NextResponse.json({
    service: "OpenAI Speech-to-Text",
    model: "gpt-4o-transcribe",
    supported_formats: [
      "audio/webm",
      "audio/mp4",
      "audio/mpeg",
      "audio/mpga",
      "audio/m4a",
      "audio/wav",
      "audio/mp3",
    ],
    max_file_size: "25MB",
    features: [
      "High-quality transcription",
      "Multi-language support",
      "Context-aware prompting",
      "Real-time processing",
    ],
  });
}
