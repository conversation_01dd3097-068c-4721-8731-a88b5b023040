import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { v4 as uuidv4 } from "uuid";
import { openai } from "@/src/lib/openai";
import { getCurrentDateContext } from "@/src/utils/date";
const improveTitleSchema = z.object({
  originalTitle: z.string().min(1, "Original title is required"),
  style: z.enum([
    "clickbait",
    "professional",
    "creative",
    "seo",
    "dramatic",
    "curiosity",
    "revealed",
    "snappy",
    "emphasize",
  ]),
});

function getStylePrompt(style: string): string {
  const stylePrompts = {
    clickbait:
      "Make it more clickbait with emotional hooks, numbers, and curiosity gaps that compel clicks",
    professional:
      "Make it more professional, clear, and business-appropriate with authoritative language",
    creative:
      "Make it more creative and artistic with unique wordplay, metaphors, and imaginative elements",
    seo: "Optimize for SEO with relevant keywords, search-friendly structure, and better discoverability",
    dramatic:
      "Add dramatic flair with emotional intensity, urgency, and powerful impact words",
    curiosity:
      "Enhance curiosity with mystery, intrigue, and questions that demand answers",
    revealed:
      "Focus on revealing secrets, exposing truths, or uncovering hidden information",
    snappy:
      "Make it shorter, punchier, and more memorable with concise, impactful language",
    emphasize:
      "Add bold emphasis with strong adjectives, capitalization, and attention-grabbing elements",
  };

  return (
    stylePrompts[style as keyof typeof stylePrompts] || "Improve the title"
  );
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { originalTitle, style } = improveTitleSchema.parse(body);

    const stylePrompt = getStylePrompt(style);

    // Get current date context for AI awareness
    const dateContext = getCurrentDateContext();

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: `You are a title improvement expert. Your task is to take an existing title and improve it based on a specific style request.

CURRENT DATE CONTEXT (for reference when relevant):
- Today's Date: ${dateContext.formattedDate}
- Current Year: ${dateContext.year}
- Current Month: ${dateContext.month}
- Current Season: ${dateContext.season}
- Day of Week: ${dateContext.dayOfWeek}

Note: Only include dates/years in titles when contextually relevant to the content. Don't force date references if they don't naturally fit the topic.

Rules:
1. Generate exactly 1 improved title
2. Keep the core meaning and topic of the original title
3. Apply the requested style transformation effectively
4. Ensure the improved title is engaging and relevant
5. When appropriate, reference current year/season/time for relevance and urgency
6. Return only the improved title text, nothing else

Style: ${style}
Instruction: ${stylePrompt}`,
        },
        {
          role: "user",
          content: `Original title: "${originalTitle}"

Please improve this title using the ${style} style.`,
        },
      ],
      temperature: 0.8,
      max_tokens: 100,
    });

    const improvedTitleText = completion.choices[0]?.message?.content?.trim();

    if (!improvedTitleText) {
      throw new Error("Failed to generate improved title");
    }

    const improvedTitle = {
      id: uuidv4(),
      text: improvedTitleText,
      style: style,
    };

    return NextResponse.json({
      success: true,
      title: improvedTitle,
    });
  } catch (error) {
    console.error("Error improving title:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid request data",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Failed to improve title",
      },
      { status: 500 }
    );
  }
}
