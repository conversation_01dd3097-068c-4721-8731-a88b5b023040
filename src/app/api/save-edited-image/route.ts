import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { db } from "@/src/db";
import { editedImages } from "@/src/db/schema";
import { createId } from "@paralleldrive/cuid2";

export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const { userId } = req.user;
    const {
      originalImageUrl,
      editedImageUrl,
      sourceType,
      sourceId,
      prompt,
      personaUsed = false,
    } = (await req.json()) as {
      originalImageUrl: string;
      editedImageUrl: string;
      sourceType: "generated" | "recreated" | "faceswap" | "background_removal";
      sourceId: string;
      prompt: string;
      personaUsed?: boolean;
    };

    if (
      !originalImageUrl ||
      !editedImageUrl ||
      !sourceType ||
      !sourceId ||
      !prompt
    ) {
      return NextResponse.json(
        { success: false, error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Create new edited image entry
    const [editedImage] = await db
      .insert(editedImages)
      .values({
        id: createId(),
        userId,
        originalImageUrl,
        editedImageUrl,
        sourceType,
        sourceId,
        prompt,
        personaUsed,
      })
      .returning();

    console.log(`✅ Edited image saved successfully to database`);

    return NextResponse.json({
      success: true,
      message: "Edited image saved successfully",
      data: editedImage,
    });
  } catch (error: any) {
    console.error("Error saving edited image:", error);
    return NextResponse.json(
      { success: false, error: "Internal Server Error" },
      { status: 500 }
    );
  }
});
