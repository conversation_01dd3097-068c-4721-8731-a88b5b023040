import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { db } from "@/src/db";
import { editedImages } from "@/src/db/schema";
import { eq, desc, asc, ilike, and, count } from "drizzle-orm";

export const dynamic = "force-dynamic";

export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const { userId } = req.user;
    const { searchParams } = new URL(req.url);

    // Parse query parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || "";
    const sourceType = searchParams.get("sourceType") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Build where conditions
    const whereConditions = [eq(editedImages.userId, userId)];

    if (search) {
      whereConditions.push(ilike(editedImages.prompt, `%${search}%`));
    }

    if (sourceType) {
      whereConditions.push(eq(editedImages.sourceType, sourceType));
    }

    // Build order by
    const orderByColumn = 
      sortBy === "sourceType" ? editedImages.sourceType :
      sortBy === "prompt" ? editedImages.prompt :
      editedImages.createdAt;

    const orderDirection = sortOrder === "asc" ? asc : desc;

    // Get total count for pagination
    const [totalResult] = await db
      .select({ count: count() })
      .from(editedImages)
      .where(and(...whereConditions));

    const totalItems = totalResult.count;
    const totalPages = Math.ceil(totalItems / limit);
    const offset = (page - 1) * limit;

    // Get edited images with pagination
    const images = await db
      .select()
      .from(editedImages)
      .where(and(...whereConditions))
      .orderBy(orderDirection(orderByColumn))
      .limit(limit)
      .offset(offset);

    const pagination = {
      currentPage: page,
      totalPages,
      totalItems,
      itemsPerPage: limit,
    };

    return NextResponse.json(
      {
        success: true,
        data: {
          editedImages: images,
          pagination,
        },
      },
      {
        headers: {
          "Cache-Control": "private, max-age=30, stale-while-revalidate=60",
          Vary: "Authorization",
        },
      }
    );
  } catch (error) {
    console.error("Error fetching edited images:", error);
    return NextResponse.json(
      { success: false, error: "Internal Server Error" },
      { status: 500 }
    );
  }
});
