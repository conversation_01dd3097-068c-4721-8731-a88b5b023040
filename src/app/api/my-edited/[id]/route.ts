import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { db } from "@/src/db";
import { editedImages } from "@/src/db/schema";
import { eq, and } from "drizzle-orm";
import cloudinary from "@/src/lib/cloudinary";

export const DELETE = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { id } = await params;

      // Find the edited image to delete
      const [editedImage] = await db
        .select()
        .from(editedImages)
        .where(
          and(
            eq(editedImages.id, id),
            eq(editedImages.userId, userId)
          )
        );

      if (!editedImage) {
        return NextResponse.json(
          { success: false, error: "Edited image not found or access denied" },
          { status: 404 }
        );
      }

      // Extract public_id from Cloudinary URL for deletion
      const urlParts = editedImage.editedImageUrl.split("/");
      const fileWithExtension = urlParts[urlParts.length - 1];
      const publicId = `thumbnails/${fileWithExtension.split(".")[0]}`;

      // Delete from Cloudinary
      try {
        await cloudinary.uploader.destroy(publicId);
      } catch (cloudinaryError) {
        console.warn("Failed to delete from Cloudinary:", cloudinaryError);
        // Continue with database deletion even if Cloudinary fails
      }

      // Delete from database
      await db
        .delete(editedImages)
        .where(
          and(
            eq(editedImages.id, id),
            eq(editedImages.userId, userId)
          )
        );

      return NextResponse.json({
        success: true,
        message: "Edited image deleted successfully",
      });

    } catch (error: any) {
      console.error("Error deleting edited image:", error);
      return NextResponse.json(
        { success: false, error: "Internal Server Error" },
        { status: 500 }
      );
    }
  }
);
