import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { createFaceSwapThumbnail } from "@/src/actions/faceSwapAction";
import { getPersona } from "@/src/actions/personaActions";
import {
  createFluxFinetuneAPI,
  FluxFinetuneAPIError,
} from "@/src/lib/flux-finetune";
import cloudinary from "@/src/lib/cloudinary";
import sharp from "sharp";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 300;

export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    const { userId } = req.user;

    // The client no longer sends a mask. The server handles it.
    const { imageUrl, persona } = (await req.json()) as {
      imageUrl: string;
      persona: {
        id: string;
        name: string;
        finetuneId: string;
      };
    };

    // Validate inputs
    if (!imageUrl || typeof imageUrl !== "string") {
      return NextResponse.json(
        { success: false, error: "Invalid or missing image URL" },
        { status: 400 }
      );
    }

    if (!persona?.id || !persona?.finetuneId) {
      return NextResponse.json(
        { success: false, error: "Valid persona with finetune ID is required" },
        { status: 400 }
      );
    }

    // Verify persona is ready for face swapping
    const personaResult = await getPersona(persona.id, userId);
    if (!personaResult.success || !personaResult.persona) {
      return NextResponse.json(
        { success: false, error: "Persona not found" },
        { status: 404 }
      );
    }

    const personaData = personaResult.persona;
    if (personaData.status !== "ready" || !personaData.finetuneId) {
      return NextResponse.json(
        {
          success: false,
          error: `Persona "${personaData.name}" is not ready for face swapping. Status: ${personaData.status}`,
        },
        { status: 400 }
      );
    }

    // Handle both data URLs and regular URLs
    let imageBuffer: Buffer;
    try {
      if (imageUrl.startsWith("data:")) {
        // Handle data URL (base64)
        const base64Data = imageUrl.split(",")[1];
        if (!base64Data) {
          throw new Error("Invalid data URL format");
        }
        imageBuffer = Buffer.from(base64Data, "base64");
        console.log(`📷 Processed data URL image: ${imageBuffer.length} bytes`);
      } else {
        // Handle regular URL
        const imgRes = await fetch(imageUrl);
        if (!imgRes.ok) throw new Error("Failed to fetch image");
        imageBuffer = Buffer.from(await imgRes.arrayBuffer());
        console.log(`📷 Fetched image from URL: ${imageBuffer.length} bytes`);
      }
    } catch (error) {
      console.error("Image processing error:", error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch or process image" },
        { status: 400 }
      );
    }

    // Create optimized face swap prompt with trigger word based on BFL best practices
    const triggerWord = personaData.triggerWord || "MYCHAR";

    // Enhanced prompt following BFL documentation for face swapping with finetuned models
    // Prepend trigger word and provide detailed description for better results
    const faceSwapPrompt = `${triggerWord}, a professional portrait of ${triggerWord}, photorealistic face, high detail, natural skin texture, seamless integration, studio lighting, sharp focus, 4k quality`;

    console.log(`Performing face swap with persona: ${personaData.name}`);
    console.log(`Using trigger word: ${triggerWord}`);
    console.log(`Finetune ID: ${personaData.finetuneId}`);
    console.log(`Optimized prompt: "${faceSwapPrompt}"`);

    // Use FLUX Fill with finetune model for face swapping
    console.log("🎯 Using FLUX Fill with finetune model for face swapping");

    // Generate the enhanced face mask on the server for maximum reliability
    console.log("🤖 Generating enhanced face mask on the server...");
    const finalFaceMask = await createServerSideFaceMask(imageBuffer);

    const fluxFinetuneAPI = createFluxFinetuneAPI("us");

    let generatedImageUrl: string;
    try {
      // Optimized parameters based on BFL documentation for face swapping
      generatedImageUrl = await fluxFinetuneAPI.fillWithFinetune({
        finetune_id: personaData.finetuneId!,
        image: imageBuffer.toString("base64"),
        mask: finalFaceMask,
        prompt: faceSwapPrompt,
        steps: 50, // Good balance of quality and speed
        guidance: 30, // Higher guidance for better prompt following with finetuned models
        finetune_strength: 1.5, // Increased for stronger persona influence
        output_format: "jpeg",
        safety_tolerance: 2,
        prompt_upsampling: false, // Disabled for more predictable results with finetunes
      });

      console.log("✅ Face swap generation completed successfully");
      console.log(`Generated image URL: ${generatedImageUrl}`);
    } catch (error) {
      console.error("❌ Face swap generation failed:", error);

      // Enhanced error handling with detailed logging
      let errorMessage = "Face swap generation failed";
      let statusCode = 500;

      if (error instanceof FluxFinetuneAPIError) {
        errorMessage = `Flux API Error: ${error.message}`;
        statusCode = error.status || 500;
        console.error("Flux API Error Details:", {
          message: error.message,
          status: error.status,
          details: error.details,
        });
      } else if (error instanceof Error) {
        errorMessage = error.message;
        console.error("Error details:", error.stack);
      }

      // Log additional context for debugging
      console.error("Face swap context:", {
        personaId: personaData.id,
        personaName: personaData.name,
        finetuneId: personaData.finetuneId,
        triggerWord,
        imageSize: imageBuffer.length,
        maskGenerated: !!finalFaceMask,
      });

      return NextResponse.json(
        {
          success: false,
          error: errorMessage,
          context: "face_swap_generation",
        },
        { status: statusCode }
      );
    }

    // Upload to Cloudinary directly from the generated URL to avoid timeouts
    console.log(
      `Uploading generated image to Cloudinary from URL: ${generatedImageUrl}`
    );
    const uploadResult = await cloudinary.uploader.upload(generatedImageUrl, {
      folder: "faceswap",
      public_id: `faceswap_${Date.now()}`,
      width: 1280,
      height: 720,
      crop: "fill",
      quality: "auto:best",
      format: "jpg",
      flags: "progressive",
    });

    // Save to database
    const faceSwapThumbnail = await createFaceSwapThumbnail({
      userId,
      imageUrl: uploadResult.secure_url,
      originalImageUrl: imageUrl,
      personaImageUrl: personaData.generatedImageUrl || "",
    });

    return NextResponse.json({
      success: true,
      imageUrl: uploadResult.secure_url,
      thumbnailId: faceSwapThumbnail.id,
      method: "fill",
    });
  } catch (err) {
    console.error("Error in /api/faceswap:", err);

    const errorMessage =
      err instanceof Error ? err.message : "Face swap generation failed";
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
});

// --- Server-Side Mask Generation ---

async function createServerSideFaceMask(imageBuffer: Buffer): Promise<string> {
  console.log("🎭 Creating server-side face mask...");

  // Try OpenAI vision for precise face detection first
  try {
    console.log("🔍 Attempting precise face detection with OpenAI Vision...");
    const preciseMask = await createOpenAIFaceMask(imageBuffer);
    if (preciseMask) {
      console.log("✅ Using precise OpenAI face detection mask");
      return preciseMask;
    }
  } catch (error) {
    console.error("❌ OpenAI face detection failed:", error);
  }

  // Fallback to enhanced mask if OpenAI detection fails
  console.log("⚠️ Using enhanced fallback mask for better face swap coverage");
  return createEnhancedFallbackMask(imageBuffer);
}

async function createEnhancedFallbackMask(
  imageBuffer: Buffer
): Promise<string> {
  try {
    const { width, height } = await sharp(imageBuffer).metadata();
    if (!width || !height) {
      throw new Error(
        "Could not determine image dimensions for enhanced mask."
      );
    }

    // Create a more sophisticated mask that covers typical face areas better
    // This mask is larger and positioned to cover most common face positions
    const centerX = width / 2;
    const centerY = height * 0.45; // Slightly higher than center for typical face position
    const radiusX = width * 0.4; // Even wider coverage
    const radiusY = height * 0.5; // Even taller coverage including forehead and chin

    const svgMask = `
      <svg width="${width}" height="${height}">
        <defs>
          <radialGradient id="faceGradient" cx="50%" cy="45%" r="50%">
            <stop offset="0%" style="stop-color:white;stop-opacity:1" />
            <stop offset="70%" style="stop-color:white;stop-opacity:1" />
            <stop offset="85%" style="stop-color:white;stop-opacity:0.8" />
            <stop offset="100%" style="stop-color:white;stop-opacity:0" />
          </radialGradient>
        </defs>
        <rect x="0" y="0" width="${width}" height="${height}" fill="black" />
        <ellipse cx="${centerX}" cy="${centerY}" rx="${radiusX}" ry="${radiusY}" fill="url(#faceGradient)" />
      </svg>
    `;

    const maskBuffer = await sharp(Buffer.from(svgMask)).png().toBuffer();
    console.log(`✅ Generated enhanced face mask: ${maskBuffer.length} bytes`);
    return maskBuffer.toString("base64");
  } catch (error) {
    console.error("Error creating enhanced mask:", error);
    throw new Error("Failed to create enhanced mask on server.");
  }
}

// --- OpenAI Vision Face Detection ---

async function createOpenAIFaceMask(
  imageBuffer: Buffer
): Promise<string | null> {
  try {
    console.log("🔍 Using OpenAI Vision for precise face detection...");

    // Convert image to base64 for OpenAI
    const base64Image = imageBuffer.toString("base64");

    // Detect image format from buffer
    let mimeType = "image/jpeg"; // Default
    if (imageBuffer[0] === 0xff && imageBuffer[1] === 0xd8) {
      mimeType = "image/jpeg";
    } else if (imageBuffer[0] === 0x89 && imageBuffer[1] === 0x50) {
      mimeType = "image/png";
    } else if (imageBuffer[0] === 0x47 && imageBuffer[1] === 0x49) {
      mimeType = "image/gif";
    } else if (imageBuffer[0] === 0x52 && imageBuffer[1] === 0x49) {
      mimeType = "image/webp";
    }

    console.log(`📷 Detected image format: ${mimeType}`);

    // Call OpenAI Vision API to detect faces
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: "gpt-4o",
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: 'Analyze this image and detect the main human face. Return ONLY a JSON object with the face bounding box coordinates as percentages of image dimensions. Format: {"x": percentage_from_left, "y": percentage_from_top, "width": percentage_width, "height": percentage_height}. If no clear human face is found, return {"error": "no_face_detected"}.',
              },
              {
                type: "image_url",
                image_url: {
                  url: `data:${mimeType};base64,${base64Image}`,
                },
              },
            ],
          },
        ],
        max_tokens: 150,
        temperature: 0.1,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `OpenAI API error: ${response.status} ${response.statusText}`
      );
    }

    const data = await response.json();
    const content = data.choices?.[0]?.message?.content?.trim();

    if (!content) {
      throw new Error("No response from OpenAI Vision API");
    }

    console.log("🤖 OpenAI Vision response:", content);

    // Clean and parse the JSON response (remove markdown code blocks if present)
    let cleanContent = content.trim();
    if (cleanContent.startsWith("```json")) {
      cleanContent = cleanContent
        .replace(/```json\s*/, "")
        .replace(/\s*```$/, "");
    } else if (cleanContent.startsWith("```")) {
      cleanContent = cleanContent.replace(/```\s*/, "").replace(/\s*```$/, "");
    }

    let faceData;
    try {
      faceData = JSON.parse(cleanContent);
    } catch (parseError) {
      console.error("Failed to parse OpenAI response as JSON:", cleanContent);
      return null;
    }

    // Check if face was detected
    if (faceData.error === "no_face_detected") {
      console.log("❌ No face detected by OpenAI Vision");
      return null;
    }

    // Validate face coordinates
    if (!faceData.x || !faceData.y || !faceData.width || !faceData.height) {
      console.error("Invalid face coordinates from OpenAI:", faceData);
      return null;
    }

    // Get image dimensions
    const { width: imgWidth, height: imgHeight } = await sharp(
      imageBuffer
    ).metadata();
    if (!imgWidth || !imgHeight) {
      throw new Error("Could not determine image dimensions");
    }

    // Convert percentages to pixel coordinates with moderate padding
    const padding = 0.4; // 40% padding around detected face for better coverage
    const baseFaceX = (faceData.x / 100) * imgWidth;
    const baseFaceY = (faceData.y / 100) * imgHeight;
    const baseFaceWidth = (faceData.width / 100) * imgWidth;
    const baseFaceHeight = (faceData.height / 100) * imgHeight;

    // Apply padding while keeping within image bounds
    const paddingX = (baseFaceWidth * padding) / 2;
    const paddingY = (baseFaceHeight * padding) / 2;
    const faceX = Math.max(0, baseFaceX - paddingX);
    const faceY = Math.max(0, baseFaceY - paddingY);
    const faceWidth = Math.min(imgWidth - faceX, baseFaceWidth + paddingX * 2);
    const faceHeight = Math.min(
      imgHeight - faceY,
      baseFaceHeight + paddingY * 2
    );

    // Calculate center and radius for elliptical mask
    const centerX = faceX + faceWidth / 2;
    const centerY = faceY + faceHeight / 2;
    const radiusX = faceWidth * 0.6; // Use the padded dimensions
    const radiusY = faceHeight * 0.6;

    console.log(
      `📍 Detected face: center(${centerX.toFixed(0)}, ${centerY.toFixed(
        0
      )}) radius(${radiusX.toFixed(0)}, ${radiusY.toFixed(0)})`
    );

    // Create precise mask based on detected face
    const svgMask = `
      <svg width="${imgWidth}" height="${imgHeight}">
        <defs>
          <radialGradient id="preciseGradient" cx="50%" cy="50%" r="50%">
            <stop offset="0%" style="stop-color:white;stop-opacity:1" />
            <stop offset="80%" style="stop-color:white;stop-opacity:1" />
            <stop offset="95%" style="stop-color:white;stop-opacity:0.5" />
            <stop offset="100%" style="stop-color:white;stop-opacity:0" />
          </radialGradient>
        </defs>
        <rect x="0" y="0" width="${imgWidth}" height="${imgHeight}" fill="black" />
        <ellipse cx="${centerX}" cy="${centerY}" rx="${radiusX}" ry="${radiusY}" fill="url(#preciseGradient)" />
      </svg>
    `;

    const maskBuffer = await sharp(Buffer.from(svgMask)).png().toBuffer();
    console.log(
      `✅ Generated precise OpenAI face mask: ${maskBuffer.length} bytes`
    );
    return maskBuffer.toString("base64");
  } catch (error) {
    console.error("Error in OpenAI face detection:", error);
    return null;
  }
}
