import { create<PERSON>luxAPI, FluxAPIError } from "@/src/lib/flux";
import { createFluxFinetuneAPI } from "@/src/lib/flux-finetune";
import cloudinary from "@/src/lib/cloudinary";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { NextResponse } from "next/server";
import { createGeneratedThumbnail } from "@/src/actions/generatedThumbnailAction";
import { getPersona } from "@/src/actions/personaActions";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 300;

export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // User is automatically authenticated via withAuth wrapper
    const { userId } = req.user;

    const { prompt, persona, title, tags } = (await req.json()) as {
      prompt: string;
      persona?: {
        id: string;
        name: string;
        generatedImageUrl?: string;
        finetuneId?: string;
      };
      title?: string;
      tags?: string[];
    };

    console.log("API Request - Prompt:", prompt);
    console.log(
      "API Request - Persona:",
      persona
        ? {
            id: persona.id,
            name: persona.name,
            hasImage: !!persona.generatedImageUrl,
            hasFinetuneId: !!persona.finetuneId,
          }
        : "No persona"
    );
    if (!prompt || typeof prompt !== "string") {
      return NextResponse.json(
        { Tropic: false, error: "Invalid or missing prompt" },
        { status: 400 }
      );
    }

    // Check if we should use fine-tuned model or regular generation
    let useFinetunedModel = false;
    let personaData = null;

    if (persona && persona.finetuneId) {
      // Get full persona data to check if fine-tuning is complete
      try {
        const personaResult = await getPersona(persona.id, userId);
        if (
          personaResult.success &&
          personaResult.persona?.status === "ready"
        ) {
          useFinetunedModel = true;
          personaData = personaResult.persona;
          console.log(
            `Using fine-tuned model for persona: ${persona.name} (${persona.finetuneId})`
          );
        }
      } catch (error) {
        console.log(
          "Failed to get persona data, falling back to regular generation:",
          error
        );
      }
    }

    let enhancedPrompt: string;
    let imageUrl: string;

    if (useFinetunedModel && personaData) {
      // Optimized prompt structure for Ultra model
      enhancedPrompt = `${personaData.triggerWord}, YouTube thumbnail, ${prompt}, bold text, 16:9, photorealistic, dramatic lighting`;

      console.log("Generating with fine-tuned model...");
      console.log("Using trigger word:", personaData.triggerWord);
      console.log("Enhanced prompt:", enhancedPrompt);
      console.log("Using finetune_id:", personaData.finetuneId);

      // Use the SAME method as the working finetune generator
      const fluxFinetuneAPI = createFluxFinetuneAPI();
      imageUrl = await fluxFinetuneAPI.testGenerateWithModel(
        personaData.finetuneId!,
        enhancedPrompt,
        personaData.triggerWord,
        {
          aspect_ratio: "16:9",
          width: 1280,
          height: 720,
          finetune_strength: 1.0, // Optimized strength
          safety_tolerance: 2,
          output_format: "jpeg",
          prompt_upsampling: false, // Keep control
        }
      );
    } else {
      // Fallback to regular generation with reference image approach
      enhancedPrompt = `You are an expert visual designer specializing in high-click-through-rate promotional graphics. Your mission is to generate a single, clean, photorealistic digital artwork (16:9) for use on YouTube as a thumbnail.

    DESIGN BRIEF: Generate an image based on this core concept: ${prompt}`;

      // Add persona-specific instructions if a persona is selected (old approach)
      if (persona && persona.generatedImageUrl) {
        enhancedPrompt += `

    PERSONA INTEGRATION (Strict Preservation):
    The main subject of the image must be the person from the reference image (${persona.name}). The character's identity is paramount.
    - **Action:** Integrate "the person from the reference image" into the scene. Do NOT use vague terms like "her" or "the person."
    - **Preservation:** You MUST preserve their distinctive appearance. This includes maintaining the **exact same facial features, hairstyle, expression, and eye color** from the reference image.
    - **Modification:** Only the clothing and the context/environment should be adapted to fit the scene. Do not "transform" the person; "place" the person within the new setting.
    - **Reference URL:** ${persona.generatedImageUrl}`;

        console.log(
          "Enhanced prompt includes persona:",
          persona.name,
          "with image:",
          persona.generatedImageUrl
        );
      }

      enhancedPrompt += `
    
    DESIGN SPECIFICATIONS & COMPOSITION:
    - **Style:** Hyper-realistic, 8K resolution, cinematic quality, rendered with sharp focus, intricate details, and deep shadows. The final image should feel like a photorealistic digital painting.
    - **Composition:** Use dynamic camera angles and the rule of thirds. The main subject must be the clear focal point. Ensure strong, clean separation between the subject and the background to maximize clarity at small sizes.
    - **Color:** Use a bold, vibrant, and high-contrast color palette to grab attention.
    - **Lighting:** Employ dramatic, professional studio-style lighting to create depth and highlight the main subject.
    - **Mood:** The energy should be exciting, intriguing, and convey a strong, clear emotion.
    
    TYPOGRAPHY & TEXT (CRITICAL):
    - **Text Accuracy:** You must render any text exactly as specified in the user's prompt. Do not add, omit, misspell, or change any words.
    - **Literal Interpretation:** If the prompt specifies text on an object (e.g., "text on a sign," "words on a calendar"), you MUST render the object with the specified text on it. Do not simplify or replace the concept (e.g., do not turn it into a neon sign unless explicitly asked).
    - **Readability & Style:** Text must be bold, clean, and highly legible at a small scale. Use strong, modern, sans-serif fonts.
    - **Visual Separation:** To make text 'pop', apply a contrasting outline (stroke) AND/OR a subtle drop shadow to all text. This is crucial for separating it from the background.
    - **Strategic Placement:** Position text to enhance the composition. Place it in areas of negative space and avoid covering the main subject's face or other crucial visual elements.

  VISUAL COHERENCE & STORYTELLING:
  - **Integrated Elements:** All visual elements, including symbols or effects, must be physically integrated into the scene. They should appear to be part of the environment, not disconnected floating overlays or clip art. For example, an effect should emit *from* an object.
  - **Consistent Scale:** Ensure all elements are appropriately scaled. A symbol like a heart should be the size of a person's hand, not a tiny icon.
  - **Proportional Relationships:** Maintain proportional relationships between elements. If a person is holding an object, the object should be sized appropriately for the person's hand.
  
  - **Environmental Detail:** Enrich the background and foreground with details that support the core story. If the theme is chaos, the environment should look chaotic. If the theme is discovery, the environment should reflect that.
  - **Physical Interaction:** Ensure the subject's interaction with objects is realistic and direct. 
    
    ABSOLUTE DON'TS (NON-NEGOTIABLE RULE):
    - **NO UI ELEMENTS:** Under no circumstances should you include any part of a user interface. This means: NO play buttons, NO video progress bars, NO timeline scrubs, NO volume icons, NO channel logos, and NO subscription buttons.
    - **CLEAN ARTWORK ONLY:** The output must be a pure digital artwork. It must NOT resemble a screenshot of a video player, website, or any application.
    - **NO LOW QUALITY:** Avoid blurry, pixelated, or noisy artifacts. The image must be sharp, clean, and professional.
    
    CREATIVE EXECUTION:
    Execute this design with the precision of an expert. The final image must be visually stunning and so compelling it's impossible to scroll past without clicking.`;

      // Initialize Flux API for regular generation
      const fluxAPI = createFluxAPI();

      console.log("Generating thumbnail with Flux KontexMax...");
      console.log("Enhanced prompt:", enhancedPrompt);

      // Generate image using Flux KontexMax
      imageUrl = await fluxAPI.generateImage({
        prompt: enhancedPrompt,
        aspect_ratio: "16:9", // YouTube thumbnail aspect ratio
        output_format: "jpeg",
        safety_tolerance: 2, // Moderate safety level
      });
    }

    console.log("Generation completed. Image URL:", imageUrl);

    // Download the image from delivery URL and convert to base64
    // Use appropriate API based on generation method
    let base64Image: string;
    if (useFinetunedModel) {
      const fluxFinetuneAPI = createFluxFinetuneAPI();
      base64Image = await fluxFinetuneAPI.downloadImageAsBase64(imageUrl);
    } else {
      const fluxAPI = createFluxAPI();
      base64Image = await fluxAPI.downloadImageAsBase64(imageUrl);
    }
    const dataUri = `data:image/jpeg;base64,${base64Image}`;
    const uploadResult = await cloudinary.uploader.upload(dataUri, {
      folder: "thumbnails",
      public_id: `thumb_${Date.now()}`,
      width: 1280,
      height: 720,
      crop: "fill",
      quality: "auto:best",
      format: "jpg",
      flags: "progressive",
    });

    // Save to database
    const savedThumbnail = await createGeneratedThumbnail({
      userId,
      imageUrl: uploadResult.secure_url,
      prompt,
      title: title || undefined,
      tags: tags || [],
    });

    return NextResponse.json({
      success: true,
      imageUrl: uploadResult.secure_url,
      responseId: savedThumbnail.id, // Use thumbnail ID as response ID
      thumbnailId: savedThumbnail.id,
    });
  } catch (err) {
    console.error("Error generating thumbnail:", err);

    // Check if it's an authentication error
    if (err instanceof Error && err.message === "Unauthorized") {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if it's a Flux API error
    if (err instanceof FluxAPIError) {
      // Handle moderation errors
      if (err.message.includes("moderated")) {
        return NextResponse.json(
          {
            success: false,
            error:
              "Content was blocked by safety filters. Try rephrasing your prompt to focus on positive, exciting adventures rather than distressing situations.",
            suggestion:
              "Consider words like 'explorer', 'adventurer', 'discovery' instead of 'shocked', 'sweaty', 'broken'",
          },
          { status: 400 }
        );
      }

      // Handle timeout errors
      if (err.status === 408) {
        return NextResponse.json(
          {
            success: false,
            error:
              "Image generation timed out. Please try again with a simpler prompt.",
          },
          { status: 408 }
        );
      }

      // Handle rate limiting
      if (err.status === 429) {
        return NextResponse.json(
          {
            success: false,
            error:
              "Too many requests. Please wait a moment before trying again.",
          },
          { status: 429 }
        );
      }

      // Handle insufficient credits
      if (err.status === 402) {
        return NextResponse.json(
          {
            success: false,
            error: "Insufficient credits. Please check your Flux API account.",
          },
          { status: 402 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: err.message,
        },
        { status: err.status || 500 }
      );
    }

    // Check if it's a legacy content policy violation (for backward compatibility)
    if (
      err instanceof Error &&
      err.message.includes("content_policy_violation")
    ) {
      return NextResponse.json(
        {
          success: false,
          error:
            "Content was blocked by safety filters. Try rephrasing your prompt to focus on positive, exciting adventures rather than distressing situations.",
          suggestion:
            "Consider words like 'explorer', 'adventurer', 'discovery' instead of 'shocked', 'sweaty', 'broken'",
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: err instanceof Error ? err.message : "Internal Server Error",
      },
      { status: 500 }
    );
  }
});
