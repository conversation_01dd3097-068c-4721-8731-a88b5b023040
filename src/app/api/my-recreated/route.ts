import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { getUserRecreatedThumbnails } from "@/src/actions/recreatedThumbnailAction";

export const dynamic = "force-dynamic";

export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // User is automatically authenticated via withAuth wrapper
    const { userId } = req.user;

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || "";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";
    const sourceType = searchParams.get("sourceType") as
      | "youtube_url"
      | "upload_file"
      | undefined;
    const personaUsed = searchParams.get("personaUsed");

    // Use Drizzle action for paginated thumbnails
    const { thumbnails, pagination } = await getUserRecreatedThumbnails(
      userId,
      {
        page,
        limit,
        search,
        sortBy,
        sortOrder,
        sourceType,
        personaUsed:
          personaUsed !== null && personaUsed !== undefined
            ? personaUsed === "true"
            : undefined,
      }
    );

    return NextResponse.json(
      {
        success: true,
        data: {
          thumbnails,
          pagination,
        },
      },
      {
        headers: {
          "Cache-Control": "private, max-age=30, stale-while-revalidate=60",
          Vary: "Authorization",
        },
      }
    );
  } catch (error) {
    console.error("Error fetching recreated thumbnails:", error);
    return NextResponse.json(
      { success: false, error: "Internal Server Error" },
      { status: 500 }
    );
  }
});
