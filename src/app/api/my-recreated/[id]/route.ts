import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  getRecreatedThumbnail,
  deleteRecreatedThumbnail,
} from "@/src/actions/recreatedThumbnailAction";
import cloudinary from "@/src/lib/cloudinary";

export const dynamic = "force-dynamic";

// Get a specific recreated thumbnail
export const GET = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      // User is automatically authenticated via withAuth wrapper
      const { userId } = req.user;

      const { id } = await params;

      if (!id) {
        return NextResponse.json(
          { success: false, error: "Thumbnail ID is required" },
          { status: 400 }
        );
      }

      // Find the thumbnail and ensure it belongs to the user
      const thumbnail = await getRecreatedThumbnail(id);
      if (!thumbnail || thumbnail.userId !== userId) {
        return NextResponse.json(
          { success: false, error: "Thumbnail not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          id: thumbnail.id,
          imageUrl: thumbnail.imageUrl,
          sourceType: thumbnail.sourceType,
          prompt: thumbnail.prompt,
          personaUsed: thumbnail.personaUsed,
          createdAt: thumbnail.createdAt,
          updatedAt: thumbnail.updatedAt,
        },
      });
    } catch (error) {
      console.error("Error fetching recreated thumbnail:", error);
      return NextResponse.json(
        { success: false, error: "Internal Server Error" },
        { status: 500 }
      );
    }
  }
);

// Delete a specific recreated thumbnail
export const DELETE = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      // User is automatically authenticated via withAuth wrapper
      const { userId } = req.user;

      const { id } = await params;

      if (!id) {
        return NextResponse.json(
          { success: false, error: "Thumbnail ID is required" },
          { status: 400 }
        );
      }

      // Find the thumbnail and ensure it belongs to the user
      const thumbnail = await getRecreatedThumbnail(id);
      if (!thumbnail || thumbnail.userId !== userId) {
        return NextResponse.json(
          { success: false, error: "Thumbnail not found" },
          { status: 404 }
        );
      }

      // Extract public_id from Cloudinary URL for deletion
      const imageUrl = thumbnail.imageUrl;
      let publicId: string | null = null;

      try {
        // Extract public_id from Cloudinary URL
        // URL format: https://res.cloudinary.com/{cloud_name}/image/upload/{transformations}/{public_id}.{format}
        const urlParts = imageUrl.split("/");
        const uploadIndex = urlParts.findIndex((part) => part === "upload");
        if (uploadIndex !== -1 && uploadIndex + 1 < urlParts.length) {
          const pathAfterUpload = urlParts.slice(uploadIndex + 1).join("/");
          // Remove file extension
          publicId = pathAfterUpload.replace(/\.[^/.]+$/, "");
        }
      } catch (error) {
        console.warn("Could not extract public_id from URL:", imageUrl);
      }

      // Delete from database first
      await deleteRecreatedThumbnail(id);

      // Try to delete from Cloudinary (non-blocking)
      if (publicId) {
        try {
          await cloudinary.uploader.destroy(publicId);
          console.log(
            `Successfully deleted image from Cloudinary: ${publicId}`
          );
        } catch (cloudinaryError) {
          console.warn(
            `Failed to delete image from Cloudinary: ${publicId}`,
            cloudinaryError
          );
          // Don't fail the request if Cloudinary deletion fails
        }
      }

      return NextResponse.json({
        success: true,
        message: "Thumbnail deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting recreated thumbnail:", error);
      return NextResponse.json(
        { success: false, error: "Internal Server Error" },
        { status: 500 }
      );
    }
  }
);
