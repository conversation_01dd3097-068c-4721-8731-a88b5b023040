import { NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { getUserBackgroundRemovals } from "@/src/actions/backgroundRemovalAction";

export const GET = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // User is automatically authenticated via withAuth wrapper
    const { userId } = req.user;

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const search = searchParams.get("search") || "";
    const sortBy = (searchParams.get("sortBy") || "createdAt") as
      | "createdAt"
      | "updatedAt";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";

    // Use Drizzle action for paginated background removals
    const { backgroundRemovals, pagination } = await getUserBackgroundRemovals(
      userId,
      {
        page,
        limit,
        search,
        sortBy,
        sortOrder,
      }
    );

    return NextResponse.json(
      {
        success: true,
        data: {
          backgroundRemovals,
          pagination,
        },
      },
      {
        headers: {
          "Cache-Control": "private, max-age=30, stale-while-revalidate=60",
          Vary: "Authorization",
        },
      }
    );
  } catch (error) {
    console.error("Error fetching background removals:", error);

    // Check if it's an authentication error
    if (error instanceof Error && error.message === "Unauthorized") {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Failed to fetch background removals" },
      { status: 500 }
    );
  }
});
