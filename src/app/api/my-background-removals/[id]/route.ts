import { NextRequest, NextResponse } from "next/server";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import {
  deleteBackgroundRemoval,
  getBackgroundRemoval,
} from "@/src/actions/backgroundRemovalAction";
import cloudinary from "@/src/lib/cloudinary";

export const DELETE = withAuth(
  async (
    req: AuthenticatedRequest,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { userId } = req.user;
      const { id } = await params;

      if (!id) {
        return NextResponse.json(
          { success: false, error: "Background removal ID is required" },
          { status: 400 }
        );
      }

      // First, get the background removal to check ownership and get the image URL
      const backgroundRemoval = await getBackgroundRemoval(id);

      if (!backgroundRemoval) {
        return NextResponse.json(
          { success: false, error: "Background removal not found" },
          { status: 404 }
        );
      }

      // Check if the user owns this background removal
      if (backgroundRemoval.userId !== userId) {
        return NextResponse.json(
          { success: false, error: "Unauthorized" },
          { status: 403 }
        );
      }

      // Extract public ID from Cloudinary URL for deletion
      const imageUrl = backgroundRemoval.imageUrl;
      let publicId = null;

      if (imageUrl && imageUrl.includes("cloudinary.com")) {
        try {
          // Extract public ID from Cloudinary URL
          // URL format: https://res.cloudinary.com/[cloud]/image/upload/v[version]/[folder]/[public_id].[format]
          const urlParts = imageUrl.split("/");
          const fileWithExtension = urlParts[urlParts.length - 1];
          const fileName = fileWithExtension.split(".")[0];

          // Find the folder part (background-removals)
          const folderIndex = urlParts.findIndex(
            (part) => part === "background-removals"
          );
          if (folderIndex !== -1) {
            publicId = `background-removals/${fileName}`;
          } else {
            publicId = fileName;
          }
        } catch (error) {
          console.error(
            "Error extracting public ID from Cloudinary URL:",
            error
          );
        }
      }

      // Delete from database first
      const deletedBackgroundRemoval = await deleteBackgroundRemoval(
        id,
        userId
      );

      if (!deletedBackgroundRemoval) {
        return NextResponse.json(
          {
            success: false,
            error: "Failed to delete background removal from database",
          },
          { status: 500 }
        );
      }

      // Try to delete from Cloudinary (non-blocking)
      if (publicId) {
        try {
          await cloudinary.uploader.destroy(publicId);
          console.log(
            `Successfully deleted image from Cloudinary: ${publicId}`
          );
        } catch (cloudinaryError) {
          console.error(
            "Error deleting image from Cloudinary:",
            cloudinaryError
          );
          // Don't fail the request if Cloudinary deletion fails
        }
      }

      return NextResponse.json({
        success: true,
        message: "Background removal deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting background removal:", error);

      return NextResponse.json(
        { success: false, error: "Failed to delete background removal" },
        { status: 500 }
      );
    }
  }
);
