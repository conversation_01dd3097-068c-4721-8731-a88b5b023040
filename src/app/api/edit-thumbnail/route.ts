import { NextResponse } from "next/server";
import { createFluxAPI, FluxAPIError } from "@/src/lib/flux";
import cloudinary from "@/src/lib/cloudinary";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { getPersona } from "@/src/actions/personaActions";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 600; // 10 minutes for Pro Finetuned generation (with polling)

export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // User is automatically authenticated via withAuth wrapper
    const { userId } = req.user;

    const { imageUrl, prompt, personaId } = (await req.json()) as {
      imageUrl: string;
      prompt: string;
      personaId?: string;
    };

    if (!imageUrl || typeof imageUrl !== "string") {
      return NextResponse.json(
        { success: false, error: "Invalid or missing image URL" },
        { status: 400 }
      );
    }

    // Validate prompt is required
    if (!prompt || !prompt.trim()) {
      return NextResponse.json(
        { success: false, error: "Prompt is required for thumbnail editing" },
        { status: 400 }
      );
    }

    // Convert image URL to base64 for Flux API
    let imageBase64: string;

    try {
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }
      const arrayBuffer = await response.arrayBuffer();
      imageBase64 = Buffer.from(arrayBuffer).toString("base64");
    } catch (error) {
      console.error("Error fetching image:", error);
      return NextResponse.json(
        { success: false, error: "Failed to fetch the image for editing" },
        { status: 400 }
      );
    }

    // Initialize Flux API
    const fluxAPI = createFluxAPI();

    // Create enhanced prompt for better editing results
    const enhancedPrompt = enhanceEditingPrompt(prompt.trim());

    console.log("🎨 Starting thumbnail editing");
    console.log("📝 Original prompt:", prompt);
    console.log("✨ Enhanced prompt:", enhancedPrompt);
    console.log("🎭 Using persona:", personaId || "none");

    let editedImageUrl: string;

    try {
      if (personaId) {
        // Use persona-based editing with fine-tuned model
        console.log("🎭 Using persona-based thumbnail editing");

        // Get persona details to get the finetuneId
        const personaResponse = await getPersona(personaId, userId);
        if (!personaResponse.success || !personaResponse.persona) {
          throw new Error("Persona not found or access denied");
        }
        const personaData = personaResponse.persona;
        const finetuneId = personaData.finetuneId;

        if (!finetuneId) {
          throw new Error("Persona does not have a valid finetune ID");
        }

        // Create persona-specific prompt with trigger word
        const personaPrompt = `${personaData.triggerWord} ${enhancedPrompt}`;

        // Use Flux Pro Finetuned API for persona-based editing
        const response = await fetch(
          "https://api.us1.bfl.ai/v1/flux-pro-finetuned",
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "x-key": process.env.BFL_API_KEY!,
            },
            body: JSON.stringify({
              finetune_id: finetuneId,
              prompt: personaPrompt,
              image_prompt: imageBase64, // Base64 encoded image (no data URI prefix)
              finetune_strength: 0.8, // Moderate persona influence to preserve original better
              steps: 30, // Fewer steps for more conservative changes
              guidance: 2.0, // Lower guidance for more natural results
              width: 1280, // YouTube thumbnail width
              height: 720, // YouTube thumbnail height
              output_format: "jpeg",
              safety_tolerance: 2,
              prompt_upsampling: false,
            }),
          }
        );

        if (!response.ok) {
          const errorData = await response.text();
          throw new FluxAPIError(
            `Flux Pro Finetuned API error: ${response.status} ${response.statusText} - ${errorData}`,
            response.status
          );
        }

        const result = await response.json();
        editedImageUrl = result.sample;
      } else {
        // Use standard image editing
        console.log("🎨 Using standard image editing");
        editedImageUrl = await fluxAPI.generateImage({
          prompt: enhancedPrompt,
          input_image: `data:image/jpeg;base64,${imageBase64}`,
          aspect_ratio: "16:9",
          output_format: "jpeg",
          safety_tolerance: 2,
          prompt_upsampling: false,
        });
      }

      console.log("✅ Thumbnail editing completed successfully");
    } catch (error) {
      console.error("❌ Flux API error:", error);

      if (error instanceof FluxAPIError) {
        // Handle specific Flux API errors
        if (error.status === 402) {
          return NextResponse.json(
            {
              success: false,
              error:
                "Insufficient credits for thumbnail editing. Please check your account balance.",
            },
            { status: 402 }
          );
        }

        if (error.status === 429) {
          return NextResponse.json(
            {
              success: false,
              error: "Too many requests. Please wait before trying again.",
            },
            { status: 429 }
          );
        }

        if (error.message.includes("moderated")) {
          return NextResponse.json(
            {
              success: false,
              error:
                "Content was blocked by safety filters. Please try a different prompt or image.",
            },
            { status: 400 }
          );
        }
      }

      throw error; // Re-throw for general error handling
    }

    // Upload the edited image to Cloudinary for permanent storage
    console.log("📤 Uploading edited thumbnail to Cloudinary");

    try {
      const uploadResult = await cloudinary.uploader.upload(editedImageUrl, {
        folder: "edited-thumbnails",
        public_id: `edited_thumbnail_${Date.now()}`,
        quality: "auto:best",
        format: "jpg",
        flags: "progressive",
        transformation: [{ quality: "auto:best" }, { fetch_format: "auto" }],
      });

      console.log(
        "✅ Successfully uploaded to Cloudinary:",
        uploadResult.secure_url
      );

      return NextResponse.json({
        success: true,
        imageUrl: uploadResult.secure_url,
        originalUrl: editedImageUrl, // Keep original URL as backup
        method: personaId ? "ai-persona-editing" : "ai-standard-editing",
      });
    } catch (uploadError) {
      console.error("❌ Cloudinary upload failed:", uploadError);
      console.error("❌ Upload error details:", {
        message:
          uploadError instanceof Error ? uploadError.message : uploadError,
        stack: uploadError instanceof Error ? uploadError.stack : undefined,
        editedImageUrl: editedImageUrl?.substring(0, 100) + "...",
      });

      // Return the direct image URL if Cloudinary fails
      return NextResponse.json({
        success: true,
        imageUrl: editedImageUrl,
        method: personaId ? "ai-persona-editing" : "ai-standard-editing",
        warning: "Image uploaded directly (Cloudinary upload failed)",
      });
    }
  } catch (err: unknown) {
    console.error("❌ Error in /api/edit-thumbnail:", err);

    const error = err as Error;

    // Handle Flux API specific errors
    if (err instanceof FluxAPIError) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
          method: "ai-editing-error",
        },
        { status: err.status || 500 }
      );
    }

    // Handle general errors
    return NextResponse.json(
      {
        success: false,
        error: error.message || "Failed to edit thumbnail",
        method: "ai-editing-error",
      },
      { status: 500 }
    );
  }
});

// Smart prompt enhancement for better editing results
function enhanceEditingPrompt(originalPrompt: string): string {
  const basePrompt = originalPrompt.trim();

  // Use a simple, direct approach similar to recreate API
  const enhancedPrompt = `${basePrompt}. IMPORTANT: Only change what was specifically requested. Keep the same background, text, layout, and overall composition. Preserve all existing elements exactly as shown unless specifically mentioned for modification. When changing an object's style or appearance, modify it rather than removing it completely. Maintain the original thumbnail's quality and visual appeal.`;

  return enhancedPrompt;
}
