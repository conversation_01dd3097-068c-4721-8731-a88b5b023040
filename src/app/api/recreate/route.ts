import { NextResponse } from "next/server";
import { create<PERSON><PERSON>API, FluxAPIError } from "@/src/lib/flux";
import cloudinary from "@/src/lib/cloudinary";
import { withAuth, AuthenticatedRequest } from "@/src/lib/api-auth-wrapper";
import { createRecreatedThumbnail } from "@/src/actions/recreatedThumbnailAction";
import { getPersona } from "@/src/actions/personaActions";

export const dynamic = "force-dynamic";
export const runtime = "nodejs";
export const maxDuration = 600; // 10 minutes for Pro Finetuned generation (with polling)

export const POST = withAuth(async (req: AuthenticatedRequest) => {
  try {
    // User is automatically authenticated via withAuth wrapper
    const { userId } = req.user;

    const { imageUrl, prompt, personaId, sourceType } = (await req.json()) as {
      imageUrl: string;
      prompt?: string;
      personaId?: string;
      sourceType: "youtube_url" | "upload_file";
    };

    if (!imageUrl || typeof imageUrl !== "string") {
      return NextResponse.json(
        { success: false, error: "Invalid or missing image URL" },
        { status: 400 }
      );
    }

    if (!sourceType || !["youtube_url", "upload_file"].includes(sourceType)) {
      return NextResponse.json(
        { success: false, error: "Invalid or missing source type" },
        { status: 400 }
      );
    }

    // Validate prompt is required
    if (!prompt || !prompt.trim()) {
      return NextResponse.json(
        { success: false, error: "Prompt is required for recreation" },
        { status: 400 }
      );
    }

    // Convert image URL to base64 for Flux API
    let imageBase64: string;

    try {
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.statusText}`);
      }

      const buffer = await response.arrayBuffer();
      imageBase64 = Buffer.from(buffer).toString("base64");
    } catch (error) {
      console.error("Error fetching image:", error);
      return NextResponse.json(
        { success: false, error: "Failed to process the input image" },
        { status: 400 }
      );
    }

    // Get persona data if persona is selected
    let personaData = null;
    if (personaId) {
      const personaResult = await getPersona(personaId, userId);
      if (!personaResult.success || !personaResult.persona) {
        return NextResponse.json(
          { success: false, error: "Selected persona not found" },
          { status: 400 }
        );
      }
      personaData = personaResult.persona;

      // Validate persona is ready for use
      if (personaData.status !== "ready" || !personaData.finetuneId) {
        return NextResponse.json(
          { success: false, error: "Selected persona is not ready for use" },
          { status: 400 }
        );
      }
    }

    // Create the enhanced prompt for image editing/redesigning
    const basePrompt = prompt.trim();
    let generatedImageUrl: string | undefined;

    if (personaData) {
      // Use Flux Pro Finetuned endpoint with persona
      const enhancedPrompt = `${personaData.triggerWord} ${basePrompt}. IMPORTANT: Only change what was specifically requested. Keep the same background, text, layout, and overall composition. Only replace the person's face while maintaining their exact skin tone, lighting, and pose. Preserve all text exactly as shown.`;

      console.log("Using persona-based generation:", personaData.name);

      // Use Flux Pro Finetuned API for persona-based recreation
      const response = await fetch(
        "https://api.us1.bfl.ai/v1/flux-pro-finetuned",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "x-key": process.env.BFL_API_KEY!,
          },
          body: JSON.stringify({
            finetune_id: personaData.finetuneId,
            prompt: enhancedPrompt,
            image_prompt: imageBase64, // Base64 encoded image (no data URI prefix)
            finetune_strength: 0.8, // Moderate persona influence to preserve original better
            steps: 30, // Fewer steps for more conservative changes
            guidance: 2.0, // Lower guidance for more natural results
            width: 1280, // YouTube thumbnail width (1280 = 40 × 32)
            height: 704, // YouTube thumbnail height (704 = 22 × 32, closest to 720)
            output_format: "jpeg",
            safety_tolerance: 2,
            prompt_upsampling: false,
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Flux Pro API Error:", errorData);
        throw new FluxAPIError(
          `Failed to generate with persona: ${response.status} ${
            response.statusText
          }. Details: ${JSON.stringify(errorData)}`,
          response.status,
          errorData
        );
      }

      const data = await response.json();
      const { polling_url: pollingUrl } = data;

      // Poll for completion - Pro Finetuned is async like Ultra
      const maxWaitTime = 10 * 60 * 1000; // 10 minutes for Pro Finetuned
      const pollInterval = 3000; // 3 seconds
      const startTime = Date.now();

      console.log("Starting polling for Pro Finetuned generation...");

      while (Date.now() - startTime < maxWaitTime) {
        const pollResponse = await fetch(pollingUrl, {
          headers: { "x-key": process.env.BFL_API_KEY! },
        });

        if (!pollResponse.ok) {
          throw new FluxAPIError(`Polling failed: ${pollResponse.statusText}`);
        }

        const result = await pollResponse.json();
        const elapsedTime = Math.round((Date.now() - startTime) / 1000);
        console.log(`Polling status after ${elapsedTime}s:`, result.status);

        if (result.status === "Ready" && result.result?.sample) {
          generatedImageUrl = result.result.sample;
          console.log(
            `✅ Pro Finetuned generation completed in ${elapsedTime}s`
          );
          break;
        } else if (result.status === "Error") {
          throw new FluxAPIError(
            `Generation failed: ${result.details || "Unknown error"}`
          );
        } else if (result.status === "Request Moderated") {
          throw new FluxAPIError(
            "Request was moderated. Please try rephrasing your prompt.",
            400
          );
        } else if (result.status === "Content Moderated") {
          throw new FluxAPIError(
            "Generated content was moderated. Please try a different prompt.",
            400
          );
        }

        // Wait before next poll
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      }

      if (!generatedImageUrl) {
        const elapsedTime = Math.round((Date.now() - startTime) / 1000);
        throw new FluxAPIError(
          `Pro Finetuned generation timed out after ${elapsedTime}s. This model typically takes 2-10 minutes.`,
          408
        );
      }
    } else {
      // Use standard image generation for non-persona recreation
      const enhancedPrompt = `Redesign and improve this YouTube thumbnail with a fresh, modern look.  based on this prompt: ${basePrompt}

REDESIGN APPROACH:
- ANALYZE the current thumbnail's strengths and weaknesses
- PRESERVE the core subject matter and message
- ENHANCE visual impact through modern design principles
- IMPROVE color schemes, lighting, and composition
- MODERNIZE typography and visual elements
- MAINTAIN the 16:9 aspect ratio (1280x720 optimal)

REDESIGN SPECIFICATIONS:
- Style: Contemporary, high-impact visual design
- Colors: Bold, contrasting palette that works on mobile
- Typography: Clean, readable fonts with strong hierarchy
- Composition: Dynamic layout with clear focal points
- Lighting: Professional, dramatic lighting effects
- Quality: Ultra-high definition, crisp details
- Mood: Engaging, exciting, and click-worthy

KEY IMPROVEMENTS TO MAKE:
- Enhance visual hierarchy and readability
- Improve color contrast and saturation
- Add modern design elements and effects
- Optimize for thumbnail viewing size
- Create stronger emotional impact
- Ensure mobile-friendly visibility

Execute this redesign with the expertise of a top-tier graphic designer who understands both classic design principles and modern YouTube trends.`;

      // Initialize Flux API
      const fluxAPI = createFluxAPI();

      // Generate the redesigned image
      generatedImageUrl = await fluxAPI.generateImage({
        prompt: enhancedPrompt,
        input_image: `data:image/jpeg;base64,${imageBase64}`,
        aspect_ratio: "16:9",
        output_format: "jpeg",
        safety_tolerance: 2,
        prompt_upsampling: false,
      });
    }

    if (!generatedImageUrl) {
      throw new FluxAPIError("Failed to generate image", 500);
    }

    console.log("Image generation completed, downloading image...");

    // Download the generated image as base64
    let editedImageBase64: string;
    if (personaData) {
      // For persona-based generation, download directly from URL
      const response = await fetch(generatedImageUrl);
      if (!response.ok) {
        throw new FluxAPIError(
          `Failed to download image: ${response.status} ${response.statusText}`,
          response.status
        );
      }
      const arrayBuffer = await response.arrayBuffer();
      editedImageBase64 = Buffer.from(arrayBuffer).toString("base64");
    } else {
      // For regular generation, use Flux API helper
      const fluxAPI = createFluxAPI();
      editedImageBase64 = await fluxAPI.downloadImageAsBase64(
        generatedImageUrl
      );
    }

    const dataUri = `data:image/jpeg;base64,${editedImageBase64}`;

    const uploadResult = await cloudinary.uploader.upload(dataUri, {
      folder: "thumbnails",
      public_id: `${
        personaData ? "persona_recreated" : "recreated"
      }_thumb_${Date.now()}`,
      width: 1280,
      height: 720, // Standard YouTube thumbnail size (Cloudinary will resize from 704 to 720)
      crop: "fill",
      quality: "auto:best",
      format: "jpg",
      flags: "progressive",
    });

    // Save to database
    const recreatedThumbnail = await createRecreatedThumbnail({
      userId,
      imageUrl: uploadResult.secure_url,
      sourceType,
      prompt: prompt,
      personaUsed: !!personaData,
    });

    return NextResponse.json({
      success: true,
      imageUrl: uploadResult.secure_url,
      thumbnailId: recreatedThumbnail.id,
    });
  } catch (err) {
    console.error("Error in /api/recreate:", err);

    // Handle specific Flux API errors
    if (err instanceof FluxAPIError) {
      if (err.status === 400) {
        return NextResponse.json(
          {
            success: false,
            error: "Invalid request. Please check your image and prompt.",
          },
          { status: 400 }
        );
      }

      if (err.status === 402) {
        return NextResponse.json(
          {
            success: false,
            error: "Insufficient credits. Please check your account balance.",
          },
          { status: 402 }
        );
      }

      if (err.status === 429) {
        return NextResponse.json(
          {
            success: false,
            error: "Rate limit exceeded. Please try again later.",
          },
          { status: 429 }
        );
      }
    }

    return NextResponse.json(
      {
        success: false,
        error:
          typeof err === "object" &&
          err !== null &&
          "message" in err &&
          typeof (err as { message?: string }).message === "string"
            ? (err as { message: string }).message
            : "Internal Server Error",
      },
      { status: 500 }
    );
  }
});
