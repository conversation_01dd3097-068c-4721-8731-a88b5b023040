"use client";

import { Bad<PERSON> } from "@/src/components/ui/badge";
import { <PERSON><PERSON> } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import {
  Sparkles,
  Target,
  Users,
  TrendingUp,
  Zap,
  Heart,
  ArrowRight,
} from "lucide-react";
import Link from "next/link";

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-8 py-16">
              <Badge
                variant="secondary"
                className="px-4 py-2 text-sm font-medium"
              >
                <Heart className="w-4 h-4 mr-2" />
                About Clickworthy.ai
              </Badge>

              <div className="space-y-4 max-w-4xl mx-auto">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
                  Empowering{" "}
                  <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    Creators
                  </span>{" "}
                  Worldwide
                </h1>
                <p className="text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  We're on a mission to democratize high-performing YouTube
                  content creation through the power of artificial intelligence.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h2 className="text-3xl sm:text-4xl font-bold">
                    Our Mission
                  </h2>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    Every creator deserves the tools to succeed. We believe that
                    great content shouldn't be held back by poor presentation.
                    That's why we've built the most advanced AI-powered toolkit
                    for YouTube optimization.
                  </p>
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    From eye-catching thumbnails to compelling titles, our
                    platform helps creators of all sizes boost their
                    click-through rates and grow their audiences faster than
                    ever before.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <Card className="p-6 text-center">
                  <CardContent className="p-0 space-y-3">
                    <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                      <Users className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">1K+</div>
                      <div className="text-sm text-muted-foreground">
                        Creators Served
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="p-6 text-center">
                  <CardContent className="p-0 space-y-3">
                    <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                      <Sparkles className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">10K+</div>
                      <div className="text-sm text-muted-foreground">
                        Thumbnails Generated
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="p-6 text-center">
                  <CardContent className="p-0 space-y-3">
                    <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                      <TrendingUp className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">100%</div>
                      <div className="text-sm text-muted-foreground">
                        Avg CTR Increase
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="p-6 text-center">
                  <CardContent className="p-0 space-y-3">
                    <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                      <Zap className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">5 Sec</div>
                      <div className="text-sm text-muted-foreground">
                        Avg Generation Time
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-4 mb-12">
              <h2 className="text-3xl sm:text-4xl font-bold">Our Values</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                The principles that guide everything we do
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <Card className="p-8 text-center">
                <CardContent className="p-0 space-y-4">
                  <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                    <Target className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold">Creator-First</h3>
                  <p className="text-muted-foreground">
                    Every feature we build is designed with creators' needs at
                    the center. Your success is our success.
                  </p>
                </CardContent>
              </Card>

              <Card className="p-8 text-center">
                <CardContent className="p-0 space-y-4">
                  <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                    <Zap className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold">Innovation</h3>
                  <p className="text-muted-foreground">
                    We're constantly pushing the boundaries of what's possible
                    with AI to give you the competitive edge.
                  </p>
                </CardContent>
              </Card>

              <Card className="p-8 text-center">
                <CardContent className="p-0 space-y-4">
                  <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto">
                    <Heart className="w-8 h-8 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold">Accessibility</h3>
                  <p className="text-muted-foreground">
                    Professional-grade tools shouldn't be limited to big
                    budgets. We make powerful AI accessible to everyone.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl sm:text-4xl font-bold">
                  Ready to Transform Your Content?
                </h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                  Join thousands of creators who are already using
                  Clickworthy.ai to boost their YouTube performance.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth">
                  <Button size="lg" className="text-lg px-8 py-3 h-auto">
                    Start Creating Free
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button
                    variant="outline"
                    size="lg"
                    className="text-lg px-8 py-3 h-auto"
                  >
                    Get in Touch
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
