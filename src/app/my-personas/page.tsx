"use client";

import { usePersonaStore } from "@/src/store/personaStore";
import { PersonaList } from "@/src/components/personas/PersonaList";
import { CreatePersonaModal } from "@/src/components/personas/CreatePersonaModal";
import { PersonaGuidelinesModal } from "@/src/components/personas/PersonaGuidelinesModal";
import { Button } from "@/src/components/ui/button";
import { Card, CardContent } from "@/src/components/ui/card";
import { Plus, User, RefreshCw } from "lucide-react";
import { useState, useEffect } from "react";
import { toast } from "sonner";

export default function PersonasPage() {
  const [isGuidelinesModalOpen, setIsGuidelinesModalOpen] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const { personas, isLoading, error, setPersonas, setLoading, setError } =
    usePersonaStore();

  // Load personas on mount
  useEffect(() => {
    loadPersonas();
  }, []);

  const loadPersonas = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/personas");
      const data = await response.json();

      if (data.success) {
        setPersonas(
          data.personas.map((p: any) => ({
            ...p,
            createdAt: new Date(p.createdAt),
            updatedAt: new Date(p.updatedAt),
          }))
        );
      } else {
        setError(data.error || "Failed to load personas");
      }
    } catch (err) {
      setError("Failed to load personas");
      console.error("Error loading personas:", err);
    } finally {
      setLoading(false);
    }
  };

  const syncPersonas = async () => {
    setIsSyncing(true);
    try {
      const response = await fetch("/api/personas/sync", {
        method: "POST",
      });
      const data = await response.json();

      if (data.success) {
        toast.success(`Synced ${data.syncedCount} personas`);
        loadPersonas(); // Refresh the list
      } else {
        toast.error(data.error || "Failed to sync personas");
      }
    } catch (err) {
      toast.error("Failed to sync personas");
      console.error("Error syncing personas:", err);
    } finally {
      setIsSyncing(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
              My Personas
            </h1>
            <p className="text-muted-foreground mt-2">
              Create AI personas of yourself for consistent thumbnail generation
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={syncPersonas}
              variant="outline"
              size="lg"
              disabled={isSyncing}
              className="gap-2"
            >
              <RefreshCw
                className={`w-4 h-4 ${isSyncing ? "animate-spin" : ""}`}
              />
              {isSyncing ? "Syncing..." : "Sync Status"}
            </Button>
            <Button
              onClick={() => setIsGuidelinesModalOpen(true)}
              size="lg"
              className="gap-2"
            >
              <Plus className="w-4 h-4" />
              Create Persona
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="mb-6 border-destructive">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-destructive">
                <span className="font-medium">Error:</span>
                <span>{error}</span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={loadPersonas}
                className="mt-2"
              >
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Personas List */}
        {personas.length > 0 && (
          <PersonaList
            personas={personas}
            isLoading={isLoading}
            onRefresh={loadPersonas}
          />
        )}

        {/* Guidelines Modal */}
        <PersonaGuidelinesModal
          isOpen={isGuidelinesModalOpen}
          onClose={() => setIsGuidelinesModalOpen(false)}
          onContinue={() => {
            setIsGuidelinesModalOpen(false);
            setIsCreateModalOpen(true);
          }}
        />

        {/* Create Persona Modal */}
        <CreatePersonaModal
          isOpen={isCreateModalOpen}
          onClose={() => setIsCreateModalOpen(false)}
          onSuccess={loadPersonas}
        />
      </div>
    </div>
  );
}
