"use client";

import React, { useEffect } from "react";
import { useDownloadThumbnailStore } from "@/src/store/useDownloadThumbnailStore";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import {
  HeroSection,
  InputSection,
  FeaturesSection,
  ThumbnailResults,
  useDownloadHandler,
} from "@/src/components/thumbnail-download";
import { useConditionalRouting } from "@/src/hooks/useConditionalRouting";

export default function DownloadThumbnailPage() {
  const { url, videoId, thumbnailUrl, error, setUrl, generate, reset } =
    useDownloadThumbnailStore();

  const { handleDownload, isDownloading, isDownloaded } =
    useDownloadHandler(videoId);

  const { redirectToAuthenticatedVersion } = useConditionalRouting();

  useEffect(() => {
    redirectToAuthenticatedVersion("/thumbnail-download");
  }, [redirectToAuthenticatedVersion]);

  return (
    <div className="mt-20 bg-background">
      <main className="container mx-auto py-8 px-4 space-y-8">
        <HeroSection />

        <InputSection
          url={url}
          setUrl={setUrl}
          generate={generate}
          reset={reset}
        />

        <FeaturesSection thumbnailUrl={thumbnailUrl} error={error} />

        {/* Error State */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription className="text-center">{error}</AlertDescription>
          </Alert>
        )}

        <ThumbnailResults
          thumbnailUrl={thumbnailUrl}
          videoId={videoId}
          handleDownload={handleDownload}
          isDownloading={isDownloading}
          isDownloaded={isDownloaded}
        />
      </main>
    </div>
  );
}
