"use client";

import React from "react";
import { useDownloadThumbnailStore } from "@/src/store/useDownloadThumbnailStore";
import { Alert, AlertDescription } from "@/src/components/ui/alert";
import {
  HeroSection,
  InputSection,
  FeaturesSection,
  ThumbnailResults,
  useDownloadHandler,
} from "@/src/components/thumbnail-download";

export default function AuthenticatedThumbnailDownloaderPage() {
  const { url, videoId, thumbnailUrl, error, setUrl, generate, reset } =
    useDownloadThumbnailStore();

  const { handleDownload, isDownloading, isDownloaded } =
    useDownloadHandler(videoId);

  return (
    <div className="h-full bg-background">
      <main className="px-4 py-8 space-y-8">
        {/* Hero Section - simplified for authenticated version */}
        <div className="text-center space-y-4 max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold">YouTube Thumbnail Downloader</h1>
          <p className="text-muted-foreground text-lg">
            Extract high-quality thumbnails from any YouTube video
          </p>
        </div>

        <InputSection
          url={url}
          setUrl={setUrl}
          generate={generate}
          reset={reset}
        />

        <FeaturesSection thumbnailUrl={thumbnailUrl} error={error} />

        {/* Error State */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription className="text-center">{error}</AlertDescription>
          </Alert>
        )}

        <ThumbnailResults
          thumbnailUrl={thumbnailUrl}
          videoId={videoId}
          handleDownload={handleDownload}
          isDownloading={isDownloading}
          isDownloaded={isDownloaded}
        />
      </main>
    </div>
  );
}
