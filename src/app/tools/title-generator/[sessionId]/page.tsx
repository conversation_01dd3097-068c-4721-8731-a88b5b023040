"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { ChatInterface } from "@/src/components/tools/title-generator/ChatInterface";
import { useTitleGeneratorStore } from "@/src/store/titleGeneratorStore";
import { MessageType } from "@/src/store/titleGeneratorStore";
import { Loader2 } from "lucide-react";

export default function TitleGeneratorSessionPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;

  const {
    setCurrentSession,
    setCurrentSessionId,
    loadSessionMessages,
    currentSessionId,
    isHydrated,
  } = useTitleGeneratorStore();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadSession = async () => {
      if (!sessionId) {
        setError("Invalid session ID");
        return;
      }

      // If we're already on this session, don't interfere
      if (currentSessionId === sessionId) {
        return;
      }

      // Only load from server if we're loading a completely different session
      // and the store has been hydrated
      if (!isHydrated) {
        return; // Wait for hydration
      }

      // Load different session data
      loadSessionData();
    };

    const loadSessionData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/title-sessions/${sessionId}`);
        const data = await response.json();

        if (!data.success) {
          if (response.status === 404) {
            setError("Session not found");
          } else if (response.status === 403) {
            setError("You don't have access to this session");
          } else {
            setError(data.error || "Failed to load session");
          }
          return;
        }

        const { session } = data;

        // Set the current session
        setCurrentSession({
          id: session.id,
          title: session.title,
          firstPrompt: session.firstPrompt,
          createdAt: new Date(session.createdAt),
          updatedAt: new Date(session.updatedAt),
        });

        setCurrentSessionId(sessionId);

        // Convert database messages to store format
        const storeMessages: MessageType[] = session.messages.map(
          (msg: {
            id: string;
            type: "user" | "assistant";
            content: string;
            titles?: { id: string; text: string; style: string }[];
            status?: "generating" | "success" | "error";
            error?: string;
            createdAt: string;
          }) => ({
            id: msg.id,
            type: msg.type,
            content: msg.content,
            titles: msg.titles || undefined,
            timestamp: new Date(msg.createdAt),
            status: msg.status || undefined,
            error: msg.error || undefined,
          })
        );

        loadSessionMessages(storeMessages);
      } catch (err) {
        console.error("Error loading session:", err);
        setError("Failed to load session");
      } finally {
        setIsLoading(false);
      }
    };

    loadSession();
  }, [
    sessionId,
    currentSessionId,
    setCurrentSession,
    setCurrentSessionId,
    loadSessionMessages,
    isHydrated,
  ]);

  if (isLoading) {
    return (
      <div className="h-full min-h-screen overflow-y-hidden flex items-center justify-center">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="h-20 w-20 animate-spin" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full min-h-screen overflow-y-hidden flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-destructive text-lg font-medium">{error}</div>
          <button
            onClick={() => router.push("/tools/title-generator")}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Start New Session
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full min-h-screen overflow-y-hidden">
      <ChatInterface />
    </div>
  );
}
