"use client";

import { Badge } from "@/src/components/ui/badge";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>le,
} from "@/src/components/ui/card";
import { Mail, MessageCircle, Clock } from "lucide-react";

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <main className="pt-24 pb-16">
        {/* Hero Section */}
        <section className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5" />

          <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center space-y-6 py-8">
              <Badge
                variant="secondary"
                className="px-4 py-2 text-sm font-medium"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Get in Touch
              </Badge>

              <div className="space-y-4 max-w-4xl mx-auto">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight">
                  We&apos;d Love to{" "}
                  <span className="bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    Hear
                  </span>{" "}
                  From You
                </h1>
                <p className="text-xl sm:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                  Have questions, feedback, or need support? Our team is here to
                  help you succeed with your YouTube content creation.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Content */}
        <section className="py-8">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
            <div className="grid md:grid-cols-2 gap-12">
              {/* Contact Info */}
              <div className="space-y-8">
                <div>
                  <h2 className="text-2xl font-bold mb-6">
                    Contact Information
                  </h2>
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
                        <Mail className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Email</h3>
                        <p className="text-muted-foreground">
                          <EMAIL>
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          We&apos;ll get back to you within 24-48 hours
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <div className="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center flex-shrink-0">
                        <Clock className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Response Time</h3>
                        <p className="text-muted-foreground">
                          Within 24-48 hours
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          Monday - Friday, 9 AM - 6 PM PST
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* How to Reach Us */}
              <div>
                <Card className="border-2 border-border bg-card shadow-lg">
                  <CardHeader>
                    <CardTitle className="text-lg">How to Reach Us</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium text-sm">General Inquiries</h4>
                      <p className="text-sm text-muted-foreground">
                        Send us an <NAME_EMAIL> for any
                        questions or feedback
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Technical Support</h4>
                      <p className="text-sm text-muted-foreground">
                        Having issues? Email us with details about your problem
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">Feature Requests</h4>
                      <p className="text-sm text-muted-foreground">
                        Share your ideas for new features via email
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm">
                        Business Partnerships
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        Interested in partnering with us? Let&apos;s talk!
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
