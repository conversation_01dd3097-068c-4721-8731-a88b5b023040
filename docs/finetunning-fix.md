You're not limited to Python! The BFL API accepts standard HTTP requests, so you can absolutely implement it with Next.js. Based on the documentation and your specific issues, let me help you with a proper implementation and address the problems you're experiencing.
Why You're Getting Wrong People
The issue you're experiencing (getting white people instead of your Black male subject) is likely due to:

Incorrect mode selection - Use "character" mode, not "general"
Poor trigger word usage - The default "TOK" is too generic
Insufficient finetune_strength - Default 1.0 may be too weak
Auto-captioning conflicts - May be overriding your intended subject

Next.js ImplementationBFL Finetuning Next.js ImplementationCode // utils/bfl-api.js
export class BFLFinetune {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = '<https://api.us1.bfl.ai/v1>';
  }

  async requestFinetuning(zipFile, options = {}) {
    const {
      finetune_comment,
      triggerCritical Settings for Your Use Case
Based on your problem, here are the key settings to fix the character preservation issue:

1. Use Character Mode
javascriptmode: 'character' // NOT 'general'
2. Better Trigger Word
javascripttrigger_word: 'MYCHAR' // More specific than 'TOK'
3. Higher Finetune Strength
javascriptfinetune_strength: 1.5 // Higher than default 1.0
4. Enhanced Prompting
Always include character description:
javascriptconst enhancedPrompt = `MYCHAR, a professional portrait of MYCHAR, ${prompt}. MYCHAR is a young Black male.`;
Step-by-Step Process

Prepare your ZIP file with 5-15 high-quality images of the person
Use the component above with the correct settings
Monitor training - it should take 150 iterations (lower as you requested)
Generate with proper prompts that include character description
Adjust finetune_strength if needed (try 1.2-2.0 range)

API Route (Optional)
If you want to handle this server-side:
javascript// pages/api/finetune.js
```js 
import { BFLFinetune } from '../../utils/bfl-api';

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { apiKey, ...options } = req.body;
  const bfl = new BFLFinetune(apiKey);

  try {
    const result = await bfl.requestFinetuning(req.body.zipFile, options);
    res.status(200).json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
}
The key issue you were facing was likely using the wrong mode and insufficient finetune strength. The character mode with proper prompting should preserve the racial characteristics much better than general mode.RetryClaude can make mistakes. Please double-check responses.


```js
// utils/bfl-api.js
export class BFLFinetune {
  constructor(apiKey) {
    this.apiKey = apiKey;
    this.baseUrl = 'https://api.us1.bfl.ai/v1';
  }

  async requestFinetuning(zipFile, options = {}) {
    const {
      finetune_comment,
      trigger_word = 'MYCHAR', // Better than default TOK
      mode = 'character', // IMPORTANT: Use character mode
      iterations = 150, // Start lower as you requested
      learning_rate = 0.00001,
      captioning = true,
      priority = 'quality',
      finetune_type = 'full',
      lora_rank = 32
    } = options;

    try {
      // Convert file to base64
      const arrayBuffer = await zipFile.arrayBuffer();
      const base64String = Buffer.from(arrayBuffer).toString('base64');

      const response = await fetch(`${this.baseUrl}/finetune`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Key': this.apiKey,
        },
        body: JSON.stringify({
          finetune_comment,
          trigger_word,
          file_data: base64String,
          iterations,
          mode,
          learning_rate,
          captioning,
          priority,
          lora_rank,
          finetune_type,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Finetune request failed: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      throw new Error(`Finetune request failed: ${error.message}`);
    }
  }

  async checkProgress(finetuneId) {
    try {
      const response = await fetch(`${this.baseUrl}/get_result?id=${finetuneId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Key': this.apiKey,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Progress check failed: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      throw new Error(`Progress check failed: ${error.message}`);
    }
  }

  async runInference(finetuneId, prompt, options = {}) {
    const {
      finetune_strength = 1.5, // Higher than default for better character preservation
      endpoint = 'flux-pro-1.1-ultra-finetuned',
      width = 1024,
      height = 1024,
      steps = 28,
      guidance = 3.5,
      seed = null,
      safety_tolerance = 2
    } = options;

    try {
      const response = await fetch(`${this.baseUrl}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Key': this.apiKey,
        },
        body: JSON.stringify({
          finetune_id: finetuneId,
          finetune_strength,
          prompt,
          width,
          height,
          steps,
          guidance,
          seed,
          safety_tolerance,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Inference request failed: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      throw new Error(`Inference request failed: ${error.message}`);
    }
  }

  async getInferenceResult(inferenceId) {
    try {
      const response = await fetch(`${this.baseUrl}/get_result?id=${inferenceId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Key': this.apiKey,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Get result failed: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      throw new Error(`Get result failed: ${error.message}`);
    }
  }

  async listFinetunes() {
    try {
      const response = await fetch(`${this.baseUrl}/my_finetunes`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Key': this.apiKey,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`List finetunes failed: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      throw new Error(`List finetunes failed: ${error.message}`);
    }
  }
}

// hooks/useFinetune.js
import { useState, useCallback } from 'react';
import { BFLFinetune } from '../utils/bfl-api';

export function useFinetune(apiKey) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [progress, setProgress] = useState(null);

  const bfl = new BFLFinetune(apiKey);

  const startTraining = useCallback(async (zipFile, comment) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await bfl.requestFinetuning(zipFile, {
        finetune_comment: comment,
        trigger_word: 'MYCHAR', // Use consistent trigger word
        mode: 'character',
        iterations: 150, // Lower iterations as requested
        finetune_strength: 1.5, // Higher strength for better character preservation
        captioning: true,
        priority: 'quality'
      });
      
      setLoading(false);
      return result;
    } catch (err) {
      setError(err.message);
      setLoading(false);
      throw err;
    }
  }, [bfl]);

  const checkTrainingProgress = useCallback(async (finetuneId) => {
    try {
      const result = await bfl.checkProgress(finetuneId);
      setProgress(result);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, [bfl]);

  const generateImage = useCallback(async (finetuneId, prompt) => {
    setLoading(true);
    setError(null);
    
    try {
      // Enhanced prompt for better character preservation
      const enhancedPrompt = `MYCHAR, a professional portrait of MYCHAR, ${prompt}. MYCHAR is a young Black male.`;
      
      const result = await bfl.runInference(finetuneId, enhancedPrompt, {
        finetune_strength: 1.5, // Higher strength
        steps: 28,
        guidance: 3.5,
        safety_tolerance: 2
      });
      
      setLoading(false);
      return result;
    } catch (err) {
      setError(err.message);
      setLoading(false);
      throw err;
    }
  }, [bfl]);

  const getResult = useCallback(async (inferenceId) => {
    try {
      return await bfl.getInferenceResult(inferenceId);
    } catch (err) {
      setError(err.message);
      throw err;
    }
  }, [bfl]);

  return {
    startTraining,
    checkTrainingProgress,
    generateImage,
    getResult,
    loading,
    error,
    progress
  };
}

// components/FinetuneUpload.js
import { useState } from 'react';
import { useFinetune } from '../hooks/useFinetune';

export default function FinetuneUpload() {
  const [apiKey, setApiKey] = useState('');
  const [zipFile, setZipFile] = useState(null);
  const [comment, setComment] = useState('');
  const [finetuneId, setFinetuneId] = useState('');
  const [results, setResults] = useState([]);
  
  const { startTraining, checkTrainingProgress, generateImage, getResult, loading, error, progress } = useFinetune(apiKey);

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file && file.type === 'application/zip') {
      setZipFile(file);
    } else {
      alert('Please select a ZIP file');
    }
  };

  const handleStartTraining = async () => {
    if (!zipFile || !comment || !apiKey) {
      alert('Please fill all fields');
      return;
    }

    try {
      const result = await startTraining(zipFile, comment);
      setFinetuneId(result.id);
      alert(`Training started! ID: ${result.id}`);
    } catch (err) {
      console.error('Training failed:', err);
    }
  };

  const handleCheckProgress = async () => {
    if (!finetuneId) return;
    
    try {
      const result = await checkTrainingProgress(finetuneId);
      console.log('Progress:', result);
    } catch (err) {
      console.error('Progress check failed:', err);
    }
  };

  const handleGenerateImage = async () => {
    if (!finetuneId) return;
    
    try {
      const inferenceResult = await generateImage(finetuneId, 'professional headshot, studio lighting');
      
      // Poll for result
      const pollResult = async (inferenceId) => {
        const result = await getResult(inferenceId);
        if (result.status === 'Ready') {
          setResults(prev => [...prev, result.result]);
        } else if (result.status === 'Pending') {
          setTimeout(() => pollResult(inferenceId), 2000);
        }
      };
      
      pollResult(inferenceResult.id);
    } catch (err) {
      console.error('Image generation failed:', err);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">BFL Finetuning</h1>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">API Key</label>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Your BFL API key"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">ZIP File</label>
          <input
            type="file"
            accept=".zip"
            onChange={handleFileUpload}
            className="w-full p-2 border border-gray-300 rounded"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Comment</label>
          <input
            type="text"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded"
            placeholder="Description of your finetune"
          />
        </div>

        <button
          onClick={handleStartTraining}
          disabled={loading}
          className="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600 disabled:bg-gray-400"
        >
          {loading ? 'Starting Training...' : 'Start Training'}
        </button>

        {finetuneId && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <p className="font-medium">Finetune ID: {finetuneId}</p>
            <button
              onClick={handleCheckProgress}
              className="mt-2 bg-green-500 text-white p-2 rounded hover:bg-green-600"
            >
              Check Progress
            </button>
            {progress && <p className="mt-2">Status: {progress.status}</p>}
          </div>
        )}

        {progress?.status === 'Ready' && (
          <button
            onClick={handleGenerateImage}
            disabled={loading}
            className="w-full bg-purple-500 text-white p-2 rounded hover:bg-purple-600 disabled:bg-gray-400"
          >
            {loading ? 'Generating...' : 'Generate Portrait'}
          </button>
        )}

        {error && (
          <div className="mt-4 p-4 bg-red-100 border border-red-400 rounded">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {results.length > 0 && (
          <div className="mt-4">
            <h3 className="font-medium mb-2">Generated Images:</h3>
            <div className="grid grid-cols-2 gap-4">
              {results.map((result, index) => (
                <img
                  key={index}
                  src={result.sample}
                  alt={`Generated ${index + 1}`}
                  className="w-full h-64 object-cover rounded"
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
```