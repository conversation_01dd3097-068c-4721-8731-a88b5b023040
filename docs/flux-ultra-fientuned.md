# Flux Pro 1.1 Ultra Fine-tuned Optimization Guide

## Recommended Settings for Thumbnail Generation

Based on your use case (YouTube thumbnails with person selection), here are the optimal settings:

### Core Configuration

```javascript
const optimalSettings = {
  finetune_strength: 1.0,        // Reduced from default 1.2 for better text handling
  aspect_ratio: "16:9",          // YouTube standard
  safety_tolerance: 2,           // Moderate safety
  output_format: "jpeg",         // Better for thumbnails
  prompt_upsampling: false,      // Keep prompt control precise
  image_prompt_strength: 0.15,   // Slightly higher for persona integration
  seed: null                     // Allow randomness unless specific reproduction needed
}
```

### Key Optimizations for Your Use Case

1. **Finetune Strength**: Reduced to `1.0` (from default 1.2)
   - Better balance between persona likeness and text rendering
   - Prevents over-fitting that can interfere with text placement

2. **Prompt Structure**: Simplified for Ultra model
   - Keep prompts concise and focused
   - Let the model handle complexity rather than over-prompting

3. **Text Handling**: Ultra model performs better with direct text instructions
   - Specify text placement early in prompt
   - Use clear, simple language for text elements

## Updated Code Implementation

### Enhanced Prompt for Fine-tuned Generation

```javascript
// Replace your current fine-tuned prompt logic with:
if (useFinetunedModel && personaData) {
  // Optimized prompt structure for Ultra model
  enhancedPrompt = `${personaData.triggerWord}, YouTube thumbnail, ${prompt}, bold text, 16:9, photorealistic, dramatic lighting`;
  
  const fluxFinetuneAPI = createFluxFinetuneAPI();
  imageUrl = await fluxFinetuneAPI.testGenerateWithModel(
    personaData.finetuneId!,
    enhancedPrompt,
    personaData.triggerWord,
    {
      aspect_ratio: "16:9",
      finetune_strength: 1.0,           // Optimized strength
      safety_tolerance: 2,
      output_format: "jpeg",
      prompt_upsampling: false,         // Keep control
      image_prompt_strength: 0.15,     // If using image prompts
    }
  );
}
```

### Text Rendering Best Practices

For better text placement and quality with your fine-tuned model:

1. **Early Text Specification**: Mention text requirements early in prompt
2. **Simplified Language**: Avoid overly complex descriptions
3. **Strategic Positioning**: Use spatial terms like "top left", "center", "bottom"

### Example Optimized Prompts

**With Person (Fine-tuned):**

```
[TRIGGER_WORD], YouTube thumbnail, [USER_PROMPT], large bold text "[TEXT_CONTENT]", dramatic lighting, 16:9
```

**Without Person (Regular):**

```
YouTube thumbnail, [USER_PROMPT], bold readable text, professional lighting, vibrant colors, 16:9
```

## Performance Expectations

- **Generation Time**: 8-10 seconds with Ultra model
- **Quality**: 4MP resolution (2048x1152 for 16:9)
- **Text Quality**: Significantly improved with proper finetune_strength
- **Persona Likeness**: Balanced with lower strength setting

## Troubleshooting Text Issues

If text quality is still problematic:

1. **Reduce finetune_strength to 0.8**: More baseline model influence
2. **Enable prompt_upsampling**: Let model enhance text descriptions
3. **Simplify prompts**: Remove complex descriptive language
4. **Position text strategically**: Avoid crowded areas of the composition

## Code Integration Notes

- The Ultra model handles complexity better with simpler prompts
- Text rendering improves when finetune_strength is not maxed out
- Consider A/B testing finetune_strength values between 0.8-1.2 for your specific personas
- Monitor generation times - Ultra should consistently deliver in 8-10 seconds

## Final Recommendation

Use these settings as your baseline, then fine-tune the `finetune_strength` parameter based on your specific trained personas. Some may work better at 0.9, others at 1.1 - the key is finding the sweet spot where persona likeness and text quality are both optimal.
