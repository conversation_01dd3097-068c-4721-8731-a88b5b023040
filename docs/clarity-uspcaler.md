````markdown
# About
## Generate Image

### 1. Calling the API

#### Install the client
The client provides a convenient way to interact with the model API.

```bash
npm install --save @fal-ai/client
````

#### Migrate to @fal-ai/client

The @fal-ai/serverless-client package has been deprecated in favor of @fal-ai/client. Please check the migration guide for more information.

#### Setup your API Key

Set FAL\_KEY as an environment variable in your runtime.

```bash
export FAL_KEY="YOUR_API_KEY"
```

#### Submit a request

The client API handles the API submit protocol. It will handle the request status updates and return the result when the request is completed.

```js
import { fal } from "@fal-ai/client";

const result = await fal.subscribe("fal-ai/clarity-upscaler", {
  input: {
    image_url: "https://storage.googleapis.com/falserverless/gallery/NOCA_Mick-Thompson.resized.resized.jpg"
  },
  logs: true,
  onQueueUpdate: (update) => {
    if (update.status === "IN_PROGRESS") {
      update.logs.map((log) => log.message).forEach(console.log);
    }
  },
});
console.log(result.data);
console.log(result.requestId);
```

### 2. Authentication

The API uses an API Key for authentication. It is recommended you set the FAL\_KEY environment variable in your runtime when possible.

#### API Key

In case your app is running in an environment where you cannot set environment variables, you can set the API Key manually as a client configuration.

```js
import { fal } from "@fal-ai/client";

fal.config({
  credentials: "YOUR_FAL_KEY"
});
```

**Protect your API Key**
When running code on the client-side (e.g. in a browser, mobile app or GUI applications), make sure to not expose your FAL\_KEY. Instead, use a server-side proxy to make requests to the API. For more information, check out our server-side integration guide.

### 3. Queue

#### Long-running requests

For long-running requests, such as training jobs or models with slower inference times, it is recommended to check the Queue status and rely on Webhooks instead of blocking while waiting for the result.

#### Submit a request

The client API provides a convenient way to submit requests to the model.

```js
import { fal } from "@fal-ai/client";

const { request_id } = await fal.queue.submit("fal-ai/clarity-upscaler", {
  input: {
    image_url: "https://storage.googleapis.com/falserverless/gallery/NOCA_Mick-Thompson.resized.resized.jpg"
  },
  webhookUrl: "https://optional.webhook.url/for/results",
});
```

#### Fetch request status

You can fetch the status of a request to check if it is completed or still in progress.

```js
import { fal } from "@fal-ai/client";

const status = await fal.queue.status("fal-ai/clarity-upscaler", {
  requestId: "764cabcf-b745-4b3e-ae38-1200304cf45b",
  logs: true,
});
```

#### Get the result

Once the request is completed, you can fetch the result. See the Output Schema for the expected result format.

```js
import { fal } from "@fal-ai/client";

const result = await fal.queue.result("fal-ai/clarity-upscaler", {
  requestId: "764cabcf-b745-4b3e-ae38-1200304cf45b"
});
console.log(result.data);
console.log(result.requestId);
```

### 4. Files

Some attributes in the API accept file URLs as input. Whenever that's the case you can pass your own URL or a Base64 data URI.

#### Data URI (base64)

You can pass a Base64 data URI as a file input. The API will handle the file decoding for you. Keep in mind that for large files, this alternative although convenient can impact the request performance.

#### Hosted files (URL)

You can also pass your own URLs as long as they are publicly accessible. Be aware that some hosts might block cross-site requests, rate-limit, or consider the request as a bot.

#### Uploading files

We provide a convenient file storage that allows you to upload files and use them in your requests. You can upload files using the client API and use the returned URL in your requests.

```js
import { fal } from "@fal-ai/client";

const file = new File(["Hello, World!"], "hello.txt", { type: "text/plain" });
const url = await fal.storage.upload(file);
```

**Auto uploads**
The client will auto-upload the file for you if you pass a binary object (e.g. File, Data).

Read more about file handling in our file upload guide.

### 5. Schema

#### Input

| Name                    | Type    | Description                                                                                 |
| ----------------------- | ------- | ------------------------------------------------------------------------------------------- |
| `image_url`             | string  | The URL of the image to upscale.                                                            |
| `prompt`                | string  | The prompt to use for generating the image. Default: `"masterpiece, best quality, highres"` |
| `upscale_factor`        | float   | The upscale factor. Default: `2`                                                            |
| `negative_prompt`       | string  | Details to avoid in the image. Default: `"(worst quality, low quality, normal quality:2)"`  |
| `creativity`            | float   | Controls how much the model deviates from the prompt. Default: `0.35`                       |
| `resemblance`           | float   | How close the result is to the original image. Default: `0.6`                               |
| `guidance_scale`        | float   | CFG scale to guide prompt accuracy. Default: `4`                                            |
| `num_inference_steps`   | integer | Number of inference steps. Default: `18`                                                    |
| `seed`                  | integer | Used for deterministic results.                                                             |
| `enable_safety_checker` | boolean | If false, disables safety checker. Default: `true`                                          |

```json
{
  "image_url": "https://storage.googleapis.com/falserverless/gallery/NOCA_Mick-Thompson.resized.resized.jpg",
  "prompt": "masterpiece, best quality, highres",
  "upscale_factor": 2,
  "negative_prompt": "(worst quality, low quality, normal quality:2)",
  "creativity": 0.35,
  "resemblance": 0.6,
  "guidance_scale": 4,
  "num_inference_steps": 18,
  "enable_safety_checker": true
}
```

#### Output

| Field     | Type    | Description                          |
| --------- | ------- | ------------------------------------ |
| `image`   | Image   | The URL of the generated image.      |
| `seed`    | integer | The seed used to generate the image. |
| `timings` | Timings | Workflow step timings.               |

```json
{
  "image": {
    "url": "",
    "content_type": "image/png",
    "file_name": "z9RV14K95DvU.png",
    "file_size": 4404019,
    "width": 1024,
    "height": 1024
  }
}
```

### Other types

#### Image

| Field          | Type    | Description                                 |
| -------------- | ------- | ------------------------------------------- |
| `url`          | string  | Downloadable URL of the file.               |
| `content_type` | string  | Mime type of the file.                      |
| `file_name`    | string  | File name (auto-generated if not provided). |
| `file_size`    | integer | File size in bytes.                         |
| `width`        | integer | Image width in pixels.                      |
| `height`       | integer | Image height in pixels.                     |

```
```
