import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      { protocol: "https", hostname: "res.cloudinary.com" },
      { protocol: "https", hostname: "img.youtube.com" },
      { protocol: "https", hostname: "i.ytimg.com" },
      { protocol: "https", hostname: "i9.ytimg.com" },
      { protocol: "https", hostname: "lh3.googleusercontent.com" },
      { protocol: "https", hostname: "lh4.googleusercontent.com" },
      { protocol: "https", hostname: "lh5.googleusercontent.com" },
      { protocol: "https", hostname: "lh6.googleusercontent.com" },
      { protocol: "https", hostname: "delivery-us1.bfl.ai" },
      { protocol: "https", hostname: "delivery-eu1.bfl.ai" },
      { protocol: "https", hostname: "v3.fal.media" },
    ],
  },
  // External packages for server components
  serverExternalPackages: [
    "sharp",
    "@remove-background-ai/rembg.js",
    "form-data",
    "tmp-promise",
  ],

  // Webpack configuration for WASM support
  webpack: (config, { isServer }) => {
    // Add support for WASM files
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
      layers: true,
    };

    // Handle .wasm files
    config.module.rules.push({
      test: /\.wasm$/,
      type: "webassembly/async",
    });

    // Completely exclude problematic WASM packages from webpack bundling
    config.externals = config.externals || [];

    // Fallback for Node.js modules in client-side
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        module: false,
      };
    }

    return config;
  },
};

export default nextConfig;
