{"name": "thumbnail-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:drop": "drizzle-kit drop"}, "dependencies": {"@fal-ai/client": "^1.6.1", "@nanostores/react": "github:ai/react", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@remove-background-ai/rembg.js": "^1.2.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.9", "@upstash/redis": "^1.35.0", "@vercel/analytics": "^1.5.0", "ai": "^4.3.16", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "date-fns": "^4.1.0", "dotenv": "^16.6.0", "form-data": "^4.0.3", "framer-motion": "^11.18.2", "fs": "^0.0.1-security", "jszip": "^3.10.1", "konva": "^9.3.20", "lucide-react": "^0.511.0", "next": "15.3.3", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^5.1.0", "postgres": "^3.4.7", "posthog-js": "^1.258.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-konva": "^19.0.6", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.2", "remark-gfm": "^4.0.1", "remeda": "^2.23.1", "server-only": "^0.0.1", "sharp": "^0.34.3", "shiki": "^1.29.2", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "tmp-promise": "^3.0.3", "tw-animate-css": "^1.3.3", "uuid": "^11.1.0", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@biomejs/biome": "2.0.5", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jszip": "^3.4.0", "@types/node": "^20", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "eslint": "^9", "eslint-config-next": "15.3.3", "pg": "^8.16.3", "tailwindcss": "^4.1.8", "tsx": "^4.20.3", "typescript": "^5", "ultracite": "5.0.19"}}